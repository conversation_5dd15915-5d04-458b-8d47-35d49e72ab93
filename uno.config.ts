import { defineConfig } from '@unocss/vite';
import transformerDirectives from '@unocss/transformer-directives';
import transformerVariantGroup from '@unocss/transformer-variant-group';
import presetUno from '@unocss/preset-uno';
import { Theme } from '@unocss/preset-uno';
import { presetYcAdmin } from '@yc/uno-preset';
import { themeVars } from './src/theme/vars';

export default defineConfig<Theme>({
  content: {
    pipeline: {
      exclude: ['node_modules', 'dist']
    }
  },
  theme: {
    ...themeVars,
    fontSize: {
      'icon-xs': '0.875rem',
      'icon-small': '1rem',
      icon: '1.125rem',
      'icon-large': '1.5rem',
      'icon-xl': '2rem'
    },
  },
  shortcuts: {
    'card-wrapper': 'rd-8px shadow-sm',
    'text-form-label': 'text-14px font-400 text-base_text',
    'yc-table__header': 'flex items-center gap-12px',
    'yc-tag': 'box-border rounded-4px py-2px text-center px-12px'
  },
  transformers: [transformerDirectives(), transformerVariantGroup()],
  presets: [presetUno({ dark: 'class' }), presetYcAdmin()]
});
