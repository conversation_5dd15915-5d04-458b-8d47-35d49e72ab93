import ky from 'ky'
import path from 'node:path'
import * as fs from 'node:fs'
import { prompt } from 'enquirer'

interface IResponse {
  data: any
  requestId: string
  rspCd: string
  rspInf: string
  success: boolean
}

// 将kebab-case转换为camelCase
const kebabToCamel = (str: string) => {
  return str.replace(/-([a-z])/g, (g: string) => g[1].toUpperCase())
}

// 首字母大写
const toUpperCaseFirst = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

// 生成函数名
const generateFunctionName = (fileName: string, api: any) => {
  // 处理URL路径
  const urlParts = api.url
    .split('/')
    .filter(Boolean)
    .map((part: string) => part.replace(/\{|\}/g, ''))
  // 移除manage-api前缀
  const parts = urlParts.slice(1)

  // 转换为驼峰
  const methodName = parts
    .map((part: string) =>
      toUpperCaseFirst(
        part
          .split('-')
          .map((i) => toUpperCaseFirst(i))
          .join('')
      )
    )
    .join('')

  // 生成函数名
  const baseName = `fetch${toUpperCaseFirst(fileName)}${methodName}`

  return baseName
}

// 生成api模板
const generateApiTemplate = async (fileName: string, apis: any) => {
  const tooltip = `/**
 * 【重要】该文件由脚本生成，请勿手动修改，重新生成请运行 npm run api 命令
 */
`
  return (
    tooltip +
    `
import { request } from '../request'
${apis
  .map((api: any) => {
    const functionName = generateFunctionName(fileName, api)
    const isGet = api.methodType.toLowerCase() === 'get'
    const hasParams = api.url.includes('{')

    // 处理GET请求参数
    let paramsType = 'unknown'
    if (isGet && hasParams) {
      const paramName = api.url.match(/\{([^}]+)\}/)[1]
      paramsType = `{ ${paramName}: string | number }`
    }

    // 处理请求参数
    const requestConfig = isGet ? `params: params` : `data: params`

    const url = isGet ? api.url.replace(/\/\{.*\}/g, '') : api.url

    return `
/**
 * ${api.name}
 */
export function ${functionName}(params: ${paramsType}) {
  return request<any>({
    url: '${url.split('/').slice(2).join('/')}',
    method: '${api.methodType.toLowerCase()}',
    ${requestConfig}${api.name.includes('下载') ? ',\n    responseType: "arraybuffer"' : ''}
  })
}`
  })
  .join('\n')}
  `
  )
}

// 生成api文件
export async function buildIgApi() {
  // 1. 获取项目列表
  const projectListGroupRes = (await ky.post('http://172.16.0.188:7001/ig/project/queryGroupList', { json: {} }).json()) as IResponse
  if (projectListGroupRes.rspCd !== '00000') {
    throw new Error('获取项目列表失败')
  }
  const projectGroupSelectRes: { projectGroupId: string } = await prompt({
    type: 'autocomplete',
    name: 'projectGroupId',
    message: '请输入关键字搜索并选择需要生成API的项目(按回车键确认)',
    initial: 0,
    limit: 5,
    scroll: true,
    choices: projectListGroupRes.data.map((item: any) => ({
      name: item.name,
      value: item.id
    }))
  })
  if (!projectGroupSelectRes.projectGroupId) {
    throw new Error('请选择一个项目')
  }
  // 2. 获取项目详细列表
  const webProjectListRes = (await ky
    .post('http://172.16.0.188:7001/ig/project/queryWebProjectList', { json: { projectGroupId: projectGroupSelectRes.projectGroupId } })
    .json()) as IResponse
  if (webProjectListRes.rspCd !== '00000') {
    throw new Error('获取项目详细列表失败')
  }
  const webProjectSelectRes: { projectId: string } = await prompt({
    type: 'autocomplete',
    name: 'projectId',
    message: '请选择需要生成API的接口(按回车键确认)',
    choices: webProjectListRes.data.map((item: any) => ({
      name: item.name,
      value: item.id
    }))
  })
  if (!webProjectSelectRes.projectId) {
    throw new Error('请选择一个项目详细列表')
  }
  const projectItem = webProjectListRes.data.find((item: any) => item.id === webProjectSelectRes.projectId)
  if (!projectItem) {
    throw new Error('项目不存在')
  }
  // 3. 获取接口列表
  const webInfSearchRes = (await ky
    .post('http://172.16.0.188:7001/ig/webinf/searchInfs', { json: { projectId: projectItem.id } })
    .json()) as IResponse
  if (webInfSearchRes.rspCd !== '00000') {
    throw new Error('获取接口列表失败')
  }
  const cwd = process.cwd()
  const serviceApiDir = path.join(cwd, 'src', 'service', 'api')
  const indexFilePath = path.join(serviceApiDir, 'index.ts')
  const indexFileContent = fs.readFileSync(indexFilePath, 'utf8')
  const fileName = kebabToCamel(projectItem.infPrefix)
  const template = await generateApiTemplate(fileName, webInfSearchRes.data)
  const apiFileName = `${fileName}.ts`
  const filePath = path.join(serviceApiDir, apiFileName)
  fs.writeFile(filePath, template, 'utf8', (err) => {
    if (!err) {
      const exportStatement = `export * from './${fileName}'`
      const newIndexFileContent = indexFileContent.includes(exportStatement) ? indexFileContent : indexFileContent + `\n${exportStatement}`
      fs.writeFile(indexFilePath, newIndexFileContent, 'utf8', (err) => {
        if (!err) {
          console.log(`✅ ${fileName} API 生成成功`)
        }
      })
    }
  })
}
