{"name": "@yc/scripts", "version": "1.0.0", "bin": {"yc": "./bin.ts"}, "exports": {".": "./src/index.ts"}, "description": "", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"bumpp": "9.4.1", "c12": "1.10.0", "cac": "6.7.14", "consola": "3.2.3", "enquirer": "2.4.1", "execa": "9.2.0", "kolorist": "1.8.0", "ky": "^1.3.0", "npm-check-updates": "16.14.20", "prettier": "^3.3.2", "rimraf": "5.0.7"}}