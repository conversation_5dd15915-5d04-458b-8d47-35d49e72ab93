import dayjs, { Dayjs } from 'dayjs'

/**
 * 转换金额为千分位格式
 */
export const $formatThousands = (num: number): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 生成随机颜色
 */
export const $randomColor = (): string => {
  return `#${Math.floor(Math.random() * 16777215).toString(16)}`
}

/**
 * 获取文件扩展名
 */
export const $getFileExt = (filename: string): string => {
  return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2)
}

/**
 * 是否为空, 空字符串、null、undefined、空数组、空对象都返回true
 */
export const $isEmpty = (value: any): boolean => {
  if (value === null || value === undefined || value === '') return true
  if (Array.isArray(value) && value.length === 0) return true
  if (typeof value === 'object' && Object.keys(value).length === 0) return true
  return false
}

/**
 * 格式化时间
 */
export const $formatTime = (time: string | number | Date | Dayjs | null | undefined, format: string): string => {
  return dayjs(time).format(format)
}

/**
 * 数值保留两位小数
 */
export const $toFixed = (num: number, digits = 2): number => {
  return Number(num.toFixed(digits))
}
