import type { ObjectDirective } from 'vue'
import { usePermission } from '@/hooks/usePermission'

/**
 * 点击外部区域指令
 */
export interface ClickOutsideOptions {
  handler: () => void
  exclude?: string[]
}

export const vClickOutside: ObjectDirective<HTMLElement, ClickOutsideOptions> = {
  mounted(el, binding) {
    const { handler, exclude = [] } = binding.value

    el._clickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      const isExcluded = exclude.some((selector) => target.matches(selector) || target.closest(selector))

      if (!el.contains(target) && !isExcluded) {
        handler()
      }
    }

    document.addEventListener('click', el._clickOutside)
  },

  beforeUnmount(el) {
    document.removeEventListener('click', el._clickOutside)
    delete el._clickOutside
  }
}

/**
 * 自动聚焦指令
 */
export const vFocus: ObjectDirective<HTMLInputElement> = {
  mounted(el) {
    el.focus()
  }
}

/**
 * 限制输入指令
 */
export const vNumberOnly: ObjectDirective<HTMLInputElement, boolean | { decimal?: boolean }> = {
  mounted(el, binding) {
    const allowDecimal = typeof binding.value === 'object' ? binding.value.decimal : binding.value

    el.addEventListener('keypress', (e) => {
      const charCode = e.which ? e.which : e.keyCode
      if (charCode > 31 && (charCode < 48 || charCode > 57) && (charCode !== 46 || !allowDecimal || el.value.includes('.'))) {
        e.preventDefault()
      }
    })
  }
}

/**
 * 权限控制指令
 */
export const vPermission: ObjectDirective = {
  mounted(el: HTMLButtonElement, binding) {
    if (binding.value === undefined) return
    const code = binding.value
    const { hasPermission } = usePermission()
    // 将获取到的值传到权限判断函数中，如果函数返回false，删除dom
    if (!hasPermission(code)) {
      el.remove()
    }
  }
}
