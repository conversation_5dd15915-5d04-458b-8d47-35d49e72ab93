import type { Component } from 'naive-ui'
import type { FormItemRule } from 'naive-ui/es/form/src/interface'

export interface FormItem {
  /** 字段类型 */
  type:
    | 'input'
    | 'select'
    | 'date'
    | 'uploadFile'
    | 'custom'
    | 'inputgroup'
    | 'NRadioGroup'
    | 'inputgroupNumber'
    | 'RegionSelect'
    | 'BankSelectTree'
    | 'EntSelect'
  /** 字段标识 */
  field: string
  /**
   * 字段名称
   */
  fieldLabel?: string
  /** 标签文本 */
  label: string
  /** 栅格宽度 (基于24栅格) */
  span?: number
  max?: number
  /** 禁用状态 */
  disabled?: boolean
  /** 提示 */
  tooltip?: string
  RuleType?: string
  /** 必填标记 */
  required?: boolean
  /** 自定义组件 */
  component?: Component
  /** 组件特有属性 */
  props?: Record<string, any>
  /** 验证规则 */
  rules?: FormItemRule[]
  /** 选项数据（select类型使用） */
  options?: Array<{ label: string; value: any }>
  /** 后缀内容 */
  suffix?: string
  /** 自定义插槽名称 */
  slotName?: string
}

export interface FormGroup {
  /** 分组标题 */
  title: string
  /** 表单项配置 */
  items: FormItem[]
}

export type FormConfig = FormGroup[]
