import type { ComputedRef, Ref } from 'vue';
import type { PaginationProps } from 'naive-ui';
import type { TableUtil } from '@/typings/table';

/** vue 的defineExpose导出的类型 */
declare namespace Expose {
  interface BetterScroll {
    instance: import('@better-scroll/core').BScrollInstance;
  }

  interface YCModal {
    /** 打开弹窗 */
    open(param?: any): void;

    /** 关闭弹窗 */
    close(): void;
  }

  interface YCDrawer {
    /** 打开抽屉 */
    open(param?: any): void;

    /** 关闭抽屉 */
    close(): void;
  }

  interface YCTable {
    /** 刷新数据 */
    refreshData(isReset?: boolean): void;
    /** 定制列 */
    Dzcolum(isReset?: boolean): void;

    /** 是否有数据 */
    hasData: ComputedRef<boolean>;

    /** 当前页数据 */
    currentPageData: Ref<TableUtil.BaseData[]>;
    alltotalData: Ref<TableUtil.BaseData[]>;

    /** pagination */
    pagination: Ref<PaginationProps>;
  }
}
