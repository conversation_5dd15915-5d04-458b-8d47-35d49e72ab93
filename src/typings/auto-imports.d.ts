/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const $formatThousands: typeof import('../globals/methods')['$formatThousands']
  const $formatTime: typeof import('../globals/methods')['$formatTime']
  const $getFileExt: typeof import('../globals/methods')['$getFileExt']
  const $isEmpty: typeof import('../globals/methods')['$isEmpty']
  const $randomColor: typeof import('../globals/methods')['$randomColor']
  const $toFixed: typeof import('../globals/methods')['$toFixed']
  const AcceptanceReceiptAdd: typeof import('../service/api/inventory')['AcceptanceReceiptAdd']
  const AcceptanceReceiptQuery: typeof import('../service/api/inventory')['AcceptanceReceiptQuery']
  const AcceptanceReceiptUpdate: typeof import('../service/api/inventory')['AcceptanceReceiptUpdate']
  const EffectScope: typeof import('vue')['EffectScope']
  const PurchasePlanhandle: typeof import('../service/api/contractReserve')['PurchasePlanhandle']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const addCompanyTask: typeof import('../service/api/contractReserve')['addCompanyTask']
  const addContractManufacture: typeof import('../service/api/contractReserve')['addContractManufacture']
  const addContractMaterial: typeof import('../service/api/contractReserve')['addContractMaterial']
  const addContractMatter: typeof import('../service/api/contractReserve')['addContractMatter']
  const addContractProject: typeof import('../service/api/contractReserve')['addContractProject']
  const addOrUpdate: typeof import('../service/api/inventory')['addOrUpdate']
  const addOrgAttachment: typeof import('../service/api/organiza')['addOrgAttachment']
  const addOrganizeTask: typeof import('../service/api/contractReserve')['addOrganizeTask']
  const addPayment: typeof import('../service/api/contractReserve')['addPayment']
  const addPerformancePlan: typeof import('../service/api/contractReserve')['addPerformancePlan']
  const addProduct: typeof import('../service/api/filedataBase')['addProduct']
  const addProductTask: typeof import('../service/api/contractReserve')['addProductTask']
  const addProject: typeof import('../service/api/contractReserve')['addProject']
  const addProjectPlanner: typeof import('../service/api/contractReserve')['addProjectPlanner']
  const addPurchaseContract: typeof import('../service/api/contractReserve')['addPurchaseContract']
  const addPurchasePlan: typeof import('../service/api/contractReserve')['addPurchasePlan']
  const addReceiptDetailed: typeof import('../service/api/inventory')['addReceiptDetailed']
  const afterSalesArchivesData: typeof import('../service/api/filedataBase')['afterSalesArchivesData']
  const anyNodeList: typeof import('../service/api/contractReserve')['anyNodeList']
  const attachmentquery: typeof import('../service/api/filedataBase')['attachmentquery']
  const backNodeList: typeof import('../service/api/rules')['backNodeList']
  const batchAddAcceptance: typeof import('../service/api/contractReserve')['batchAddAcceptance']
  const checkhandle: typeof import('../service/api/rules')['checkhandle']
  const companyBYName: typeof import('../service/api/contractReserve')['companyBYName']
  const companyCertificatequeryList: typeof import('../service/api/contractReserve')['companyCertificatequeryList']
  const companyhandle: typeof import('../service/api/supplier')['companyhandle']
  const computed: typeof import('vue')['computed']
  const contractManufacturegetProject: typeof import('../service/api/contractReserve')['contractManufacturegetProject']
  const contractManufacturehandle: typeof import('../service/api/contractReserve')['contractManufacturehandle']
  const contractPerformanceData: typeof import('../service/api/filedataBase')['contractPerformanceData']
  const contractProjectgetProject: typeof import('../service/api/contractReserve')['contractProjectgetProject']
  const countDoneEveryMonth: typeof import('../service/api/contractReserve')['countDoneEveryMonth']
  const countDoneThisMonthAndToday: typeof import('../service/api/contractReserve')['countDoneThisMonthAndToday']
  const createApp: typeof import('vue')['createApp']
  const createFactoryNumber: typeof import('../service/api/contractReserve')['createFactoryNumber']
  const createNotificationNumber: typeof import('../service/api/contractReserve')['createNotificationNumber']
  const createPinia: typeof import('pinia')['createPinia']
  const createProductJobCode: typeof import('../service/api/contractReserve')['createProductJobCode']
  const createPurchaseContractCode: typeof import('../service/api/contractReserve')['createPurchaseContractCode']
  const createPurchasePlanCode: typeof import('../service/api/contractReserve')['createPurchasePlanCode']
  const createRecCode: typeof import('../service/api/contractReserve')['createRecCode']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const deleteCompanyCertificateYear: typeof import('../service/api/contractReserve')['deleteCompanyCertificateYear']
  const deleteCompanyTask: typeof import('../service/api/contractReserve')['deleteCompanyTask']
  const deleteCompanyYear: typeof import('../service/api/contractReserve')['deleteCompanyYear']
  const deleteComponentPart: typeof import('../service/api/contractReserve')['deleteComponentPart']
  const deleteContractManufacture: typeof import('../service/api/contractReserve')['deleteContractManufacture']
  const deleteContractMaterial: typeof import('../service/api/contractReserve')['deleteContractMaterial']
  const deleteContractMatter: typeof import('../service/api/contractReserve')['deleteContractMatter']
  const deleteContractProject: typeof import('../service/api/contractReserve')['deleteContractProject']
  const deleteEndProduct: typeof import('../service/api/contractReserve')['deleteEndProduct']
  const deleteLog: typeof import('../service/api/inventory')['deleteLog']
  const deleteOrgCertificateYearList: typeof import('../service/api/contractReserve')['deleteOrgCertificateYearList']
  const deleteOrganizeTask: typeof import('../service/api/contractReserve')['deleteOrganizeTask']
  const deletePayment: typeof import('../service/api/contractReserve')['deletePayment']
  const deletePerformancePlan: typeof import('../service/api/contractReserve')['deletePerformancePlan']
  const deleteProduct: typeof import('../service/api/filedataBase')['deleteProduct']
  const deleteProductTask: typeof import('../service/api/contractReserve')['deleteProductTask']
  const deleteProjectPlanner: typeof import('../service/api/contractReserve')['deleteProjectPlanner']
  const deletePurchaseContract: typeof import('../service/api/contractReserve')['deletePurchaseContract']
  const deletePurchasePlan: typeof import('../service/api/contractReserve')['deletePurchasePlan']
  const deleteReceiptDetailed: typeof import('../service/api/inventory')['deleteReceiptDetailed']
  const donePage: typeof import('../service/api/contractReserve')['donePage']
  const effectScope: typeof import('vue')['effectScope']
  const exportCompany: typeof import('../service/api/contractReserve')['exportCompany']
  const exportConfiggetInfo: typeof import('../service/api/inventory')['exportConfiggetInfo']
  const exportContractManufacture: typeof import('../service/api/contractReserve')['exportContractManufacture']
  const exportLog: typeof import('../service/api/inventory')['exportLog']
  const exportProductTask: typeof import('../service/api/contractReserve')['exportProductTask']
  const exportPurchaseContract: typeof import('../service/api/contractReserve')['exportPurchaseContract']
  const exportPurchasePlan: typeof import('../service/api/contractReserve')['exportPurchasePlan']
  const exportQualityAssurance: typeof import('../service/api/contractReserve')['exportQualityAssurance']
  const fetchAddCheckApply: typeof import('../service/api/rules')['fetchAddCheckApply']
  const fetchAddCompany: typeof import('../service/api/supplier')['fetchAddCompany']
  const fetchAddCompanyCertificate: typeof import('../service/api/supplier')['fetchAddCompanyCertificate']
  const fetchAddCompanyQualificate: typeof import('../service/api/supplier')['fetchAddCompanyQualificate']
  const fetchAddElement: typeof import('../service/api/settings')['fetchAddElement']
  const fetchAddIntegrityCompany: typeof import('../service/api/supplier')['fetchAddIntegrityCompany']
  const fetchAddOrgCertificate: typeof import('../service/api/organiza')['fetchAddOrgCertificate']
  const fetchAddOrgQualificate: typeof import('../service/api/organiza')['fetchAddOrgQualificate']
  const fetchAddOrgUser: typeof import('../service/api/organiza')['fetchAddOrgUser']
  const fetchAddRole: typeof import('../service/api/settings')['fetchAddRole']
  const fetchAdminResetPwd: typeof import('../service/api/auth')['fetchAdminResetPwd']
  const fetchAftermarketAdd: typeof import('../service/api/sale')['fetchAftermarketAdd']
  const fetchAftermarketDelete: typeof import('../service/api/sale')['fetchAftermarketDelete']
  const fetchAftermarketList: typeof import('../service/api/sale')['fetchAftermarketList']
  const fetchAftermarketQuery: typeof import('../service/api/sale')['fetchAftermarketQuery']
  const fetchAftermarketUpdate: typeof import('../service/api/sale')['fetchAftermarketUpdate']
  const fetchApplyContract: typeof import('../service/api/rules')['fetchApplyContract']
  const fetchApplyRelevantFile: typeof import('../service/api/rules')['fetchApplyRelevantFile']
  const fetchAttachmentQuery: typeof import('../service/api/common')['fetchAttachmentQuery']
  const fetchAttachmentUpload: typeof import('../service/api/common')['fetchAttachmentUpload']
  const fetchAuditApplyCondition: typeof import('../service/api/rules')['fetchAuditApplyCondition']
  const fetchAuditApplyHandle: typeof import('../service/api/rules')['fetchAuditApplyHandle']
  const fetchAuditApplyQuality: typeof import('../service/api/rules')['fetchAuditApplyQuality']
  const fetchCardApplyAdd: typeof import('../service/api/rules')['fetchCardApplyAdd']
  const fetchCardApplyAudit: typeof import('../service/api/rules')['fetchCardApplyAudit']
  const fetchCardApplyDelete: typeof import('../service/api/rules')['fetchCardApplyDelete']
  const fetchCardApplyQuery: typeof import('../service/api/rules')['fetchCardApplyQuery']
  const fetchCardApplyUpdate: typeof import('../service/api/rules')['fetchCardApplyUpdate']
  const fetchCheckCardApplyHandle: typeof import('../service/api/rules')['fetchCheckCardApplyHandle']
  const fetchCheckLatestVersion: typeof import('../service/api/rules')['fetchCheckLatestVersion']
  const fetchCompanyApprove: typeof import('../service/api/supplier')['fetchCompanyApprove']
  const fetchCompanyImportExcel: typeof import('../service/api/supplier')['fetchCompanyImportExcel']
  const fetchContractProject: typeof import('../service/api/task')['fetchContractProject']
  const fetchCostAdd: typeof import('../service/api/task')['fetchCostAdd']
  const fetchCostDelete: typeof import('../service/api/task')['fetchCostDelete']
  const fetchCostList: typeof import('../service/api/task')['fetchCostList']
  const fetchCostQuery: typeof import('../service/api/task')['fetchCostQuery']
  const fetchCostUpdate: typeof import('../service/api/task')['fetchCostUpdate']
  const fetchCreateCheckNumber: typeof import('../service/api/rules')['fetchCreateCheckNumber']
  const fetchDeleteCheck: typeof import('../service/api/rules')['fetchDeleteCheck']
  const fetchDeleteCheckApplyById: typeof import('../service/api/rules')['fetchDeleteCheckApplyById']
  const fetchDeleteCompanyById: typeof import('../service/api/supplier')['fetchDeleteCompanyById']
  const fetchDeleteCompanyCertificateById: typeof import('../service/api/supplier')['fetchDeleteCompanyCertificateById']
  const fetchDeleteCompanyQualificateById: typeof import('../service/api/supplier')['fetchDeleteCompanyQualificateById']
  const fetchDeleteCondition: typeof import('../service/api/rules')['fetchDeleteCondition']
  const fetchDeleteElementById: typeof import('../service/api/settings')['fetchDeleteElementById']
  const fetchDeleteInspect: typeof import('../service/api/rules')['fetchDeleteInspect']
  const fetchDeleteOrgCertificateById: typeof import('../service/api/organiza')['fetchDeleteOrgCertificateById']
  const fetchDeleteOrgQualificateById: typeof import('../service/api/organiza')['fetchDeleteOrgQualificateById']
  const fetchDeleteOrgUserById: typeof import('../service/api/organiza')['fetchDeleteOrgUserById']
  const fetchDeleteQuality: typeof import('../service/api/rules')['fetchDeleteQuality']
  const fetchDeleteRoleById: typeof import('../service/api/settings')['fetchDeleteRoleById']
  const fetchDeviationAdd: typeof import('../service/api/task')['fetchDeviationAdd']
  const fetchDeviationDelete: typeof import('../service/api/task')['fetchDeviationDelete']
  const fetchDeviationList: typeof import('../service/api/task')['fetchDeviationList']
  const fetchDeviationQuery: typeof import('../service/api/task')['fetchDeviationQuery']
  const fetchDeviationUpdate: typeof import('../service/api/task')['fetchDeviationUpdate']
  const fetchDictDelete: typeof import('../service/api/settings')['fetchDictDelete']
  const fetchDictDetailByType: typeof import('../service/api/settings')['fetchDictDetailByType']
  const fetchDictQueryList: typeof import('../service/api/common')['fetchDictQueryList']
  const fetchDictUpdate: typeof import('../service/api/settings')['fetchDictUpdate']
  const fetchExportApplyCard: typeof import('../service/api/rules')['fetchExportApplyCard']
  const fetchExportInsideCard: typeof import('../service/api/rules')['fetchExportInsideCard']
  const fetchExportJqCard: typeof import('../service/api/rules')['fetchExportJqCard']
  const fetchExportProductCard: typeof import('../service/api/rules')['fetchExportProductCard']
  const fetchFactoryAdd: typeof import('../service/api/rules')['fetchFactoryAdd']
  const fetchFactoryAudit: typeof import('../service/api/rules')['fetchFactoryAudit']
  const fetchFactoryDelete: typeof import('../service/api/rules')['fetchFactoryDelete']
  const fetchFactoryHandle: typeof import('../service/api/rules')['fetchFactoryHandle']
  const fetchFactoryQuery: typeof import('../service/api/rules')['fetchFactoryQuery']
  const fetchFactoryUpdate: typeof import('../service/api/rules')['fetchFactoryUpdate']
  const fetchFileCentreDownload: typeof import('../service/api/common')['fetchFileCentreDownload']
  const fetchFlowQueryAuditInfo: typeof import('../service/api/common')['fetchFlowQueryAuditInfo']
  const fetchFlowWithdraw: typeof import('../service/api/common')['fetchFlowWithdraw']
  const fetchGetCheckApplyById: typeof import('../service/api/rules')['fetchGetCheckApplyById']
  const fetchGetCheckCardById: typeof import('../service/api/rules')['fetchGetCheckCardById']
  const fetchGetCompanyApproveDetailById: typeof import('../service/api/supplier')['fetchGetCompanyApproveDetailById']
  const fetchGetCompanyById: typeof import('../service/api/supplier')['fetchGetCompanyById']
  const fetchGetCompanyCertificateById: typeof import('../service/api/supplier')['fetchGetCompanyCertificateById']
  const fetchGetCompanyQualificateById: typeof import('../service/api/supplier')['fetchGetCompanyQualificateById']
  const fetchGetElementById: typeof import('../service/api/settings')['fetchGetElementById']
  const fetchGetOrgCertificateById: typeof import('../service/api/organiza')['fetchGetOrgCertificateById']
  const fetchGetOrgQualificateById: typeof import('../service/api/organiza')['fetchGetOrgQualificateById']
  const fetchGetOrgUserById: typeof import('../service/api/organiza')['fetchGetOrgUserById']
  const fetchGetOrganiza: typeof import('../service/api/organiza')['fetchGetOrganiza']
  const fetchGetRoleById: typeof import('../service/api/settings')['fetchGetRoleById']
  const fetchGetRsaPublicKey: typeof import('../service/api/auth')['fetchGetRsaPublicKey']
  const fetchGetUserRoutes: typeof import('../service/api/route')['fetchGetUserRoutes']
  const fetchInsertCheck: typeof import('../service/api/rules')['fetchInsertCheck']
  const fetchInsertProduct: typeof import('../service/api/rules')['fetchInsertProduct']
  const fetchIsRouteExist: typeof import('../service/api/route')['fetchIsRouteExist']
  const fetchLibraryAdd: typeof import('../service/api/inventory')['fetchLibraryAdd']
  const fetchLibraryDelete: typeof import('../service/api/inventory')['fetchLibraryDelete']
  const fetchLibraryHandle: typeof import('../service/api/inventory')['fetchLibraryHandle']
  const fetchLibraryList: typeof import('../service/api/inventory')['fetchLibraryList']
  const fetchLibraryQuery: typeof import('../service/api/inventory')['fetchLibraryQuery']
  const fetchLibraryUpdate: typeof import('../service/api/inventory')['fetchLibraryUpdate']
  const fetchLogin: typeof import('../service/api/auth')['fetchLogin']
  const fetchLogout: typeof import('../service/api/auth')['fetchLogout']
  const fetchManageApiAttachmentAdd: typeof import('../service/api/manageApi')['fetchManageApiAttachmentAdd']
  const fetchManageApiAttachmentDelete: typeof import('../service/api/manageApi')['fetchManageApiAttachmentDelete']
  const fetchManageApiAttachmentDeleteById: typeof import('../service/api/manageApi')['fetchManageApiAttachmentDeleteById']
  const fetchManageApiAttachmentDownloadFileByUrl: typeof import('../service/api/manageApi')['fetchManageApiAttachmentDownloadFileByUrl']
  const fetchManageApiAttachmentQuery: typeof import('../service/api/manageApi')['fetchManageApiAttachmentQuery']
  const fetchManageApiAttachmentUpdate: typeof import('../service/api/manageApi')['fetchManageApiAttachmentUpdate']
  const fetchManageApiAttachmentUpload: typeof import('../service/api/manageApi')['fetchManageApiAttachmentUpload']
  const fetchManageApiAttachmentUploadByteArrays: typeof import('../service/api/manageApi')['fetchManageApiAttachmentUploadByteArrays']
  const fetchManageApiAuditAuditCredit: typeof import('../service/api/manageApi')['fetchManageApiAuditAuditCredit']
  const fetchManageApiAuditAuditGuarantee: typeof import('../service/api/manageApi')['fetchManageApiAuditAuditGuarantee']
  const fetchManageApiAuditBatchAuditGuarantee: typeof import('../service/api/manageApi')['fetchManageApiAuditBatchAuditGuarantee']
  const fetchManageApiAuditKill: typeof import('../service/api/manageApi')['fetchManageApiAuditKill']
  const fetchManageApiAuditRecall: typeof import('../service/api/manageApi')['fetchManageApiAuditRecall']
  const fetchManageApiAuditSubmit: typeof import('../service/api/manageApi')['fetchManageApiAuditSubmit']
  const fetchManageApiAuditSubmitCredit: typeof import('../service/api/manageApi')['fetchManageApiAuditSubmitCredit']
  const fetchManageApiAuditSubmitGuarantee: typeof import('../service/api/manageApi')['fetchManageApiAuditSubmitGuarantee']
  const fetchManageApiAuthLoginManage: typeof import('../service/api/manageApi')['fetchManageApiAuthLoginManage']
  const fetchManageApiAuthLogout: typeof import('../service/api/manageApi')['fetchManageApiAuthLogout']
  const fetchManageApiAuthSendCode: typeof import('../service/api/manageApi')['fetchManageApiAuthSendCode']
  const fetchManageApiConfigAdd: typeof import('../service/api/manageApi')['fetchManageApiConfigAdd']
  const fetchManageApiConfigEdit: typeof import('../service/api/manageApi')['fetchManageApiConfigEdit']
  const fetchManageApiConfigFusing: typeof import('../service/api/manageApi')['fetchManageApiConfigFusing']
  const fetchManageApiConfigQueryPage: typeof import('../service/api/manageApi')['fetchManageApiConfigQueryPage']
  const fetchManageApiConfigQueryYearCount: typeof import('../service/api/manageApi')['fetchManageApiConfigQueryYearCount']
  const fetchManageApiDashboardQueryDashboard: typeof import('../service/api/manageApi')['fetchManageApiDashboardQueryDashboard']
  const fetchManageApiDashboardQueryReportList: typeof import('../service/api/manageApi')['fetchManageApiDashboardQueryReportList']
  const fetchManageApiDictDelete: typeof import('../service/api/manageApi')['fetchManageApiDictDelete']
  const fetchManageApiDictDetail: typeof import('../service/api/manageApi')['fetchManageApiDictDetail']
  const fetchManageApiDictQueryPage: typeof import('../service/api/manageApi')['fetchManageApiDictQueryPage']
  const fetchManageApiDictUpdate: typeof import('../service/api/manageApi')['fetchManageApiDictUpdate']
  const fetchManageApiElementAddElement: typeof import('../service/api/manageApi')['fetchManageApiElementAddElement']
  const fetchManageApiElementDeleteElement: typeof import('../service/api/manageApi')['fetchManageApiElementDeleteElement']
  const fetchManageApiElementGetElement: typeof import('../service/api/manageApi')['fetchManageApiElementGetElement']
  const fetchManageApiElementQueryApiList: typeof import('../service/api/manageApi')['fetchManageApiElementQueryApiList']
  const fetchManageApiElementQueryElementList: typeof import('../service/api/manageApi')['fetchManageApiElementQueryElementList']
  const fetchManageApiElementSortElement: typeof import('../service/api/manageApi')['fetchManageApiElementSortElement']
  const fetchManageApiElementUpdateElement: typeof import('../service/api/manageApi')['fetchManageApiElementUpdateElement']
  const fetchManageApiExportConfigAddOrUpdate: typeof import('../service/api/manageApi')['fetchManageApiExportConfigAddOrUpdate']
  const fetchManageApiExportConfigGetInfo: typeof import('../service/api/manageApi')['fetchManageApiExportConfigGetInfo']
  const fetchManageApiExportLogDeleteLog: typeof import('../service/api/manageApi')['fetchManageApiExportLogDeleteLog']
  const fetchManageApiExportLogQueryPage: typeof import('../service/api/manageApi')['fetchManageApiExportLogQueryPage']
  const fetchManageApiFlowDefinitionActiveDefinitionId: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionActiveDefinitionId']
  const fetchManageApiFlowDefinitionAdd: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionAdd']
  const fetchManageApiFlowDefinitionBatchRemoveIds: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionBatchRemoveIds']
  const fetchManageApiFlowDefinitionCopyDefId: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionCopyDefId']
  const fetchManageApiFlowDefinitionEdit: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionEdit']
  const fetchManageApiFlowDefinitionFlowChartInstanceId: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionFlowChartInstanceId']
  const fetchManageApiFlowDefinitionFlowChartNoColorInstanceId: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionFlowChartNoColorInstanceId']
  const fetchManageApiFlowDefinitionGetInfoId: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionGetInfoId']
  const fetchManageApiFlowDefinitionImportDefinition: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionImportDefinition']
  const fetchManageApiFlowDefinitionList: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionList']
  const fetchManageApiFlowDefinitionPublishId: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionPublishId']
  const fetchManageApiFlowDefinitionRemoveId: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionRemoveId']
  const fetchManageApiFlowDefinitionUnActiveDefinitionId: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionUnActiveDefinitionId']
  const fetchManageApiFlowDefinitionUnPublishId: typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionUnPublishId']
  const fetchManageApiFlowExecuteActiveInstanceId: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteActiveInstanceId']
  const fetchManageApiFlowExecuteAnyNodeListInstanceId: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteAnyNodeListInstanceId']
  const fetchManageApiFlowExecuteCopyPage: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteCopyPage']
  const fetchManageApiFlowExecuteCountDoneEveryMonth: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteCountDoneEveryMonth']
  const fetchManageApiFlowExecuteCountDoneThisMonthAndToday: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteCountDoneThisMonthAndToday']
  const fetchManageApiFlowExecuteDoneListInstanceId: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteDoneListInstanceId']
  const fetchManageApiFlowExecuteDonePage: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteDonePage']
  const fetchManageApiFlowExecuteGetBackNodeListTaskId: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteGetBackNodeListTaskId']
  const fetchManageApiFlowExecuteGetTaskByIdTaskId: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteGetTaskByIdTaskId']
  const fetchManageApiFlowExecuteIdReverseDisplayNameIds: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteIdReverseDisplayNameIds']
  const fetchManageApiFlowExecuteInteractiveType: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteInteractiveType']
  const fetchManageApiFlowExecuteToDoPage: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteToDoPage']
  const fetchManageApiFlowExecuteUnActiveInstanceId: typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteUnActiveInstanceId']
  const fetchManageApiLoanRateAdd: typeof import('../service/api/manageApi')['fetchManageApiLoanRateAdd']
  const fetchManageApiLoanRateDelete: typeof import('../service/api/manageApi')['fetchManageApiLoanRateDelete']
  const fetchManageApiLoanRateEdit: typeof import('../service/api/manageApi')['fetchManageApiLoanRateEdit']
  const fetchManageApiLoanRateQueryPage: typeof import('../service/api/manageApi')['fetchManageApiLoanRateQueryPage']
  const fetchManageApiLoanStatisticQueryListByBank: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryListByBank']
  const fetchManageApiLoanStatisticQueryListByRegion: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryListByRegion']
  const fetchManageApiLoanStatisticQueryListByRegionExport: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryListByRegionExport']
  const fetchManageApiLoanStatisticQueryPageByBank: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByBank']
  const fetchManageApiLoanStatisticQueryPageByBankExport: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByBankExport']
  const fetchManageApiLoanStatisticQueryPageByBankRegion: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByBankRegion']
  const fetchManageApiLoanStatisticQueryPageByBankRegionExport: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByBankRegionExport']
  const fetchManageApiLoanStatisticQueryPageByEnt: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByEnt']
  const fetchManageApiLoanStatisticQueryPageByEntExport: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByEntExport']
  const fetchManageApiLoanStatisticQueryPageByRegion: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegion']
  const fetchManageApiLoanStatisticQueryPageByRegionExport: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegionExport']
  const fetchManageApiLoanStatisticQueryPageByRegionOrg: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegionOrg']
  const fetchManageApiLoanStatisticQueryPageByRegionOrgEnt: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegionOrgEnt']
  const fetchManageApiLoanStatisticQueryPageByRegionOrgEntExport: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegionOrgEntExport']
  const fetchManageApiLoanStatisticQueryPageByRegionOrgExport: typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegionOrgExport']
  const fetchManageApiLoanTaskManageAdd: typeof import('../service/api/manageApi')['fetchManageApiLoanTaskManageAdd']
  const fetchManageApiLoanTaskManageDelete: typeof import('../service/api/manageApi')['fetchManageApiLoanTaskManageDelete']
  const fetchManageApiLoanTaskManageGet: typeof import('../service/api/manageApi')['fetchManageApiLoanTaskManageGet']
  const fetchManageApiLoanTaskManageQueryPage: typeof import('../service/api/manageApi')['fetchManageApiLoanTaskManageQueryPage']
  const fetchManageApiLoanTaskManageUpdate: typeof import('../service/api/manageApi')['fetchManageApiLoanTaskManageUpdate']
  const fetchManageApiNoticeBatchRead: typeof import('../service/api/manageApi')['fetchManageApiNoticeBatchRead']
  const fetchManageApiNoticeQueryDetail: typeof import('../service/api/manageApi')['fetchManageApiNoticeQueryDetail']
  const fetchManageApiNoticeQueryPage: typeof import('../service/api/manageApi')['fetchManageApiNoticeQueryPage']
  const fetchManageApiNoticeQueryUnReadCount: typeof import('../service/api/manageApi')['fetchManageApiNoticeQueryUnReadCount']
  const fetchManageApiNoticeRead: typeof import('../service/api/manageApi')['fetchManageApiNoticeRead']
  const fetchManageApiOperateLogOperateLogBackup: typeof import('../service/api/manageApi')['fetchManageApiOperateLogOperateLogBackup']
  const fetchManageApiOperateLogOperateLogRecover: typeof import('../service/api/manageApi')['fetchManageApiOperateLogOperateLogRecover']
  const fetchManageApiOperateLogQueryPage: typeof import('../service/api/manageApi')['fetchManageApiOperateLogQueryPage']
  const fetchManageApiOrgImport: typeof import('../service/api/manageApi')['fetchManageApiOrgImport']
  const fetchManageApiOrgQueryPage: typeof import('../service/api/manageApi')['fetchManageApiOrgQueryPage']
  const fetchManageApiOrgRemove: typeof import('../service/api/manageApi')['fetchManageApiOrgRemove']
  const fetchManageApiOrgSave: typeof import('../service/api/manageApi')['fetchManageApiOrgSave']
  const fetchManageApiOrgUpdate: typeof import('../service/api/manageApi')['fetchManageApiOrgUpdate']
  const fetchManageApiOrganizationAdd: typeof import('../service/api/manageApi')['fetchManageApiOrganizationAdd']
  const fetchManageApiOrganizationDelete: typeof import('../service/api/manageApi')['fetchManageApiOrganizationDelete']
  const fetchManageApiOrganizationDetail: typeof import('../service/api/manageApi')['fetchManageApiOrganizationDetail']
  const fetchManageApiOrganizationGetOrganizationTree: typeof import('../service/api/manageApi')['fetchManageApiOrganizationGetOrganizationTree']
  const fetchManageApiOrganizationQueryPage: typeof import('../service/api/manageApi')['fetchManageApiOrganizationQueryPage']
  const fetchManageApiOrganizationUpdate: typeof import('../service/api/manageApi')['fetchManageApiOrganizationUpdate']
  const fetchManageApiRecordAdd: typeof import('../service/api/manageApi')['fetchManageApiRecordAdd']
  const fetchManageApiRecordChangeBatchStatus: typeof import('../service/api/manageApi')['fetchManageApiRecordChangeBatchStatus']
  const fetchManageApiRecordDelete: typeof import('../service/api/manageApi')['fetchManageApiRecordDelete']
  const fetchManageApiRecordEdit: typeof import('../service/api/manageApi')['fetchManageApiRecordEdit']
  const fetchManageApiRecordQueryDetail: typeof import('../service/api/manageApi')['fetchManageApiRecordQueryDetail']
  const fetchManageApiRecordQueryPage: typeof import('../service/api/manageApi')['fetchManageApiRecordQueryPage']
  const fetchManageApiRecordQueryRestAmount: typeof import('../service/api/manageApi')['fetchManageApiRecordQueryRestAmount']
  const fetchManageApiRecordRecordApply: typeof import('../service/api/manageApi')['fetchManageApiRecordRecordApply']
  const fetchManageApiRecordSubmit: typeof import('../service/api/manageApi')['fetchManageApiRecordSubmit']
  const fetchManageApiRiskAddRisk: typeof import('../service/api/manageApi')['fetchManageApiRiskAddRisk']
  const fetchManageApiRiskEdit: typeof import('../service/api/manageApi')['fetchManageApiRiskEdit']
  const fetchManageApiRiskPaid: typeof import('../service/api/manageApi')['fetchManageApiRiskPaid']
  const fetchManageApiRiskQueryDetail: typeof import('../service/api/manageApi')['fetchManageApiRiskQueryDetail']
  const fetchManageApiRiskQueryDoneList: typeof import('../service/api/manageApi')['fetchManageApiRiskQueryDoneList']
  const fetchManageApiRiskQueryHisTask: typeof import('../service/api/manageApi')['fetchManageApiRiskQueryHisTask']
  const fetchManageApiRiskQueryPage: typeof import('../service/api/manageApi')['fetchManageApiRiskQueryPage']
  const fetchManageApiRiskQueryTodoList: typeof import('../service/api/manageApi')['fetchManageApiRiskQueryTodoList']
  const fetchManageApiRiskRemoveRiskAudit: typeof import('../service/api/manageApi')['fetchManageApiRiskRemoveRiskAudit']
  const fetchManageApiRiskStatisticQueryListByRegion: typeof import('../service/api/manageApi')['fetchManageApiRiskStatisticQueryListByRegion']
  const fetchManageApiRiskStatisticQueryPageByBank: typeof import('../service/api/manageApi')['fetchManageApiRiskStatisticQueryPageByBank']
  const fetchManageApiRiskStatisticQueryPageByBankExport: typeof import('../service/api/manageApi')['fetchManageApiRiskStatisticQueryPageByBankExport']
  const fetchManageApiRiskStatisticQueryPageByRegion: typeof import('../service/api/manageApi')['fetchManageApiRiskStatisticQueryPageByRegion']
  const fetchManageApiRiskStatisticQueryPageByRegionExport: typeof import('../service/api/manageApi')['fetchManageApiRiskStatisticQueryPageByRegionExport']
  const fetchManageApiRiskSubmit: typeof import('../service/api/manageApi')['fetchManageApiRiskSubmit']
  const fetchManageApiRoleAddRole: typeof import('../service/api/manageApi')['fetchManageApiRoleAddRole']
  const fetchManageApiRoleDeleteRole: typeof import('../service/api/manageApi')['fetchManageApiRoleDeleteRole']
  const fetchManageApiRoleGetRole: typeof import('../service/api/manageApi')['fetchManageApiRoleGetRole']
  const fetchManageApiRoleQueryRoleList: typeof import('../service/api/manageApi')['fetchManageApiRoleQueryRoleList']
  const fetchManageApiRoleQueryRolePage: typeof import('../service/api/manageApi')['fetchManageApiRoleQueryRolePage']
  const fetchManageApiRoleUpdateRole: typeof import('../service/api/manageApi')['fetchManageApiRoleUpdateRole']
  const fetchManageApiSciEntAdd: typeof import('../service/api/manageApi')['fetchManageApiSciEntAdd']
  const fetchManageApiSciEntBatchUpdate: typeof import('../service/api/manageApi')['fetchManageApiSciEntBatchUpdate']
  const fetchManageApiSciEntCalcByYear: typeof import('../service/api/manageApi')['fetchManageApiSciEntCalcByYear']
  const fetchManageApiSciEntDownloadTemplate: typeof import('../service/api/manageApi')['fetchManageApiSciEntDownloadTemplate']
  const fetchManageApiSciEntImportData: typeof import('../service/api/manageApi')['fetchManageApiSciEntImportData']
  const fetchManageApiSciEntQueryDetail: typeof import('../service/api/manageApi')['fetchManageApiSciEntQueryDetail']
  const fetchManageApiSciEntQueryPage: typeof import('../service/api/manageApi')['fetchManageApiSciEntQueryPage']
  const fetchManageApiSciEntRemove: typeof import('../service/api/manageApi')['fetchManageApiSciEntRemove']
  const fetchManageApiSciEntSyncEntData: typeof import('../service/api/manageApi')['fetchManageApiSciEntSyncEntData']
  const fetchManageApiSciEntSyncRecordData: typeof import('../service/api/manageApi')['fetchManageApiSciEntSyncRecordData']
  const fetchManageApiSciEntUpdate: typeof import('../service/api/manageApi')['fetchManageApiSciEntUpdate']
  const fetchManageApiSciEntUpload: typeof import('../service/api/manageApi')['fetchManageApiSciEntUpload']
  const fetchManageApiSciEntbatchUpdate: typeof import('../service/api/manageApi')['fetchManageApiSciEntbatchUpdate']
  const fetchManageApiSciEntqueryDetail: typeof import('../service/api/manageApi')['fetchManageApiSciEntqueryDetail']
  const fetchManageApiSecurityGetRsaPublicKey: typeof import('../service/api/manageApi')['fetchManageApiSecurityGetRsaPublicKey']
  const fetchManageApiSecurityImportantDataDecrypt: typeof import('../service/api/manageApi')['fetchManageApiSecurityImportantDataDecrypt']
  const fetchManageApiSecurityImportantDataEncrypt: typeof import('../service/api/manageApi')['fetchManageApiSecurityImportantDataEncrypt']
  const fetchManageApiTrackTrackRecord: typeof import('../service/api/manageApi')['fetchManageApiTrackTrackRecord']
  const fetchManageApiUserAdd: typeof import('../service/api/manageApi')['fetchManageApiUserAdd']
  const fetchManageApiUserDelete: typeof import('../service/api/manageApi')['fetchManageApiUserDelete']
  const fetchManageApiUserDetail: typeof import('../service/api/manageApi')['fetchManageApiUserDetail']
  const fetchManageApiUserEncryptPhone: typeof import('../service/api/manageApi')['fetchManageApiUserEncryptPhone']
  const fetchManageApiUserQueryList: typeof import('../service/api/manageApi')['fetchManageApiUserQueryList']
  const fetchManageApiUserQueryPage: typeof import('../service/api/manageApi')['fetchManageApiUserQueryPage']
  const fetchManageApiUserUpdate: typeof import('../service/api/manageApi')['fetchManageApiUserUpdate']
  const fetchManageApiUserUpdatePwd: typeof import('../service/api/manageApi')['fetchManageApiUserUpdatePwd']
  const fetchManageApiconfigqueryPage: typeof import('../service/api/manageApi')['fetchManageApiconfigqueryPage']
  const fetchManageApirecordqueryDetail: typeof import('../service/api/manageApi')['fetchManageApirecordqueryDetail']
  const fetchMaterialEntityList: typeof import('../service/api/inventory')['fetchMaterialEntityList']
  const fetchMaterialList: typeof import('../service/api/inventory')['fetchMaterialList']
  const fetchMaterialQuery: typeof import('../service/api/inventory')['fetchMaterialQuery']
  const fetchMatingAdd: typeof import('../service/api/task')['fetchMatingAdd']
  const fetchMatingDelete: typeof import('../service/api/task')['fetchMatingDelete']
  const fetchMatingList: typeof import('../service/api/task')['fetchMatingList']
  const fetchMatingQuery: typeof import('../service/api/task')['fetchMatingQuery']
  const fetchMatingUpdate: typeof import('../service/api/task')['fetchMatingUpdate']
  const fetchNodeAdd: typeof import('../service/api/task')['fetchNodeAdd']
  const fetchNodeDelete: typeof import('../service/api/task')['fetchNodeDelete']
  const fetchNodeList: typeof import('../service/api/task')['fetchNodeList']
  const fetchNodeQuery: typeof import('../service/api/task')['fetchNodeQuery']
  const fetchNodeUpdate: typeof import('../service/api/task')['fetchNodeUpdate']
  const fetchObjByNameAndModelApply: typeof import('../service/api/rules')['fetchObjByNameAndModelApply']
  const fetchOperateLogPage: typeof import('../service/api/settings')['fetchOperateLogPage']
  const fetchOrgAdd: typeof import('../service/api/settings')['fetchOrgAdd']
  const fetchOrgDeleteById: typeof import('../service/api/settings')['fetchOrgDeleteById']
  const fetchOrgDetailById: typeof import('../service/api/settings')['fetchOrgDetailById']
  const fetchOrgQueryList: typeof import('../service/api/settings')['fetchOrgQueryList']
  const fetchOrgQueryOrgCertificate: typeof import('../service/api/organiza')['fetchOrgQueryOrgCertificate']
  const fetchOrgQueryOrgQualificate: typeof import('../service/api/organiza')['fetchOrgQueryOrgQualificate']
  const fetchOrgQueryOrgUserPage: typeof import('../service/api/organiza')['fetchOrgQueryOrgUserPage']
  const fetchOrgUpdate: typeof import('../service/api/settings')['fetchOrgUpdate']
  const fetchPerformanceAdd: typeof import('../service/api/sale')['fetchPerformanceAdd']
  const fetchPerformanceAddAdvise: typeof import('../service/api/sale')['fetchPerformanceAddAdvise']
  const fetchPerformanceDelete: typeof import('../service/api/sale')['fetchPerformanceDelete']
  const fetchPerformanceDeleteAdvise: typeof import('../service/api/sale')['fetchPerformanceDeleteAdvise']
  const fetchPerformanceList: typeof import('../service/api/sale')['fetchPerformanceList']
  const fetchPerformanceQuery: typeof import('../service/api/sale')['fetchPerformanceQuery']
  const fetchPerformanceUpdate: typeof import('../service/api/sale')['fetchPerformanceUpdate']
  const fetchPerformanceUpdateAdvise: typeof import('../service/api/sale')['fetchPerformanceUpdateAdvise']
  const fetchPlanAdd: typeof import('../service/api/rules')['fetchPlanAdd']
  const fetchPlanDelete: typeof import('../service/api/rules')['fetchPlanDelete']
  const fetchPlanHandle: typeof import('../service/api/rules')['fetchPlanHandle']
  const fetchPlanInspectName: typeof import('../service/api/rules')['fetchPlanInspectName']
  const fetchPlanList: typeof import('../service/api/rules')['fetchPlanList']
  const fetchPlanQuery: typeof import('../service/api/rules')['fetchPlanQuery']
  const fetchPlanUpdate: typeof import('../service/api/rules')['fetchPlanUpdate']
  const fetchProblemAdd: typeof import('../service/api/task')['fetchProblemAdd']
  const fetchProblemDelete: typeof import('../service/api/task')['fetchProblemDelete']
  const fetchProblemHandle: typeof import('../service/api/task')['fetchProblemHandle']
  const fetchProblemList: typeof import('../service/api/task')['fetchProblemList']
  const fetchProblemOver: typeof import('../service/api/task')['fetchProblemOver']
  const fetchProblemQuery: typeof import('../service/api/task')['fetchProblemQuery']
  const fetchProblemUpdate: typeof import('../service/api/task')['fetchProblemUpdate']
  const fetchProcessesBalance: typeof import('../service/api/rules')['fetchProcessesBalance']
  const fetchPumpCheckPart: typeof import('../service/api/rules')['fetchPumpCheckPart']
  const fetchPumpLibraryList: typeof import('../service/api/inventory')['fetchPumpLibraryList']
  const fetchPumpLibraryQuery: typeof import('../service/api/inventory')['fetchPumpLibraryQuery']
  const fetchQueryApiList: typeof import('../service/api/settings')['fetchQueryApiList']
  const fetchQueryCertificateDetail: typeof import('../service/api/rules')['fetchQueryCertificateDetail']
  const fetchQueryCertificatePage: typeof import('../service/api/rules')['fetchQueryCertificatePage']
  const fetchQueryCheck: typeof import('../service/api/rules')['fetchQueryCheck']
  const fetchQueryCheckApplyPage: typeof import('../service/api/rules')['fetchQueryCheckApplyPage']
  const fetchQueryCheckCardApplyPage: typeof import('../service/api/rules')['fetchQueryCheckCardApplyPage']
  const fetchQueryCheckCardPage: typeof import('../service/api/rules')['fetchQueryCheckCardPage']
  const fetchQueryCheckDetail: typeof import('../service/api/rules')['fetchQueryCheckDetail']
  const fetchQueryCheckDetails: typeof import('../service/api/rules')['fetchQueryCheckDetails']
  const fetchQueryCheckFactoryPage: typeof import('../service/api/rules')['fetchQueryCheckFactoryPage']
  const fetchQueryCheckVersion: typeof import('../service/api/rules')['fetchQueryCheckVersion']
  const fetchQueryCompanyCertificateList: typeof import('../service/api/supplier')['fetchQueryCompanyCertificateList']
  const fetchQueryCompanyCertificatePage: typeof import('../service/api/supplier')['fetchQueryCompanyCertificatePage']
  const fetchQueryCompanyPage: typeof import('../service/api/supplier')['fetchQueryCompanyPage']
  const fetchQueryCompanyQualificatePage: typeof import('../service/api/supplier')['fetchQueryCompanyQualificatePage']
  const fetchQueryContract: typeof import('../service/api/task')['fetchQueryContract']
  const fetchQueryContractProjectList: typeof import('../service/api/contractReserve')['fetchQueryContractProjectList']
  const fetchQueryDictPage: typeof import('../service/api/settings')['fetchQueryDictPage']
  const fetchQueryElementList: typeof import('../service/api/settings')['fetchQueryElementList']
  const fetchQueryIntegrityCompanyPage: typeof import('../service/api/supplier')['fetchQueryIntegrityCompanyPage']
  const fetchQueryList: typeof import('../service/api/task')['fetchQueryList']
  const fetchQueryOperateLogPage: typeof import('../service/api/settings')['fetchQueryOperateLogPage']
  const fetchQueryPageList: typeof import('../service/api/task')['fetchQueryPageList']
  const fetchQueryRoleList: typeof import('../service/api/settings')['fetchQueryRoleList']
  const fetchQueryRolePage: typeof import('../service/api/settings')['fetchQueryRolePage']
  const fetchQueryTask: typeof import('../service/api/task')['fetchQueryTask']
  const fetchResetPwd: typeof import('../service/api/auth')['fetchResetPwd']
  const fetchRiskAdd: typeof import('../service/api/task')['fetchRiskAdd']
  const fetchRiskDel: typeof import('../service/api/task')['fetchRiskDel']
  const fetchRiskDelete: typeof import('../service/api/task')['fetchRiskDelete']
  const fetchRiskHandle: typeof import('../service/api/task')['fetchRiskHandle']
  const fetchRiskList: typeof import('../service/api/task')['fetchRiskList']
  const fetchRiskQuery: typeof import('../service/api/task')['fetchRiskQuery']
  const fetchRiskUpdate: typeof import('../service/api/task')['fetchRiskUpdate']
  const fetchSaleLibraryAdd: typeof import('../service/api/inventory')['fetchSaleLibraryAdd']
  const fetchSaleLibraryDelete: typeof import('../service/api/inventory')['fetchSaleLibraryDelete']
  const fetchSaleLibraryHandle: typeof import('../service/api/inventory')['fetchSaleLibraryHandle']
  const fetchSaleLibraryList: typeof import('../service/api/inventory')['fetchSaleLibraryList']
  const fetchSaleLibraryQuery: typeof import('../service/api/inventory')['fetchSaleLibraryQuery']
  const fetchSaleLibraryUpdate: typeof import('../service/api/inventory')['fetchSaleLibraryUpdate']
  const fetchSaveDelete: typeof import('../service/api/task')['fetchSaveDelete']
  const fetchSaveQuality: typeof import('../service/api/task')['fetchSaveQuality']
  const fetchSaveQualityHandle: typeof import('../service/api/task')['fetchSaveQualityHandle']
  const fetchSaveQuery: typeof import('../service/api/task')['fetchSaveQuery']
  const fetchSaveUpdate: typeof import('../service/api/task')['fetchSaveUpdate']
  const fetchSendCode: typeof import('../service/api/auth')['fetchSendCode']
  const fetchShippingAdd: typeof import('../service/api/rules')['fetchShippingAdd']
  const fetchShippingAudit: typeof import('../service/api/rules')['fetchShippingAudit']
  const fetchShippingDelete: typeof import('../service/api/rules')['fetchShippingDelete']
  const fetchShippingHandle: typeof import('../service/api/rules')['fetchShippingHandle']
  const fetchShippingList: typeof import('../service/api/rules')['fetchShippingList']
  const fetchShippingQuery: typeof import('../service/api/rules')['fetchShippingQuery']
  const fetchShippingUpdate: typeof import('../service/api/rules')['fetchShippingUpdate']
  const fetchSortElement: typeof import('../service/api/settings')['fetchSortElement']
  const fetchStateChangeAdd: typeof import('../service/api/task')['fetchStateChangeAdd']
  const fetchStateChangeDelete: typeof import('../service/api/task')['fetchStateChangeDelete']
  const fetchStateChangeQuery: typeof import('../service/api/task')['fetchStateChangeQuery']
  const fetchStateChangeUpdate: typeof import('../service/api/task')['fetchStateChangeUpdate']
  const fetchSuperviseAdd: typeof import('../service/api/task')['fetchSuperviseAdd']
  const fetchSuperviseDelete: typeof import('../service/api/task')['fetchSuperviseDelete']
  const fetchSuperviseList: typeof import('../service/api/task')['fetchSuperviseList']
  const fetchSuperviseQuery: typeof import('../service/api/task')['fetchSuperviseQuery']
  const fetchSuperviseUpdateFlow: typeof import('../service/api/task')['fetchSuperviseUpdateFlow']
  const fetchUnqualifiedAdd: typeof import('../service/api/task')['fetchUnqualifiedAdd']
  const fetchUnqualifiedDelete: typeof import('../service/api/task')['fetchUnqualifiedDelete']
  const fetchUnqualifiedHandle: typeof import('../service/api/task')['fetchUnqualifiedHandle']
  const fetchUnqualifiedList: typeof import('../service/api/task')['fetchUnqualifiedList']
  const fetchUnqualifiedQuery: typeof import('../service/api/task')['fetchUnqualifiedQuery']
  const fetchUnqualifiedUpdate: typeof import('../service/api/task')['fetchUnqualifiedUpdate']
  const fetchUpdateCheckApply: typeof import('../service/api/rules')['fetchUpdateCheckApply']
  const fetchUpdateCompany: typeof import('../service/api/supplier')['fetchUpdateCompany']
  const fetchUpdateCompanyCertificate: typeof import('../service/api/supplier')['fetchUpdateCompanyCertificate']
  const fetchUpdateCompanyQualificate: typeof import('../service/api/supplier')['fetchUpdateCompanyQualificate']
  const fetchUpdateElement: typeof import('../service/api/settings')['fetchUpdateElement']
  const fetchUpdateInpect: typeof import('../service/api/rules')['fetchUpdateInpect']
  const fetchUpdateIntegrityCompany: typeof import('../service/api/supplier')['fetchUpdateIntegrityCompany']
  const fetchUpdateOrgCertificate: typeof import('../service/api/organiza')['fetchUpdateOrgCertificate']
  const fetchUpdateOrgQualificate: typeof import('../service/api/organiza')['fetchUpdateOrgQualificate']
  const fetchUpdateOrgUser: typeof import('../service/api/organiza')['fetchUpdateOrgUser']
  const fetchUpdateOrganiza: typeof import('../service/api/organiza')['fetchUpdateOrganiza']
  const fetchUpdateProduct: typeof import('../service/api/rules')['fetchUpdateProduct']
  const fetchUpdatePwd: typeof import('../service/api/auth')['fetchUpdatePwd']
  const fetchUpdateRole: typeof import('../service/api/settings')['fetchUpdateRole']
  const fetchUserAdd: typeof import('../service/api/settings')['fetchUserAdd']
  const fetchUserDelete: typeof import('../service/api/settings')['fetchUserDelete']
  const fetchUserDetailById: typeof import('../service/api/settings')['fetchUserDetailById']
  const fetchUserImportExcel: typeof import('../service/api/common')['fetchUserImportExcel']
  const fetchUserList: typeof import('../service/api/settings')['fetchUserList']
  const fetchUserQueryPage: typeof import('../service/api/settings')['fetchUserQueryPage']
  const fetchUserUpdate: typeof import('../service/api/settings')['fetchUserUpdate']
  const fetchUserUpdateStatus: typeof import('../service/api/settings')['fetchUserUpdateStatus']
  const fetchWorkOrderNoProApply: typeof import('../service/api/rules')['fetchWorkOrderNoProApply']
  const fileDeleteById: typeof import('../service/api/common')['fileDeleteById']
  const flowChartNoColor: typeof import('../service/api/contractReserve')['flowChartNoColor']
  const flowchart: typeof import('../service/api/contractReserve')['flowchart']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getArchives: typeof import('../service/api/filedataBase')['getArchives']
  const getBackNodeList: typeof import('../service/api/contractReserve')['getBackNodeList']
  const getCompany: typeof import('../service/api/contractReserve')['getCompany']
  const getCompanyTask: typeof import('../service/api/contractReserve')['getCompanyTask']
  const getContract: typeof import('../service/api/contractReserve')['getContract']
  const getContractManufacture: typeof import('../service/api/contractReserve')['getContractManufacture']
  const getContractManufacturePage: typeof import('../service/api/contractReserve')['getContractManufacturePage']
  const getContractMaterial: typeof import('../service/api/contractReserve')['getContractMaterial']
  const getContractMatter: typeof import('../service/api/contractReserve')['getContractMatter']
  const getContractProject: typeof import('../service/api/contractReserve')['getContractProject']
  const getContractProjectInfo: typeof import('../service/api/contractReserve')['getContractProjectInfo']
  const getContractProjectPage: typeof import('../service/api/contractReserve')['getContractProjectPage']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getFlowTask: typeof import('../service/api/contractReserve')['getFlowTask']
  const getHydrostaticTest: typeof import('../service/api/rules')['getHydrostaticTest']
  const getMaterialWarehouse: typeof import('../service/api/contractReserve')['getMaterialWarehouse']
  const getOrgAttachment: typeof import('../service/api/organiza')['getOrgAttachment']
  const getOrgCertificateStatus: typeof import('../service/api/contractReserve')['getOrgCertificateStatus']
  const getOrgUser: typeof import('../service/api/contractReserve')['getOrgUser']
  const getOrganiza: typeof import('../service/api/contractReserve')['getOrganiza']
  const getOrganizeTask: typeof import('../service/api/contractReserve')['getOrganizeTask']
  const getPayment: typeof import('../service/api/contractReserve')['getPayment']
  const getPerformancePlan: typeof import('../service/api/contractReserve')['getPerformancePlan']
  const getProcessesPart: typeof import('../service/api/rules')['getProcessesPart']
  const getProduct: typeof import('../service/api/contractReserve')['getProduct']
  const getProductForParts: typeof import('../service/api/contractReserve')['getProductForParts']
  const getProductTaskById: typeof import('../service/api/contractReserve')['getProductTaskById']
  const getProductTaskCode: typeof import('../service/api/contractReserve')['getProductTaskCode']
  const getProgressInformation: typeof import('../service/api/contractReserve')['getProgressInformation']
  const getProject: typeof import('../service/api/contractReserve')['getProject']
  const getProjectEndAndParts: typeof import('../service/api/contractReserve')['getProjectEndAndParts']
  const getProjectPlannerDetail: typeof import('../service/api/contractReserve')['getProjectPlannerDetail']
  const getProjectPlannerPage: typeof import('../service/api/contractReserve')['getProjectPlannerPage']
  const getPurchaseCode: typeof import('../service/api/contractReserve')['getPurchaseCode']
  const getPurchaseContract: typeof import('../service/api/contractReserve')['getPurchaseContract']
  const getPurchaseContractById: typeof import('../service/api/contractReserve')['getPurchaseContractById']
  const getPurchaseContractList: typeof import('../service/api/contractReserve')['getPurchaseContractList']
  const getPurchaseContractPage: typeof import('../service/api/contractReserve')['getPurchaseContractPage']
  const getPurchasePartsById: typeof import('../service/api/contractReserve')['getPurchasePartsById']
  const getPurchasePlan: typeof import('../service/api/contractReserve')['getPurchasePlan']
  const getPurchasePlanPage: typeof import('../service/api/contractReserve')['getPurchasePlanPage']
  const getPurchaseUser: typeof import('../service/api/contractReserve')['getPurchaseUser']
  const getStrCode: typeof import('../service/api/contractReserve')['getStrCode']
  const getTaskContractProject: typeof import('../service/api/contractReserve')['getTaskContractProject']
  const getTaskProject: typeof import('../service/api/contractReserve')['getTaskProject']
  const getUnqualifiedList: typeof import('../service/api/rules')['getUnqualifiedList']
  const getWarehouseProduct: typeof import('../service/api/filedataBase')['getWarehouseProduct']
  const getcompanContract: typeof import('../service/api/contractReserve')['getcompanContract']
  const getdoneList: typeof import('../service/api/contractReserve')['getdoneList']
  const h: typeof import('vue')['h']
  const handlerProjectPlanner: typeof import('../service/api/contractReserve')['handlerProjectPlanner']
  const importCompany: typeof import('../service/api/supplier')['importCompany']
  const importEndProduct: typeof import('../service/api/contractReserve')['importEndProduct']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const materialLibrarqueryList: typeof import('../service/api/inventory')['materialLibrarqueryList']
  const mesContract: typeof import('../service/api/contractReserve')['mesContract']
  const mesPickLibrary: typeof import('../service/api/contractReserve')['mesPickLibrary']
  const mesProductTask: typeof import('../service/api/contractReserve')['mesProductTask']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const permission: typeof import('../globals/directives')['permission']
  const productPage: typeof import('../service/api/filedataBase')['productPage']
  const productTaskhandle: typeof import('../service/api/contractReserve')['productTaskhandle']
  const productTaskqueryList: typeof import('../service/api/contractReserve')['productTaskqueryList']
  const projectCreateProjectCode: typeof import('../service/api/contractReserve')['projectCreateProjectCode']
  const projectDeleteProject: typeof import('../service/api/contractReserve')['projectDeleteProject']
  const projectGetProType: typeof import('../service/api/contractReserve')['projectGetProType']
  const projecthandle: typeof import('../service/api/contractReserve')['projecthandle']
  const projectupdateProject: typeof import('../service/api/contractReserve')['projectupdateProject']
  const provide: typeof import('vue')['provide']
  const queryCompanyArchives: typeof import('../service/api/filedataBase')['queryCompanyArchives']
  const queryCompanyCertificateYearList: typeof import('../service/api/contractReserve')['queryCompanyCertificateYearList']
  const queryContract: typeof import('../service/api/contractReserve')['queryContract']
  const queryContractArchivesData: typeof import('../service/api/filedataBase')['queryContractArchivesData']
  const queryFileDetails: typeof import('../service/api/filedataBase')['queryFileDetails']
  const queryFileList: typeof import('../service/api/filedataBase')['queryFileList']
  const queryInspectItems: typeof import('../service/api/contractReserve')['queryInspectItems']
  const queryLatestProductName: typeof import('../service/api/contractReserve')['queryLatestProductName']
  const queryMaterialPageList: typeof import('../service/api/contractReserve')['queryMaterialPageList']
  const queryMaterials: typeof import('../service/api/filedataBase')['queryMaterials']
  const queryOrgCertificateYearList: typeof import('../service/api/contractReserve')['queryOrgCertificateYearList']
  const queryPageByBankExport: typeof import('../service/api/inventory')['queryPageByBankExport']
  const queryPageByEntExport: typeof import('../service/api/inventory')['queryPageByEntExport']
  const queryPageByRegionExport: typeof import('../service/api/inventory')['queryPageByRegionExport']
  const queryProductFactoryList: typeof import('../service/api/filedataBase')['queryProductFactoryList']
  const queryProductMaterials: typeof import('../service/api/filedataBase')['queryProductMaterials']
  const queryProductName: typeof import('../service/api/contractReserve')['queryProductName']
  const queryProductTaskPage: typeof import('../service/api/contractReserve')['queryProductTaskPage']
  const queryProjectPage: typeof import('../service/api/contractReserve')['queryProjectPage']
  const queryPurchaseContractByContractProjectId: typeof import('../service/api/contractReserve')['queryPurchaseContractByContractProjectId']
  const queryPurchaseContracts: typeof import('../service/api/filedataBase')['queryPurchaseContracts']
  const queryTaskPage: typeof import('../service/api/contractReserve')['queryTaskPage']
  const queryUnFinishPurchaseContractPage: typeof import('../service/api/contractReserve')['queryUnFinishPurchaseContractPage']
  const querycompanyTaskPage: typeof import('../service/api/contractReserve')['querycompanyTaskPage']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const receiptadd: typeof import('../service/api/inventory')['receiptadd']
  const receiptdelete: typeof import('../service/api/inventory')['receiptdelete']
  const receiptquery: typeof import('../service/api/inventory')['receiptquery']
  const receiptqueryPageList: typeof import('../service/api/inventory')['receiptqueryPageList']
  const receiptupdate: typeof import('../service/api/inventory')['receiptupdate']
  const recordApply: typeof import('../service/api/manageApi')['recordApply']
  const recordedit: typeof import('../service/api/manageApi')['recordedit']
  const ref: typeof import('vue')['ref']
  const reloadMaterialCode: typeof import('../service/api/contractReserve')['reloadMaterialCode']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const riskStatisticqueryPageByBankExport: typeof import('../service/api/inventory')['riskStatisticqueryPageByBankExport']
  const riskStatisticqueryPageByRegionExport: typeof import('../service/api/inventory')['riskStatisticqueryPageByRegionExport']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const stateChange: typeof import('../service/api/contractReserve')['stateChange']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const toDoPage: typeof import('../service/api/contractReserve')['toDoPage']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const upArchivesDataFile: typeof import('../service/api/filedataBase')['upArchivesDataFile']
  const updContractProject: typeof import('../service/api/contractReserve')['updContractProject']
  const updateCompanyTask: typeof import('../service/api/contractReserve')['updateCompanyTask']
  const updateContractArchivesDataRemark: typeof import('../service/api/contractReserve')['updateContractArchivesDataRemark']
  const updateContractManufacture: typeof import('../service/api/contractReserve')['updateContractManufacture']
  const updateEndProductByIdsFactory: typeof import('../service/api/contractReserve')['updateEndProductByIdsFactory']
  const updateOrganizeTask: typeof import('../service/api/contractReserve')['updateOrganizeTask']
  const updatePartsByIdsFactory: typeof import('../service/api/contractReserve')['updatePartsByIdsFactory']
  const updateProduct: typeof import('../service/api/filedataBase')['updateProduct']
  const updateProductTask: typeof import('../service/api/contractReserve')['updateProductTask']
  const updateProjectPlanner: typeof import('../service/api/contractReserve')['updateProjectPlanner']
  const updatePurchaseContract: typeof import('../service/api/contractReserve')['updatePurchaseContract']
  const updatePurchasePlan: typeof import('../service/api/contractReserve')['updatePurchasePlan']
  const updateReceiptDetailed: typeof import('../service/api/inventory')['updateReceiptDetailed']
  const updateReceiptHandle: typeof import('../service/api/inventory')['updateReceiptHandle']
  const updateStatusByIds: typeof import('../service/api/contractReserve')['updateStatusByIds']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useModel: typeof import('vue')['useModel']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const vClickOutside: typeof import('../globals/directives')['vClickOutside']
  const vFocus: typeof import('../globals/directives')['vFocus']
  const vNumberOnly: typeof import('../globals/directives')['vNumberOnly']
  const vPermission: typeof import('../globals/directives')['vPermission']
  const verificationRecordadd: typeof import('../service/api/contractReserve')['verificationRecordadd']
  const verificationRecorddelete: typeof import('../service/api/contractReserve')['verificationRecorddelete']
  const verificationRecordqueryDetail: typeof import('../service/api/contractReserve')['verificationRecordqueryDetail']
  const verificationRecordqueryPage: typeof import('../service/api/contractReserve')['verificationRecordqueryPage']
  const verificationRecordupdate: typeof import('../service/api/contractReserve')['verificationRecordupdate']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const workBenchContract: typeof import('../service/api/contractReserve')['workBenchContract']
  const zipFilesWithCommonsCompress: typeof import('../service/api/filedataBase')['zipFilesWithCommonsCompress']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { ClickOutsideOptions } from '../globals/directives'
  import('../globals/directives')
}

// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface GlobalComponents {}
  interface ComponentCustomProperties {
    readonly $formatThousands: UnwrapRef<typeof import('../globals/methods')['$formatThousands']>
    readonly $formatTime: UnwrapRef<typeof import('../globals/methods')['$formatTime']>
    readonly $getFileExt: UnwrapRef<typeof import('../globals/methods')['$getFileExt']>
    readonly $isEmpty: UnwrapRef<typeof import('../globals/methods')['$isEmpty']>
    readonly $randomColor: UnwrapRef<typeof import('../globals/methods')['$randomColor']>
    readonly $toFixed: UnwrapRef<typeof import('../globals/methods')['$toFixed']>
    readonly EffectScope: UnwrapRef<typeof import('vue')['EffectScope']>
    readonly PurchasePlanhandle: UnwrapRef<typeof import('../service/api/contractReserve')['PurchasePlanhandle']>
    readonly acceptHMRUpdate: UnwrapRef<typeof import('pinia')['acceptHMRUpdate']>
    readonly addCompanyTask: UnwrapRef<typeof import('../service/api/contractReserve')['addCompanyTask']>
    readonly addContractManufacture: UnwrapRef<typeof import('../service/api/contractReserve')['addContractManufacture']>
    readonly addContractMaterial: UnwrapRef<typeof import('../service/api/contractReserve')['addContractMaterial']>
    readonly addContractMatter: UnwrapRef<typeof import('../service/api/contractReserve')['addContractMatter']>
    readonly addContractProject: UnwrapRef<typeof import('../service/api/contractReserve')['addContractProject']>
    readonly addOrUpdate: UnwrapRef<typeof import('../service/api/inventory')['addOrUpdate']>
    readonly addOrgAttachment: UnwrapRef<typeof import('../service/api/organiza')['addOrgAttachment']>
    readonly addOrganizeTask: UnwrapRef<typeof import('../service/api/contractReserve')['addOrganizeTask']>
    readonly addPayment: UnwrapRef<typeof import('../service/api/contractReserve')['addPayment']>
    readonly addPerformancePlan: UnwrapRef<typeof import('../service/api/contractReserve')['addPerformancePlan']>
    readonly addProduct: UnwrapRef<typeof import('../service/api/filedataBase')['addProduct']>
    readonly addProductTask: UnwrapRef<typeof import('../service/api/contractReserve')['addProductTask']>
    readonly addProject: UnwrapRef<typeof import('../service/api/contractReserve')['addProject']>
    readonly addProjectPlanner: UnwrapRef<typeof import('../service/api/contractReserve')['addProjectPlanner']>
    readonly addPurchaseContract: UnwrapRef<typeof import('../service/api/contractReserve')['addPurchaseContract']>
    readonly addPurchasePlan: UnwrapRef<typeof import('../service/api/contractReserve')['addPurchasePlan']>
    readonly afterSalesArchivesData: UnwrapRef<typeof import('../service/api/filedataBase')['afterSalesArchivesData']>
    readonly anyNodeList: UnwrapRef<typeof import('../service/api/contractReserve')['anyNodeList']>
    readonly attachmentquery: UnwrapRef<typeof import('../service/api/filedataBase')['attachmentquery']>
    readonly backNodeList: UnwrapRef<typeof import('../service/api/rules')['backNodeList']>
    readonly batchAddAcceptance: UnwrapRef<typeof import('../service/api/contractReserve')['batchAddAcceptance']>
    readonly checkhandle: UnwrapRef<typeof import('../service/api/rules')['checkhandle']>
    readonly companyBYName: UnwrapRef<typeof import('../service/api/contractReserve')['companyBYName']>
    readonly companyCertificatequeryList: UnwrapRef<typeof import('../service/api/contractReserve')['companyCertificatequeryList']>
    readonly companyhandle: UnwrapRef<typeof import('../service/api/supplier')['companyhandle']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly contractManufacturegetProject: UnwrapRef<typeof import('../service/api/contractReserve')['contractManufacturegetProject']>
    readonly contractManufacturehandle: UnwrapRef<typeof import('../service/api/contractReserve')['contractManufacturehandle']>
    readonly contractPerformanceData: UnwrapRef<typeof import('../service/api/filedataBase')['contractPerformanceData']>
    readonly contractProjectgetProject: UnwrapRef<typeof import('../service/api/contractReserve')['contractProjectgetProject']>
    readonly countDoneEveryMonth: UnwrapRef<typeof import('../service/api/contractReserve')['countDoneEveryMonth']>
    readonly countDoneThisMonthAndToday: UnwrapRef<typeof import('../service/api/contractReserve')['countDoneThisMonthAndToday']>
    readonly createApp: UnwrapRef<typeof import('vue')['createApp']>
    readonly createFactoryNumber: UnwrapRef<typeof import('../service/api/contractReserve')['createFactoryNumber']>
    readonly createNotificationNumber: UnwrapRef<typeof import('../service/api/contractReserve')['createNotificationNumber']>
    readonly createPinia: UnwrapRef<typeof import('pinia')['createPinia']>
    readonly createProductJobCode: UnwrapRef<typeof import('../service/api/contractReserve')['createProductJobCode']>
    readonly createPurchaseContractCode: UnwrapRef<typeof import('../service/api/contractReserve')['createPurchaseContractCode']>
    readonly createPurchasePlanCode: UnwrapRef<typeof import('../service/api/contractReserve')['createPurchasePlanCode']>
    readonly createRecCode: UnwrapRef<typeof import('../service/api/contractReserve')['createRecCode']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineStore: UnwrapRef<typeof import('pinia')['defineStore']>
    readonly deleteCompanyCertificateYear: UnwrapRef<typeof import('../service/api/contractReserve')['deleteCompanyCertificateYear']>
    readonly deleteCompanyTask: UnwrapRef<typeof import('../service/api/contractReserve')['deleteCompanyTask']>
    readonly deleteCompanyYear: UnwrapRef<typeof import('../service/api/contractReserve')['deleteCompanyYear']>
    readonly deleteComponentPart: UnwrapRef<typeof import('../service/api/contractReserve')['deleteComponentPart']>
    readonly deleteContractManufacture: UnwrapRef<typeof import('../service/api/contractReserve')['deleteContractManufacture']>
    readonly deleteContractMaterial: UnwrapRef<typeof import('../service/api/contractReserve')['deleteContractMaterial']>
    readonly deleteContractMatter: UnwrapRef<typeof import('../service/api/contractReserve')['deleteContractMatter']>
    readonly deleteContractProject: UnwrapRef<typeof import('../service/api/contractReserve')['deleteContractProject']>
    readonly deleteEndProduct: UnwrapRef<typeof import('../service/api/contractReserve')['deleteEndProduct']>
    readonly deleteLog: UnwrapRef<typeof import('../service/api/inventory')['deleteLog']>
    readonly deleteOrgCertificateYearList: UnwrapRef<typeof import('../service/api/contractReserve')['deleteOrgCertificateYearList']>
    readonly deleteOrganizeTask: UnwrapRef<typeof import('../service/api/contractReserve')['deleteOrganizeTask']>
    readonly deletePayment: UnwrapRef<typeof import('../service/api/contractReserve')['deletePayment']>
    readonly deletePerformancePlan: UnwrapRef<typeof import('../service/api/contractReserve')['deletePerformancePlan']>
    readonly deleteProduct: UnwrapRef<typeof import('../service/api/filedataBase')['deleteProduct']>
    readonly deleteProductTask: UnwrapRef<typeof import('../service/api/contractReserve')['deleteProductTask']>
    readonly deleteProjectPlanner: UnwrapRef<typeof import('../service/api/contractReserve')['deleteProjectPlanner']>
    readonly deletePurchaseContract: UnwrapRef<typeof import('../service/api/contractReserve')['deletePurchaseContract']>
    readonly deletePurchasePlan: UnwrapRef<typeof import('../service/api/contractReserve')['deletePurchasePlan']>
    readonly donePage: UnwrapRef<typeof import('../service/api/contractReserve')['donePage']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly exportCompany: UnwrapRef<typeof import('../service/api/contractReserve')['exportCompany']>
    readonly exportConfiggetInfo: UnwrapRef<typeof import('../service/api/inventory')['exportConfiggetInfo']>
    readonly exportContractManufacture: UnwrapRef<typeof import('../service/api/contractReserve')['exportContractManufacture']>
    readonly exportLog: UnwrapRef<typeof import('../service/api/inventory')['exportLog']>
    readonly exportProductTask: UnwrapRef<typeof import('../service/api/contractReserve')['exportProductTask']>
    readonly exportPurchaseContract: UnwrapRef<typeof import('../service/api/contractReserve')['exportPurchaseContract']>
    readonly exportPurchasePlan: UnwrapRef<typeof import('../service/api/contractReserve')['exportPurchasePlan']>
    readonly exportQualityAssurance: UnwrapRef<typeof import('../service/api/contractReserve')['exportQualityAssurance']>
    readonly fetchAddCheckApply: UnwrapRef<typeof import('../service/api/rules')['fetchAddCheckApply']>
    readonly fetchAddCompany: UnwrapRef<typeof import('../service/api/supplier')['fetchAddCompany']>
    readonly fetchAddCompanyCertificate: UnwrapRef<typeof import('../service/api/supplier')['fetchAddCompanyCertificate']>
    readonly fetchAddCompanyQualificate: UnwrapRef<typeof import('../service/api/supplier')['fetchAddCompanyQualificate']>
    readonly fetchAddElement: UnwrapRef<typeof import('../service/api/settings')['fetchAddElement']>
    readonly fetchAddIntegrityCompany: UnwrapRef<typeof import('../service/api/supplier')['fetchAddIntegrityCompany']>
    readonly fetchAddOrgCertificate: UnwrapRef<typeof import('../service/api/organiza')['fetchAddOrgCertificate']>
    readonly fetchAddOrgQualificate: UnwrapRef<typeof import('../service/api/organiza')['fetchAddOrgQualificate']>
    readonly fetchAddOrgUser: UnwrapRef<typeof import('../service/api/organiza')['fetchAddOrgUser']>
    readonly fetchAddRole: UnwrapRef<typeof import('../service/api/settings')['fetchAddRole']>
    readonly fetchAdminResetPwd: UnwrapRef<typeof import('../service/api/auth')['fetchAdminResetPwd']>
    readonly fetchAftermarketAdd: UnwrapRef<typeof import('../service/api/sale')['fetchAftermarketAdd']>
    readonly fetchAftermarketDelete: UnwrapRef<typeof import('../service/api/sale')['fetchAftermarketDelete']>
    readonly fetchAftermarketList: UnwrapRef<typeof import('../service/api/sale')['fetchAftermarketList']>
    readonly fetchAftermarketQuery: UnwrapRef<typeof import('../service/api/sale')['fetchAftermarketQuery']>
    readonly fetchAftermarketUpdate: UnwrapRef<typeof import('../service/api/sale')['fetchAftermarketUpdate']>
    readonly fetchApplyContract: UnwrapRef<typeof import('../service/api/rules')['fetchApplyContract']>
    readonly fetchApplyRelevantFile: UnwrapRef<typeof import('../service/api/rules')['fetchApplyRelevantFile']>
    readonly fetchAttachmentQuery: UnwrapRef<typeof import('../service/api/common')['fetchAttachmentQuery']>
    readonly fetchAttachmentUpload: UnwrapRef<typeof import('../service/api/common')['fetchAttachmentUpload']>
    readonly fetchAuditApplyCondition: UnwrapRef<typeof import('../service/api/rules')['fetchAuditApplyCondition']>
    readonly fetchAuditApplyHandle: UnwrapRef<typeof import('../service/api/rules')['fetchAuditApplyHandle']>
    readonly fetchAuditApplyQuality: UnwrapRef<typeof import('../service/api/rules')['fetchAuditApplyQuality']>
    readonly fetchCardApplyAdd: UnwrapRef<typeof import('../service/api/rules')['fetchCardApplyAdd']>
    readonly fetchCardApplyAudit: UnwrapRef<typeof import('../service/api/rules')['fetchCardApplyAudit']>
    readonly fetchCardApplyDelete: UnwrapRef<typeof import('../service/api/rules')['fetchCardApplyDelete']>
    readonly fetchCardApplyQuery: UnwrapRef<typeof import('../service/api/rules')['fetchCardApplyQuery']>
    readonly fetchCardApplyUpdate: UnwrapRef<typeof import('../service/api/rules')['fetchCardApplyUpdate']>
    readonly fetchCheckCardApplyHandle: UnwrapRef<typeof import('../service/api/rules')['fetchCheckCardApplyHandle']>
    readonly fetchCheckLatestVersion: UnwrapRef<typeof import('../service/api/rules')['fetchCheckLatestVersion']>
    readonly fetchCompanyApprove: UnwrapRef<typeof import('../service/api/supplier')['fetchCompanyApprove']>
    readonly fetchCompanyImportExcel: UnwrapRef<typeof import('../service/api/supplier')['fetchCompanyImportExcel']>
    readonly fetchContractProject: UnwrapRef<typeof import('../service/api/task')['fetchContractProject']>
    readonly fetchCostAdd: UnwrapRef<typeof import('../service/api/task')['fetchCostAdd']>
    readonly fetchCostDelete: UnwrapRef<typeof import('../service/api/task')['fetchCostDelete']>
    readonly fetchCostList: UnwrapRef<typeof import('../service/api/task')['fetchCostList']>
    readonly fetchCostQuery: UnwrapRef<typeof import('../service/api/task')['fetchCostQuery']>
    readonly fetchCostUpdate: UnwrapRef<typeof import('../service/api/task')['fetchCostUpdate']>
    readonly fetchCreateCheckNumber: UnwrapRef<typeof import('../service/api/rules')['fetchCreateCheckNumber']>
    readonly fetchDeleteCheck: UnwrapRef<typeof import('../service/api/rules')['fetchDeleteCheck']>
    readonly fetchDeleteCheckApplyById: UnwrapRef<typeof import('../service/api/rules')['fetchDeleteCheckApplyById']>
    readonly fetchDeleteCompanyById: UnwrapRef<typeof import('../service/api/supplier')['fetchDeleteCompanyById']>
    readonly fetchDeleteCompanyCertificateById: UnwrapRef<typeof import('../service/api/supplier')['fetchDeleteCompanyCertificateById']>
    readonly fetchDeleteCompanyQualificateById: UnwrapRef<typeof import('../service/api/supplier')['fetchDeleteCompanyQualificateById']>
    readonly fetchDeleteCondition: UnwrapRef<typeof import('../service/api/rules')['fetchDeleteCondition']>
    readonly fetchDeleteElementById: UnwrapRef<typeof import('../service/api/settings')['fetchDeleteElementById']>
    readonly fetchDeleteInspect: UnwrapRef<typeof import('../service/api/rules')['fetchDeleteInspect']>
    readonly fetchDeleteOrgCertificateById: UnwrapRef<typeof import('../service/api/organiza')['fetchDeleteOrgCertificateById']>
    readonly fetchDeleteOrgQualificateById: UnwrapRef<typeof import('../service/api/organiza')['fetchDeleteOrgQualificateById']>
    readonly fetchDeleteOrgUserById: UnwrapRef<typeof import('../service/api/organiza')['fetchDeleteOrgUserById']>
    readonly fetchDeleteQuality: UnwrapRef<typeof import('../service/api/rules')['fetchDeleteQuality']>
    readonly fetchDeleteRoleById: UnwrapRef<typeof import('../service/api/settings')['fetchDeleteRoleById']>
    readonly fetchDeviationAdd: UnwrapRef<typeof import('../service/api/task')['fetchDeviationAdd']>
    readonly fetchDeviationDelete: UnwrapRef<typeof import('../service/api/task')['fetchDeviationDelete']>
    readonly fetchDeviationList: UnwrapRef<typeof import('../service/api/task')['fetchDeviationList']>
    readonly fetchDeviationQuery: UnwrapRef<typeof import('../service/api/task')['fetchDeviationQuery']>
    readonly fetchDeviationUpdate: UnwrapRef<typeof import('../service/api/task')['fetchDeviationUpdate']>
    readonly fetchDictDelete: UnwrapRef<typeof import('../service/api/settings')['fetchDictDelete']>
    readonly fetchDictDetailByType: UnwrapRef<typeof import('../service/api/settings')['fetchDictDetailByType']>
    readonly fetchDictQueryList: UnwrapRef<typeof import('../service/api/common')['fetchDictQueryList']>
    readonly fetchDictUpdate: UnwrapRef<typeof import('../service/api/settings')['fetchDictUpdate']>
    readonly fetchExportApplyCard: UnwrapRef<typeof import('../service/api/rules')['fetchExportApplyCard']>
    readonly fetchExportInsideCard: UnwrapRef<typeof import('../service/api/rules')['fetchExportInsideCard']>
    readonly fetchExportJqCard: UnwrapRef<typeof import('../service/api/rules')['fetchExportJqCard']>
    readonly fetchExportProductCard: UnwrapRef<typeof import('../service/api/rules')['fetchExportProductCard']>
    readonly fetchFactoryAdd: UnwrapRef<typeof import('../service/api/rules')['fetchFactoryAdd']>
    readonly fetchFactoryAudit: UnwrapRef<typeof import('../service/api/rules')['fetchFactoryAudit']>
    readonly fetchFactoryDelete: UnwrapRef<typeof import('../service/api/rules')['fetchFactoryDelete']>
    readonly fetchFactoryHandle: UnwrapRef<typeof import('../service/api/rules')['fetchFactoryHandle']>
    readonly fetchFactoryQuery: UnwrapRef<typeof import('../service/api/rules')['fetchFactoryQuery']>
    readonly fetchFactoryUpdate: UnwrapRef<typeof import('../service/api/rules')['fetchFactoryUpdate']>
    readonly fetchFileCentreDownload: UnwrapRef<typeof import('../service/api/common')['fetchFileCentreDownload']>
    readonly fetchFlowQueryAuditInfo: UnwrapRef<typeof import('../service/api/common')['fetchFlowQueryAuditInfo']>
    readonly fetchFlowWithdraw: UnwrapRef<typeof import('../service/api/common')['fetchFlowWithdraw']>
    readonly fetchGetCheckApplyById: UnwrapRef<typeof import('../service/api/rules')['fetchGetCheckApplyById']>
    readonly fetchGetCheckCardById: UnwrapRef<typeof import('../service/api/rules')['fetchGetCheckCardById']>
    readonly fetchGetCompanyApproveDetailById: UnwrapRef<typeof import('../service/api/supplier')['fetchGetCompanyApproveDetailById']>
    readonly fetchGetCompanyById: UnwrapRef<typeof import('../service/api/supplier')['fetchGetCompanyById']>
    readonly fetchGetCompanyCertificateById: UnwrapRef<typeof import('../service/api/supplier')['fetchGetCompanyCertificateById']>
    readonly fetchGetCompanyQualificateById: UnwrapRef<typeof import('../service/api/supplier')['fetchGetCompanyQualificateById']>
    readonly fetchGetElementById: UnwrapRef<typeof import('../service/api/settings')['fetchGetElementById']>
    readonly fetchGetOrgCertificateById: UnwrapRef<typeof import('../service/api/organiza')['fetchGetOrgCertificateById']>
    readonly fetchGetOrgQualificateById: UnwrapRef<typeof import('../service/api/organiza')['fetchGetOrgQualificateById']>
    readonly fetchGetOrgUserById: UnwrapRef<typeof import('../service/api/organiza')['fetchGetOrgUserById']>
    readonly fetchGetOrganiza: UnwrapRef<typeof import('../service/api/organiza')['fetchGetOrganiza']>
    readonly fetchGetRoleById: UnwrapRef<typeof import('../service/api/settings')['fetchGetRoleById']>
    readonly fetchGetRsaPublicKey: UnwrapRef<typeof import('../service/api/auth')['fetchGetRsaPublicKey']>
    readonly fetchGetUserRoutes: UnwrapRef<typeof import('../service/api/route')['fetchGetUserRoutes']>
    readonly fetchInsertCheck: UnwrapRef<typeof import('../service/api/rules')['fetchInsertCheck']>
    readonly fetchInsertProduct: UnwrapRef<typeof import('../service/api/rules')['fetchInsertProduct']>
    readonly fetchIsRouteExist: UnwrapRef<typeof import('../service/api/route')['fetchIsRouteExist']>
    readonly fetchLogin: UnwrapRef<typeof import('../service/api/auth')['fetchLogin']>
    readonly fetchLogout: UnwrapRef<typeof import('../service/api/auth')['fetchLogout']>
    readonly fetchManageApiAttachmentAdd: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAttachmentAdd']>
    readonly fetchManageApiAttachmentDelete: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAttachmentDelete']>
    readonly fetchManageApiAttachmentDeleteById: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAttachmentDeleteById']>
    readonly fetchManageApiAttachmentDownloadFileByUrl: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAttachmentDownloadFileByUrl']>
    readonly fetchManageApiAttachmentQuery: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAttachmentQuery']>
    readonly fetchManageApiAttachmentUpdate: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAttachmentUpdate']>
    readonly fetchManageApiAttachmentUpload: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAttachmentUpload']>
    readonly fetchManageApiAttachmentUploadByteArrays: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAttachmentUploadByteArrays']>
    readonly fetchManageApiAuditAuditCredit: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAuditAuditCredit']>
    readonly fetchManageApiAuditAuditGuarantee: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAuditAuditGuarantee']>
    readonly fetchManageApiAuditBatchAuditGuarantee: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAuditBatchAuditGuarantee']>
    readonly fetchManageApiAuditKill: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAuditKill']>
    readonly fetchManageApiAuditRecall: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAuditRecall']>
    readonly fetchManageApiAuditSubmitCredit: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAuditSubmitCredit']>
    readonly fetchManageApiAuditSubmitGuarantee: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAuditSubmitGuarantee']>
    readonly fetchManageApiAuthLoginManage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAuthLoginManage']>
    readonly fetchManageApiAuthLogout: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAuthLogout']>
    readonly fetchManageApiAuthSendCode: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiAuthSendCode']>
    readonly fetchManageApiConfigAdd: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiConfigAdd']>
    readonly fetchManageApiConfigEdit: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiConfigEdit']>
    readonly fetchManageApiConfigFusing: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiConfigFusing']>
    readonly fetchManageApiConfigQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiConfigQueryPage']>
    readonly fetchManageApiConfigQueryYearCount: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiConfigQueryYearCount']>
    readonly fetchManageApiDashboardQueryDashboard: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiDashboardQueryDashboard']>
    readonly fetchManageApiDashboardQueryReportList: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiDashboardQueryReportList']>
    readonly fetchManageApiDictDelete: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiDictDelete']>
    readonly fetchManageApiDictDetail: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiDictDetail']>
    readonly fetchManageApiDictQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiDictQueryPage']>
    readonly fetchManageApiDictUpdate: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiDictUpdate']>
    readonly fetchManageApiElementAddElement: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiElementAddElement']>
    readonly fetchManageApiElementDeleteElement: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiElementDeleteElement']>
    readonly fetchManageApiElementGetElement: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiElementGetElement']>
    readonly fetchManageApiElementQueryApiList: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiElementQueryApiList']>
    readonly fetchManageApiElementQueryElementList: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiElementQueryElementList']>
    readonly fetchManageApiElementSortElement: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiElementSortElement']>
    readonly fetchManageApiElementUpdateElement: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiElementUpdateElement']>
    readonly fetchManageApiExportConfigAddOrUpdate: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiExportConfigAddOrUpdate']>
    readonly fetchManageApiExportConfigGetInfo: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiExportConfigGetInfo']>
    readonly fetchManageApiExportLogDeleteLog: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiExportLogDeleteLog']>
    readonly fetchManageApiExportLogQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiExportLogQueryPage']>
    readonly fetchManageApiFlowDefinitionActiveDefinitionId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionActiveDefinitionId']>
    readonly fetchManageApiFlowDefinitionAdd: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionAdd']>
    readonly fetchManageApiFlowDefinitionBatchRemoveIds: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionBatchRemoveIds']>
    readonly fetchManageApiFlowDefinitionCopyDefId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionCopyDefId']>
    readonly fetchManageApiFlowDefinitionEdit: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionEdit']>
    readonly fetchManageApiFlowDefinitionFlowChartInstanceId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionFlowChartInstanceId']>
    readonly fetchManageApiFlowDefinitionFlowChartNoColorInstanceId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionFlowChartNoColorInstanceId']>
    readonly fetchManageApiFlowDefinitionGetInfoId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionGetInfoId']>
    readonly fetchManageApiFlowDefinitionImportDefinition: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionImportDefinition']>
    readonly fetchManageApiFlowDefinitionList: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionList']>
    readonly fetchManageApiFlowDefinitionPublishId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionPublishId']>
    readonly fetchManageApiFlowDefinitionRemoveId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionRemoveId']>
    readonly fetchManageApiFlowDefinitionUnActiveDefinitionId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionUnActiveDefinitionId']>
    readonly fetchManageApiFlowDefinitionUnPublishId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowDefinitionUnPublishId']>
    readonly fetchManageApiFlowExecuteActiveInstanceId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteActiveInstanceId']>
    readonly fetchManageApiFlowExecuteAnyNodeListInstanceId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteAnyNodeListInstanceId']>
    readonly fetchManageApiFlowExecuteCopyPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteCopyPage']>
    readonly fetchManageApiFlowExecuteCountDoneEveryMonth: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteCountDoneEveryMonth']>
    readonly fetchManageApiFlowExecuteCountDoneThisMonthAndToday: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteCountDoneThisMonthAndToday']>
    readonly fetchManageApiFlowExecuteDoneListInstanceId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteDoneListInstanceId']>
    readonly fetchManageApiFlowExecuteDonePage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteDonePage']>
    readonly fetchManageApiFlowExecuteGetBackNodeListTaskId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteGetBackNodeListTaskId']>
    readonly fetchManageApiFlowExecuteGetTaskByIdTaskId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteGetTaskByIdTaskId']>
    readonly fetchManageApiFlowExecuteIdReverseDisplayNameIds: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteIdReverseDisplayNameIds']>
    readonly fetchManageApiFlowExecuteInteractiveType: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteInteractiveType']>
    readonly fetchManageApiFlowExecuteToDoPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteToDoPage']>
    readonly fetchManageApiFlowExecuteUnActiveInstanceId: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiFlowExecuteUnActiveInstanceId']>
    readonly fetchManageApiLoanRateAdd: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanRateAdd']>
    readonly fetchManageApiLoanRateDelete: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanRateDelete']>
    readonly fetchManageApiLoanRateEdit: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanRateEdit']>
    readonly fetchManageApiLoanRateQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanRateQueryPage']>
    readonly fetchManageApiLoanStatisticQueryPageByBank: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByBank']>
    readonly fetchManageApiLoanStatisticQueryPageByBankExport: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByBankExport']>
    readonly fetchManageApiLoanStatisticQueryPageByBankRegion: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByBankRegion']>
    readonly fetchManageApiLoanStatisticQueryPageByBankRegionExport: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByBankRegionExport']>
    readonly fetchManageApiLoanStatisticQueryPageByEnt: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByEnt']>
    readonly fetchManageApiLoanStatisticQueryPageByEntExport: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByEntExport']>
    readonly fetchManageApiLoanStatisticQueryPageByRegion: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegion']>
    readonly fetchManageApiLoanStatisticQueryPageByRegionExport: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegionExport']>
    readonly fetchManageApiLoanStatisticQueryPageByRegionOrg: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegionOrg']>
    readonly fetchManageApiLoanStatisticQueryPageByRegionOrgEnt: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegionOrgEnt']>
    readonly fetchManageApiLoanStatisticQueryPageByRegionOrgEntExport: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegionOrgEntExport']>
    readonly fetchManageApiLoanStatisticQueryPageByRegionOrgExport: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanStatisticQueryPageByRegionOrgExport']>
    readonly fetchManageApiLoanTaskManageAdd: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanTaskManageAdd']>
    readonly fetchManageApiLoanTaskManageDelete: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanTaskManageDelete']>
    readonly fetchManageApiLoanTaskManageGet: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanTaskManageGet']>
    readonly fetchManageApiLoanTaskManageQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanTaskManageQueryPage']>
    readonly fetchManageApiLoanTaskManageUpdate: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiLoanTaskManageUpdate']>
    readonly fetchManageApiNoticeBatchRead: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiNoticeBatchRead']>
    readonly fetchManageApiNoticeQueryDetail: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiNoticeQueryDetail']>
    readonly fetchManageApiNoticeQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiNoticeQueryPage']>
    readonly fetchManageApiNoticeQueryUnReadCount: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiNoticeQueryUnReadCount']>
    readonly fetchManageApiNoticeRead: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiNoticeRead']>
    readonly fetchManageApiOperateLogOperateLogBackup: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOperateLogOperateLogBackup']>
    readonly fetchManageApiOperateLogOperateLogRecover: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOperateLogOperateLogRecover']>
    readonly fetchManageApiOperateLogQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOperateLogQueryPage']>
    readonly fetchManageApiOrgImport: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrgImport']>
    readonly fetchManageApiOrgQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrgQueryPage']>
    readonly fetchManageApiOrgRemove: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrgRemove']>
    readonly fetchManageApiOrgSave: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrgSave']>
    readonly fetchManageApiOrgUpdate: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrgUpdate']>
    readonly fetchManageApiOrganizationAdd: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrganizationAdd']>
    readonly fetchManageApiOrganizationDelete: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrganizationDelete']>
    readonly fetchManageApiOrganizationDetail: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrganizationDetail']>
    readonly fetchManageApiOrganizationGetOrganizationTree: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrganizationGetOrganizationTree']>
    readonly fetchManageApiOrganizationQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrganizationQueryPage']>
    readonly fetchManageApiOrganizationUpdate: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiOrganizationUpdate']>
    readonly fetchManageApiRecordAdd: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRecordAdd']>
    readonly fetchManageApiRecordChangeBatchStatus: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRecordChangeBatchStatus']>
    readonly fetchManageApiRecordDelete: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRecordDelete']>
    readonly fetchManageApiRecordEdit: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRecordEdit']>
    readonly fetchManageApiRecordQueryDetail: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRecordQueryDetail']>
    readonly fetchManageApiRecordQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRecordQueryPage']>
    readonly fetchManageApiRecordQueryRestAmount: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRecordQueryRestAmount']>
    readonly fetchManageApiRecordRecordApply: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRecordRecordApply']>
    readonly fetchManageApiRiskAddRisk: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskAddRisk']>
    readonly fetchManageApiRiskEdit: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskEdit']>
    readonly fetchManageApiRiskPaid: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskPaid']>
    readonly fetchManageApiRiskQueryDetail: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskQueryDetail']>
    readonly fetchManageApiRiskQueryDoneList: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskQueryDoneList']>
    readonly fetchManageApiRiskQueryHisTask: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskQueryHisTask']>
    readonly fetchManageApiRiskQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskQueryPage']>
    readonly fetchManageApiRiskQueryTodoList: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskQueryTodoList']>
    readonly fetchManageApiRiskRemoveRiskAudit: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskRemoveRiskAudit']>
    readonly fetchManageApiRiskStatisticQueryPageByBank: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskStatisticQueryPageByBank']>
    readonly fetchManageApiRiskStatisticQueryPageByBankExport: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskStatisticQueryPageByBankExport']>
    readonly fetchManageApiRiskStatisticQueryPageByRegion: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskStatisticQueryPageByRegion']>
    readonly fetchManageApiRiskStatisticQueryPageByRegionExport: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskStatisticQueryPageByRegionExport']>
    readonly fetchManageApiRiskSubmit: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRiskSubmit']>
    readonly fetchManageApiRoleAddRole: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRoleAddRole']>
    readonly fetchManageApiRoleDeleteRole: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRoleDeleteRole']>
    readonly fetchManageApiRoleGetRole: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRoleGetRole']>
    readonly fetchManageApiRoleQueryRoleList: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRoleQueryRoleList']>
    readonly fetchManageApiRoleQueryRolePage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRoleQueryRolePage']>
    readonly fetchManageApiRoleUpdateRole: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiRoleUpdateRole']>
    readonly fetchManageApiSciEntAdd: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntAdd']>
    readonly fetchManageApiSciEntBatchUpdate: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntBatchUpdate']>
    readonly fetchManageApiSciEntCalcByYear: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntCalcByYear']>
    readonly fetchManageApiSciEntDownloadTemplate: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntDownloadTemplate']>
    readonly fetchManageApiSciEntImportData: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntImportData']>
    readonly fetchManageApiSciEntQueryDetail: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntQueryDetail']>
    readonly fetchManageApiSciEntQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntQueryPage']>
    readonly fetchManageApiSciEntRemove: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntRemove']>
    readonly fetchManageApiSciEntSyncEntData: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntSyncEntData']>
    readonly fetchManageApiSciEntSyncRecordData: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntSyncRecordData']>
    readonly fetchManageApiSciEntUpdate: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntUpdate']>
    readonly fetchManageApiSciEntUpload: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSciEntUpload']>
    readonly fetchManageApiSecurityGetRsaPublicKey: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSecurityGetRsaPublicKey']>
    readonly fetchManageApiSecurityImportantDataDecrypt: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSecurityImportantDataDecrypt']>
    readonly fetchManageApiSecurityImportantDataEncrypt: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiSecurityImportantDataEncrypt']>
    readonly fetchManageApiTrackTrackRecord: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiTrackTrackRecord']>
    readonly fetchManageApiUserAdd: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiUserAdd']>
    readonly fetchManageApiUserDelete: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiUserDelete']>
    readonly fetchManageApiUserDetail: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiUserDetail']>
    readonly fetchManageApiUserEncryptPhone: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiUserEncryptPhone']>
    readonly fetchManageApiUserQueryList: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiUserQueryList']>
    readonly fetchManageApiUserQueryPage: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiUserQueryPage']>
    readonly fetchManageApiUserUpdate: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiUserUpdate']>
    readonly fetchManageApiUserUpdatePwd: UnwrapRef<typeof import('../service/api/manageApi')['fetchManageApiUserUpdatePwd']>
    readonly fetchMatingAdd: UnwrapRef<typeof import('../service/api/task')['fetchMatingAdd']>
    readonly fetchMatingDelete: UnwrapRef<typeof import('../service/api/task')['fetchMatingDelete']>
    readonly fetchMatingList: UnwrapRef<typeof import('../service/api/task')['fetchMatingList']>
    readonly fetchMatingQuery: UnwrapRef<typeof import('../service/api/task')['fetchMatingQuery']>
    readonly fetchMatingUpdate: UnwrapRef<typeof import('../service/api/task')['fetchMatingUpdate']>
    readonly fetchNodeAdd: UnwrapRef<typeof import('../service/api/task')['fetchNodeAdd']>
    readonly fetchNodeDelete: UnwrapRef<typeof import('../service/api/task')['fetchNodeDelete']>
    readonly fetchNodeList: UnwrapRef<typeof import('../service/api/task')['fetchNodeList']>
    readonly fetchNodeQuery: UnwrapRef<typeof import('../service/api/task')['fetchNodeQuery']>
    readonly fetchNodeUpdate: UnwrapRef<typeof import('../service/api/task')['fetchNodeUpdate']>
    readonly fetchObjByNameAndModelApply: UnwrapRef<typeof import('../service/api/rules')['fetchObjByNameAndModelApply']>
    readonly fetchOperateLogPage: UnwrapRef<typeof import('../service/api/settings')['fetchOperateLogPage']>
    readonly fetchOrgAdd: UnwrapRef<typeof import('../service/api/settings')['fetchOrgAdd']>
    readonly fetchOrgDeleteById: UnwrapRef<typeof import('../service/api/settings')['fetchOrgDeleteById']>
    readonly fetchOrgDetailById: UnwrapRef<typeof import('../service/api/settings')['fetchOrgDetailById']>
    readonly fetchOrgQueryList: UnwrapRef<typeof import('../service/api/settings')['fetchOrgQueryList']>
    readonly fetchOrgQueryOrgCertificate: UnwrapRef<typeof import('../service/api/organiza')['fetchOrgQueryOrgCertificate']>
    readonly fetchOrgQueryOrgQualificate: UnwrapRef<typeof import('../service/api/organiza')['fetchOrgQueryOrgQualificate']>
    readonly fetchOrgQueryOrgUserPage: UnwrapRef<typeof import('../service/api/organiza')['fetchOrgQueryOrgUserPage']>
    readonly fetchOrgUpdate: UnwrapRef<typeof import('../service/api/settings')['fetchOrgUpdate']>
    readonly fetchPerformanceAdd: UnwrapRef<typeof import('../service/api/sale')['fetchPerformanceAdd']>
    readonly fetchPerformanceAddAdvise: UnwrapRef<typeof import('../service/api/sale')['fetchPerformanceAddAdvise']>
    readonly fetchPerformanceDelete: UnwrapRef<typeof import('../service/api/sale')['fetchPerformanceDelete']>
    readonly fetchPerformanceDeleteAdvise: UnwrapRef<typeof import('../service/api/sale')['fetchPerformanceDeleteAdvise']>
    readonly fetchPerformanceList: UnwrapRef<typeof import('../service/api/sale')['fetchPerformanceList']>
    readonly fetchPerformanceQuery: UnwrapRef<typeof import('../service/api/sale')['fetchPerformanceQuery']>
    readonly fetchPerformanceUpdate: UnwrapRef<typeof import('../service/api/sale')['fetchPerformanceUpdate']>
    readonly fetchPerformanceUpdateAdvise: UnwrapRef<typeof import('../service/api/sale')['fetchPerformanceUpdateAdvise']>
    readonly fetchPlanAdd: UnwrapRef<typeof import('../service/api/rules')['fetchPlanAdd']>
    readonly fetchPlanDelete: UnwrapRef<typeof import('../service/api/rules')['fetchPlanDelete']>
    readonly fetchPlanHandle: UnwrapRef<typeof import('../service/api/rules')['fetchPlanHandle']>
    readonly fetchPlanInspectName: UnwrapRef<typeof import('../service/api/rules')['fetchPlanInspectName']>
    readonly fetchPlanList: UnwrapRef<typeof import('../service/api/rules')['fetchPlanList']>
    readonly fetchPlanQuery: UnwrapRef<typeof import('../service/api/rules')['fetchPlanQuery']>
    readonly fetchPlanUpdate: UnwrapRef<typeof import('../service/api/rules')['fetchPlanUpdate']>
    readonly fetchProblemAdd: UnwrapRef<typeof import('../service/api/task')['fetchProblemAdd']>
    readonly fetchProblemDelete: UnwrapRef<typeof import('../service/api/task')['fetchProblemDelete']>
    readonly fetchProblemHandle: UnwrapRef<typeof import('../service/api/task')['fetchProblemHandle']>
    readonly fetchProblemList: UnwrapRef<typeof import('../service/api/task')['fetchProblemList']>
    readonly fetchProblemOver: UnwrapRef<typeof import('../service/api/task')['fetchProblemOver']>
    readonly fetchProblemQuery: UnwrapRef<typeof import('../service/api/task')['fetchProblemQuery']>
    readonly fetchProblemUpdate: UnwrapRef<typeof import('../service/api/task')['fetchProblemUpdate']>
    readonly fetchProcessesBalance: UnwrapRef<typeof import('../service/api/rules')['fetchProcessesBalance']>
    readonly fetchPumpCheckPart: UnwrapRef<typeof import('../service/api/rules')['fetchPumpCheckPart']>
    readonly fetchQueryApiList: UnwrapRef<typeof import('../service/api/settings')['fetchQueryApiList']>
    readonly fetchQueryCertificateDetail: UnwrapRef<typeof import('../service/api/rules')['fetchQueryCertificateDetail']>
    readonly fetchQueryCertificatePage: UnwrapRef<typeof import('../service/api/rules')['fetchQueryCertificatePage']>
    readonly fetchQueryCheck: UnwrapRef<typeof import('../service/api/rules')['fetchQueryCheck']>
    readonly fetchQueryCheckApplyPage: UnwrapRef<typeof import('../service/api/rules')['fetchQueryCheckApplyPage']>
    readonly fetchQueryCheckCardApplyPage: UnwrapRef<typeof import('../service/api/rules')['fetchQueryCheckCardApplyPage']>
    readonly fetchQueryCheckCardPage: UnwrapRef<typeof import('../service/api/rules')['fetchQueryCheckCardPage']>
    readonly fetchQueryCheckDetail: UnwrapRef<typeof import('../service/api/rules')['fetchQueryCheckDetail']>
    readonly fetchQueryCheckDetails: UnwrapRef<typeof import('../service/api/rules')['fetchQueryCheckDetails']>
    readonly fetchQueryCheckFactoryPage: UnwrapRef<typeof import('../service/api/rules')['fetchQueryCheckFactoryPage']>
    readonly fetchQueryCheckVersion: UnwrapRef<typeof import('../service/api/rules')['fetchQueryCheckVersion']>
    readonly fetchQueryCompanyCertificateList: UnwrapRef<typeof import('../service/api/supplier')['fetchQueryCompanyCertificateList']>
    readonly fetchQueryCompanyCertificatePage: UnwrapRef<typeof import('../service/api/supplier')['fetchQueryCompanyCertificatePage']>
    readonly fetchQueryCompanyPage: UnwrapRef<typeof import('../service/api/supplier')['fetchQueryCompanyPage']>
    readonly fetchQueryCompanyQualificatePage: UnwrapRef<typeof import('../service/api/supplier')['fetchQueryCompanyQualificatePage']>
    readonly fetchQueryContract: UnwrapRef<typeof import('../service/api/task')['fetchQueryContract']>
    readonly fetchQueryContractProjectList: UnwrapRef<typeof import('../service/api/contractReserve')['fetchQueryContractProjectList']>
    readonly fetchQueryDictPage: UnwrapRef<typeof import('../service/api/settings')['fetchQueryDictPage']>
    readonly fetchQueryElementList: UnwrapRef<typeof import('../service/api/settings')['fetchQueryElementList']>
    readonly fetchQueryIntegrityCompanyPage: UnwrapRef<typeof import('../service/api/supplier')['fetchQueryIntegrityCompanyPage']>
    readonly fetchQueryList: UnwrapRef<typeof import('../service/api/task')['fetchQueryList']>
    readonly fetchQueryOperateLogPage: UnwrapRef<typeof import('../service/api/settings')['fetchQueryOperateLogPage']>
    readonly fetchQueryPageList: UnwrapRef<typeof import('../service/api/task')['fetchQueryPageList']>
    readonly fetchQueryRoleList: UnwrapRef<typeof import('../service/api/settings')['fetchQueryRoleList']>
    readonly fetchQueryRolePage: UnwrapRef<typeof import('../service/api/settings')['fetchQueryRolePage']>
    readonly fetchQueryTask: UnwrapRef<typeof import('../service/api/task')['fetchQueryTask']>
    readonly fetchResetPwd: UnwrapRef<typeof import('../service/api/auth')['fetchResetPwd']>
    readonly fetchRiskAdd: UnwrapRef<typeof import('../service/api/task')['fetchRiskAdd']>
    readonly fetchRiskDel: UnwrapRef<typeof import('../service/api/task')['fetchRiskDel']>
    readonly fetchRiskDelete: UnwrapRef<typeof import('../service/api/task')['fetchRiskDelete']>
    readonly fetchRiskHandle: UnwrapRef<typeof import('../service/api/task')['fetchRiskHandle']>
    readonly fetchRiskList: UnwrapRef<typeof import('../service/api/task')['fetchRiskList']>
    readonly fetchRiskQuery: UnwrapRef<typeof import('../service/api/task')['fetchRiskQuery']>
    readonly fetchRiskUpdate: UnwrapRef<typeof import('../service/api/task')['fetchRiskUpdate']>
    readonly fetchSaveDelete: UnwrapRef<typeof import('../service/api/task')['fetchSaveDelete']>
    readonly fetchSaveQuality: UnwrapRef<typeof import('../service/api/task')['fetchSaveQuality']>
    readonly fetchSaveQualityHandle: UnwrapRef<typeof import('../service/api/task')['fetchSaveQualityHandle']>
    readonly fetchSaveQuery: UnwrapRef<typeof import('../service/api/task')['fetchSaveQuery']>
    readonly fetchSaveUpdate: UnwrapRef<typeof import('../service/api/task')['fetchSaveUpdate']>
    readonly fetchSendCode: UnwrapRef<typeof import('../service/api/auth')['fetchSendCode']>
    readonly fetchShippingAdd: UnwrapRef<typeof import('../service/api/rules')['fetchShippingAdd']>
    readonly fetchShippingAudit: UnwrapRef<typeof import('../service/api/rules')['fetchShippingAudit']>
    readonly fetchShippingDelete: UnwrapRef<typeof import('../service/api/rules')['fetchShippingDelete']>
    readonly fetchShippingHandle: UnwrapRef<typeof import('../service/api/rules')['fetchShippingHandle']>
    readonly fetchShippingList: UnwrapRef<typeof import('../service/api/rules')['fetchShippingList']>
    readonly fetchShippingQuery: UnwrapRef<typeof import('../service/api/rules')['fetchShippingQuery']>
    readonly fetchShippingUpdate: UnwrapRef<typeof import('../service/api/rules')['fetchShippingUpdate']>
    readonly fetchSortElement: UnwrapRef<typeof import('../service/api/settings')['fetchSortElement']>
    readonly fetchStateChangeAdd: UnwrapRef<typeof import('../service/api/task')['fetchStateChangeAdd']>
    readonly fetchStateChangeDelete: UnwrapRef<typeof import('../service/api/task')['fetchStateChangeDelete']>
    readonly fetchStateChangeQuery: UnwrapRef<typeof import('../service/api/task')['fetchStateChangeQuery']>
    readonly fetchStateChangeUpdate: UnwrapRef<typeof import('../service/api/task')['fetchStateChangeUpdate']>
    readonly fetchSuperviseAdd: UnwrapRef<typeof import('../service/api/task')['fetchSuperviseAdd']>
    readonly fetchSuperviseDelete: UnwrapRef<typeof import('../service/api/task')['fetchSuperviseDelete']>
    readonly fetchSuperviseList: UnwrapRef<typeof import('../service/api/task')['fetchSuperviseList']>
    readonly fetchSuperviseQuery: UnwrapRef<typeof import('../service/api/task')['fetchSuperviseQuery']>
    readonly fetchSuperviseUpdateFlow: UnwrapRef<typeof import('../service/api/task')['fetchSuperviseUpdateFlow']>
    readonly fetchUnqualifiedAdd: UnwrapRef<typeof import('../service/api/task')['fetchUnqualifiedAdd']>
    readonly fetchUnqualifiedDelete: UnwrapRef<typeof import('../service/api/task')['fetchUnqualifiedDelete']>
    readonly fetchUnqualifiedHandle: UnwrapRef<typeof import('../service/api/task')['fetchUnqualifiedHandle']>
    readonly fetchUnqualifiedList: UnwrapRef<typeof import('../service/api/task')['fetchUnqualifiedList']>
    readonly fetchUnqualifiedQuery: UnwrapRef<typeof import('../service/api/task')['fetchUnqualifiedQuery']>
    readonly fetchUnqualifiedUpdate: UnwrapRef<typeof import('../service/api/task')['fetchUnqualifiedUpdate']>
    readonly fetchUpdateCheckApply: UnwrapRef<typeof import('../service/api/rules')['fetchUpdateCheckApply']>
    readonly fetchUpdateCompany: UnwrapRef<typeof import('../service/api/supplier')['fetchUpdateCompany']>
    readonly fetchUpdateCompanyCertificate: UnwrapRef<typeof import('../service/api/supplier')['fetchUpdateCompanyCertificate']>
    readonly fetchUpdateCompanyQualificate: UnwrapRef<typeof import('../service/api/supplier')['fetchUpdateCompanyQualificate']>
    readonly fetchUpdateElement: UnwrapRef<typeof import('../service/api/settings')['fetchUpdateElement']>
    readonly fetchUpdateInpect: UnwrapRef<typeof import('../service/api/rules')['fetchUpdateInpect']>
    readonly fetchUpdateIntegrityCompany: UnwrapRef<typeof import('../service/api/supplier')['fetchUpdateIntegrityCompany']>
    readonly fetchUpdateOrgCertificate: UnwrapRef<typeof import('../service/api/organiza')['fetchUpdateOrgCertificate']>
    readonly fetchUpdateOrgQualificate: UnwrapRef<typeof import('../service/api/organiza')['fetchUpdateOrgQualificate']>
    readonly fetchUpdateOrgUser: UnwrapRef<typeof import('../service/api/organiza')['fetchUpdateOrgUser']>
    readonly fetchUpdateOrganiza: UnwrapRef<typeof import('../service/api/organiza')['fetchUpdateOrganiza']>
    readonly fetchUpdateProduct: UnwrapRef<typeof import('../service/api/rules')['fetchUpdateProduct']>
    readonly fetchUpdatePwd: UnwrapRef<typeof import('../service/api/auth')['fetchUpdatePwd']>
    readonly fetchUpdateRole: UnwrapRef<typeof import('../service/api/settings')['fetchUpdateRole']>
    readonly fetchUserAdd: UnwrapRef<typeof import('../service/api/settings')['fetchUserAdd']>
    readonly fetchUserDelete: UnwrapRef<typeof import('../service/api/settings')['fetchUserDelete']>
    readonly fetchUserDetailById: UnwrapRef<typeof import('../service/api/settings')['fetchUserDetailById']>
    readonly fetchUserImportExcel: UnwrapRef<typeof import('../service/api/common')['fetchUserImportExcel']>
    readonly fetchUserList: UnwrapRef<typeof import('../service/api/settings')['fetchUserList']>
    readonly fetchUserQueryPage: UnwrapRef<typeof import('../service/api/settings')['fetchUserQueryPage']>
    readonly fetchUserUpdate: UnwrapRef<typeof import('../service/api/settings')['fetchUserUpdate']>
    readonly fetchUserUpdateStatus: UnwrapRef<typeof import('../service/api/settings')['fetchUserUpdateStatus']>
    readonly fetchWorkOrderNoProApply: UnwrapRef<typeof import('../service/api/rules')['fetchWorkOrderNoProApply']>
    readonly fileDeleteById: UnwrapRef<typeof import('../service/api/common')['fileDeleteById']>
    readonly flowChartNoColor: UnwrapRef<typeof import('../service/api/contractReserve')['flowChartNoColor']>
    readonly flowchart: UnwrapRef<typeof import('../service/api/contractReserve')['flowchart']>
    readonly getActivePinia: UnwrapRef<typeof import('pinia')['getActivePinia']>
    readonly getArchives: UnwrapRef<typeof import('../service/api/filedataBase')['getArchives']>
    readonly getBackNodeList: UnwrapRef<typeof import('../service/api/contractReserve')['getBackNodeList']>
    readonly getCompany: UnwrapRef<typeof import('../service/api/contractReserve')['getCompany']>
    readonly getCompanyTask: UnwrapRef<typeof import('../service/api/contractReserve')['getCompanyTask']>
    readonly getContract: UnwrapRef<typeof import('../service/api/contractReserve')['getContract']>
    readonly getContractManufacture: UnwrapRef<typeof import('../service/api/contractReserve')['getContractManufacture']>
    readonly getContractManufacturePage: UnwrapRef<typeof import('../service/api/contractReserve')['getContractManufacturePage']>
    readonly getContractMaterial: UnwrapRef<typeof import('../service/api/contractReserve')['getContractMaterial']>
    readonly getContractMatter: UnwrapRef<typeof import('../service/api/contractReserve')['getContractMatter']>
    readonly getContractProject: UnwrapRef<typeof import('../service/api/contractReserve')['getContractProject']>
    readonly getContractProjectInfo: UnwrapRef<typeof import('../service/api/contractReserve')['getContractProjectInfo']>
    readonly getContractProjectPage: UnwrapRef<typeof import('../service/api/contractReserve')['getContractProjectPage']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getFlowTask: UnwrapRef<typeof import('../service/api/contractReserve')['getFlowTask']>
    readonly getHydrostaticTest: UnwrapRef<typeof import('../service/api/rules')['getHydrostaticTest']>
    readonly getMaterialWarehouse: UnwrapRef<typeof import('../service/api/contractReserve')['getMaterialWarehouse']>
    readonly getOrgAttachment: UnwrapRef<typeof import('../service/api/organiza')['getOrgAttachment']>
    readonly getOrgCertificateStatus: UnwrapRef<typeof import('../service/api/contractReserve')['getOrgCertificateStatus']>
    readonly getOrgUser: UnwrapRef<typeof import('../service/api/contractReserve')['getOrgUser']>
    readonly getOrganiza: UnwrapRef<typeof import('../service/api/contractReserve')['getOrganiza']>
    readonly getOrganizeTask: UnwrapRef<typeof import('../service/api/contractReserve')['getOrganizeTask']>
    readonly getPayment: UnwrapRef<typeof import('../service/api/contractReserve')['getPayment']>
    readonly getPerformancePlan: UnwrapRef<typeof import('../service/api/contractReserve')['getPerformancePlan']>
    readonly getProcessesPart: UnwrapRef<typeof import('../service/api/rules')['getProcessesPart']>
    readonly getProduct: UnwrapRef<typeof import('../service/api/contractReserve')['getProduct']>
    readonly getProductForParts: UnwrapRef<typeof import('../service/api/contractReserve')['getProductForParts']>
    readonly getProductTaskById: UnwrapRef<typeof import('../service/api/contractReserve')['getProductTaskById']>
    readonly getProductTaskCode: UnwrapRef<typeof import('../service/api/contractReserve')['getProductTaskCode']>
    readonly getProgressInformation: UnwrapRef<typeof import('../service/api/contractReserve')['getProgressInformation']>
    readonly getProject: UnwrapRef<typeof import('../service/api/contractReserve')['getProject']>
    readonly getProjectEndAndParts: UnwrapRef<typeof import('../service/api/contractReserve')['getProjectEndAndParts']>
    readonly getProjectPlannerDetail: UnwrapRef<typeof import('../service/api/contractReserve')['getProjectPlannerDetail']>
    readonly getProjectPlannerPage: UnwrapRef<typeof import('../service/api/contractReserve')['getProjectPlannerPage']>
    readonly getPurchaseCode: UnwrapRef<typeof import('../service/api/contractReserve')['getPurchaseCode']>
    readonly getPurchaseContract: UnwrapRef<typeof import('../service/api/contractReserve')['getPurchaseContract']>
    readonly getPurchaseContractById: UnwrapRef<typeof import('../service/api/contractReserve')['getPurchaseContractById']>
    readonly getPurchaseContractList: UnwrapRef<typeof import('../service/api/contractReserve')['getPurchaseContractList']>
    readonly getPurchaseContractPage: UnwrapRef<typeof import('../service/api/contractReserve')['getPurchaseContractPage']>
    readonly getPurchasePartsById: UnwrapRef<typeof import('../service/api/contractReserve')['getPurchasePartsById']>
    readonly getPurchasePlan: UnwrapRef<typeof import('../service/api/contractReserve')['getPurchasePlan']>
    readonly getPurchasePlanPage: UnwrapRef<typeof import('../service/api/contractReserve')['getPurchasePlanPage']>
    readonly getPurchaseUser: UnwrapRef<typeof import('../service/api/contractReserve')['getPurchaseUser']>
    readonly getStrCode: UnwrapRef<typeof import('../service/api/contractReserve')['getStrCode']>
    readonly getTaskContractProject: UnwrapRef<typeof import('../service/api/contractReserve')['getTaskContractProject']>
    readonly getTaskProject: UnwrapRef<typeof import('../service/api/contractReserve')['getTaskProject']>
    readonly getUnqualifiedList: UnwrapRef<typeof import('../service/api/rules')['getUnqualifiedList']>
    readonly getWarehouseProduct: UnwrapRef<typeof import('../service/api/filedataBase')['getWarehouseProduct']>
    readonly getcompanContract: UnwrapRef<typeof import('../service/api/contractReserve')['getcompanContract']>
    readonly getdoneList: UnwrapRef<typeof import('../service/api/contractReserve')['getdoneList']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly handlerProjectPlanner: UnwrapRef<typeof import('../service/api/contractReserve')['handlerProjectPlanner']>
    readonly importCompany: UnwrapRef<typeof import('../service/api/supplier')['importCompany']>
    readonly importEndProduct: UnwrapRef<typeof import('../service/api/contractReserve')['importEndProduct']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly mapActions: UnwrapRef<typeof import('pinia')['mapActions']>
    readonly mapGetters: UnwrapRef<typeof import('pinia')['mapGetters']>
    readonly mapState: UnwrapRef<typeof import('pinia')['mapState']>
    readonly mapStores: UnwrapRef<typeof import('pinia')['mapStores']>
    readonly mapWritableState: UnwrapRef<typeof import('pinia')['mapWritableState']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly mesContract: UnwrapRef<typeof import('../service/api/contractReserve')['mesContract']>
    readonly mesPickLibrary: UnwrapRef<typeof import('../service/api/contractReserve')['mesPickLibrary']>
    readonly mesProductTask: UnwrapRef<typeof import('../service/api/contractReserve')['mesProductTask']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly onWatcherCleanup: UnwrapRef<typeof import('vue')['onWatcherCleanup']>
    readonly productPage: UnwrapRef<typeof import('../service/api/filedataBase')['productPage']>
    readonly productTaskhandle: UnwrapRef<typeof import('../service/api/contractReserve')['productTaskhandle']>
    readonly productTaskqueryList: UnwrapRef<typeof import('../service/api/contractReserve')['productTaskqueryList']>
    readonly projectCreateProjectCode: UnwrapRef<typeof import('../service/api/contractReserve')['projectCreateProjectCode']>
    readonly projectDeleteProject: UnwrapRef<typeof import('../service/api/contractReserve')['projectDeleteProject']>
    readonly projectGetProType: UnwrapRef<typeof import('../service/api/contractReserve')['projectGetProType']>
    readonly projecthandle: UnwrapRef<typeof import('../service/api/contractReserve')['projecthandle']>
    readonly projectupdateProject: UnwrapRef<typeof import('../service/api/contractReserve')['projectupdateProject']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly queryCompanyArchives: UnwrapRef<typeof import('../service/api/filedataBase')['queryCompanyArchives']>
    readonly queryCompanyCertificateYearList: UnwrapRef<typeof import('../service/api/contractReserve')['queryCompanyCertificateYearList']>
    readonly queryContract: UnwrapRef<typeof import('../service/api/contractReserve')['queryContract']>
    readonly queryContractArchivesData: UnwrapRef<typeof import('../service/api/filedataBase')['queryContractArchivesData']>
    readonly queryFileDetails: UnwrapRef<typeof import('../service/api/filedataBase')['queryFileDetails']>
    readonly queryFileList: UnwrapRef<typeof import('../service/api/filedataBase')['queryFileList']>
    readonly queryInspectItems: UnwrapRef<typeof import('../service/api/contractReserve')['queryInspectItems']>
    readonly queryLatestProductName: UnwrapRef<typeof import('../service/api/contractReserve')['queryLatestProductName']>
    readonly queryMaterialPageList: UnwrapRef<typeof import('../service/api/contractReserve')['queryMaterialPageList']>
    readonly queryMaterials: UnwrapRef<typeof import('../service/api/filedataBase')['queryMaterials']>
    readonly queryOrgCertificateYearList: UnwrapRef<typeof import('../service/api/contractReserve')['queryOrgCertificateYearList']>
    readonly queryPageByBankExport: UnwrapRef<typeof import('../service/api/inventory')['queryPageByBankExport']>
    readonly queryPageByEntExport: UnwrapRef<typeof import('../service/api/inventory')['queryPageByEntExport']>
    readonly queryPageByRegionExport: UnwrapRef<typeof import('../service/api/inventory')['queryPageByRegionExport']>
    readonly queryProductFactoryList: UnwrapRef<typeof import('../service/api/filedataBase')['queryProductFactoryList']>
    readonly queryProductMaterials: UnwrapRef<typeof import('../service/api/filedataBase')['queryProductMaterials']>
    readonly queryProductName: UnwrapRef<typeof import('../service/api/contractReserve')['queryProductName']>
    readonly queryProductTaskPage: UnwrapRef<typeof import('../service/api/contractReserve')['queryProductTaskPage']>
    readonly queryProjectPage: UnwrapRef<typeof import('../service/api/contractReserve')['queryProjectPage']>
    readonly queryPurchaseContractByContractProjectId: UnwrapRef<typeof import('../service/api/contractReserve')['queryPurchaseContractByContractProjectId']>
    readonly queryPurchaseContracts: UnwrapRef<typeof import('../service/api/filedataBase')['queryPurchaseContracts']>
    readonly queryTaskPage: UnwrapRef<typeof import('../service/api/contractReserve')['queryTaskPage']>
    readonly queryUnFinishPurchaseContractPage: UnwrapRef<typeof import('../service/api/contractReserve')['queryUnFinishPurchaseContractPage']>
    readonly querycompanyTaskPage: UnwrapRef<typeof import('../service/api/contractReserve')['querycompanyTaskPage']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly reloadMaterialCode: UnwrapRef<typeof import('../service/api/contractReserve')['reloadMaterialCode']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly riskStatisticqueryPageByBankExport: UnwrapRef<typeof import('../service/api/inventory')['riskStatisticqueryPageByBankExport']>
    readonly riskStatisticqueryPageByRegionExport: UnwrapRef<typeof import('../service/api/inventory')['riskStatisticqueryPageByRegionExport']>
    readonly setActivePinia: UnwrapRef<typeof import('pinia')['setActivePinia']>
    readonly setMapStoreSuffix: UnwrapRef<typeof import('pinia')['setMapStoreSuffix']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly stateChange: UnwrapRef<typeof import('../service/api/contractReserve')['stateChange']>
    readonly storeToRefs: UnwrapRef<typeof import('pinia')['storeToRefs']>
    readonly toDoPage: UnwrapRef<typeof import('../service/api/contractReserve')['toDoPage']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly upArchivesDataFile: UnwrapRef<typeof import('../service/api/filedataBase')['upArchivesDataFile']>
    readonly updContractProject: UnwrapRef<typeof import('../service/api/contractReserve')['updContractProject']>
    readonly updateCompanyTask: UnwrapRef<typeof import('../service/api/contractReserve')['updateCompanyTask']>
    readonly updateContractArchivesDataRemark: UnwrapRef<typeof import('../service/api/contractReserve')['updateContractArchivesDataRemark']>
    readonly updateContractManufacture: UnwrapRef<typeof import('../service/api/contractReserve')['updateContractManufacture']>
    readonly updateEndProductByIdsFactory: UnwrapRef<typeof import('../service/api/contractReserve')['updateEndProductByIdsFactory']>
    readonly updateOrganizeTask: UnwrapRef<typeof import('../service/api/contractReserve')['updateOrganizeTask']>
    readonly updatePartsByIdsFactory: UnwrapRef<typeof import('../service/api/contractReserve')['updatePartsByIdsFactory']>
    readonly updateProduct: UnwrapRef<typeof import('../service/api/filedataBase')['updateProduct']>
    readonly updateProductTask: UnwrapRef<typeof import('../service/api/contractReserve')['updateProductTask']>
    readonly updateProjectPlanner: UnwrapRef<typeof import('../service/api/contractReserve')['updateProjectPlanner']>
    readonly updatePurchaseContract: UnwrapRef<typeof import('../service/api/contractReserve')['updatePurchaseContract']>
    readonly updatePurchasePlan: UnwrapRef<typeof import('../service/api/contractReserve')['updatePurchasePlan']>
    readonly updateStatusByIds: UnwrapRef<typeof import('../service/api/contractReserve')['updateStatusByIds']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly useRoute: UnwrapRef<typeof import('vue-router')['useRoute']>
    readonly useRouter: UnwrapRef<typeof import('vue-router')['useRouter']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly vClickOutside: UnwrapRef<typeof import('../globals/directives')['vClickOutside']>
    readonly vFocus: UnwrapRef<typeof import('../globals/directives')['vFocus']>
    readonly vNumberOnly: UnwrapRef<typeof import('../globals/directives')['vNumberOnly']>
    readonly vPermission: UnwrapRef<typeof import('../globals/directives')['vPermission']>
    readonly verificationRecordadd: UnwrapRef<typeof import('../service/api/contractReserve')['verificationRecordadd']>
    readonly verificationRecorddelete: UnwrapRef<typeof import('../service/api/contractReserve')['verificationRecorddelete']>
    readonly verificationRecordqueryDetail: UnwrapRef<typeof import('../service/api/contractReserve')['verificationRecordqueryDetail']>
    readonly verificationRecordqueryPage: UnwrapRef<typeof import('../service/api/contractReserve')['verificationRecordqueryPage']>
    readonly verificationRecordupdate: UnwrapRef<typeof import('../service/api/contractReserve')['verificationRecordupdate']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
    readonly workBenchContract: UnwrapRef<typeof import('../service/api/contractReserve')['workBenchContract']>
    readonly zipFilesWithCommonsCompress: UnwrapRef<typeof import('../service/api/filedataBase')['zipFilesWithCommonsCompress']>
  }
}
// for vue directives auto import
declare module 'vue' {
  interface GlobalComponents {}
  interface ComponentCustomProperties {
    vClickOutside: typeof import('../globals/directives')['vClickOutside']
    vFocus: typeof import('../globals/directives')['vFocus']
    vNumberOnly: typeof import('../globals/directives')['vNumberOnly']
    vPermission: typeof import('../globals/directives')['vPermission']
  }
  interface GlobalDirectives {
    vClickOutside: typeof import('../globals/directives')['vClickOutside']
    vFocus: typeof import('../globals/directives')['vFocus']
    vNumberOnly: typeof import('../globals/directives')['vNumberOnly']
    vPermission: typeof import('../globals/directives')['vPermission']
  }
}