/**
 * 命名空间 Env
 *
 * 用于声明 import.meta 对象的类型
 */
declare namespace Env {
  /** 路由历史模式 */
  type RouterHistoryMode = 'hash' | 'history' | 'memory'

  /** import.meta 的接口定义 */
  interface ImportMeta extends ImportMetaEnv {
    /** 应用程序的基础URL */
    readonly VITE_BASE_URL: string
    /** 应用程序的标题 */
    readonly VITE_APP_TITLE: string
    /** 应用程序的描述 */
    readonly VITE_APP_DESC: string
    /** 路由历史模式 */
    readonly VITE_ROUTER_HISTORY_MODE?: RouterHistoryMode
    /** Iconify 图标的前缀 */
    readonly VITE_ICON_PREFIX: 'icon'
    /**
     * 本地图标的前缀
     *
     * 这个前缀以图标前缀开始
     */
    readonly VITE_ICON_LOCAL_PREFIX: 'local-icon'
    /** 后端服务基础URL */
    readonly VITE_SERVICE_BASE_URL: string
    /**
     * 其他后端服务基础URL
     *
     * 值为 JSON 格式
     */
    readonly VITE_OTHER_SERVICE_BASE_URL: string
    /**
     * 是否启用 HTTP 代理
     *
     * 仅在开发环境中有效
     */
    readonly VITE_HTTP_PROXY?: Common.YesOrNo
    /**
     * 权限路由模式
     *
     * - Static: 权限路由在前端生成
     * - Dynamic: 权限路由在后端生成
     */
    readonly VITE_AUTH_ROUTE_MODE: 'static' | 'dynamic'
    /**
     * 首页路由键
     *
     * 仅在静态权限路由模式下生效，如果路由模式为动态，则首页路由键在后端定义
     */
    readonly VITE_ROUTE_HOME: import('@elegant-router/types').LastLevelRouteKey
    /**
     * 未设置菜单图标时的默认菜单图标
     *
     * Iconify 图标名称
     */
    readonly VITE_MENU_ICON: string
    /** 是否构建 sourcemap */
    readonly VITE_SOURCE_MAP?: Common.YesOrNo
    /**
     * Iconify API 提供者 URL
     *
     * 如果项目部署在内网，可以将 API 提供者 URL 设置为本地 Iconify 服务器
     *
     * @link https://docs.iconify.design/api/providers.html
     */
    readonly VITE_ICONIFY_URL?: string
  }
}
