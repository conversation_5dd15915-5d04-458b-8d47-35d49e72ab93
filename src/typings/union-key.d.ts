/** The union key namespace */
declare namespace UnionKey {
  /**
   * The login module
   *
   * - Pwd-login: password login
   * - Code-login: phone code login
   * - Register: register
   * - Reset-pwd: reset password
   * - Bind-wechat: bind wechat
   */
  type LoginModule = 'pwd-login' | 'code-login' | 'register' | 'reset-pwd' | 'bind-wechat'

  /** Theme scheme */
  type ThemeScheme = 'light' | 'dark' | 'auto'

  /**
   * The layout mode
   *
   * - Vertical: the vertical menu in left
   * - Horizontal: the horizontal menu in top
   * - Vertical-mix: two vertical mixed menus in left
   * - Horizontal-mix: the vertical menu in left and horizontal menu in top
   */
  type ThemeLayoutMode = 'vertical' | 'horizontal' | 'vertical-mix' | 'horizontal-mix'

  /**
   * The scroll mode when content overflow
   *
   * - Wrapper the wrapper component's root element overflow
   * - Content the content component overflow
   */
  type ThemeScrollMode = import('@yc/materials').LayoutScrollMode

  /** Page animate mode */
  type ThemePageAnimateMode = 'fade' | 'fade-slide' | 'fade-bottom' | 'fade-scale' | 'zoom-fade' | 'zoom-out' | 'none'

  /** 元素类型 */
  type ElementType = 'MENU' | 'BUTTON' | 'PAGE'

  /** 平台类型 */
  type PlatformType = 'MANAGE' | 'MANAGE_MP'

  /** 性别 */
  type SexType = 'MALE' | 'FEMALE'

  /** 用户类型 */
  type UserType = 'ADMIN' | 'USER' | 'JUSER'

  /** 日志类型 */
  type LogType = 'BUSINESS' | 'SYSTEM'

  /** 日志类型 */
  type OperTargetType = 'MANAGE_USER' | 'ROLE' | 'ELEMENT'

  /** 贷款备案状态 */
  type loanStatus =
    | 'RECORDED'
    | 'MANUAL_CONFIRMED_EXPIRED'
    | 'TEMP_SAVED'
    | 'SYSTEM_DETERMINED_EXPIRED'
    | 'LOAN_RENEWAL'
    | 'MANUAL_CONFIRMED_REPAID'
    | 'APPLIED'
    | 'APPLIED_TERMINATED'

  /** 贷款备案状态 */
  type loanType = 'GUARANTEE' | 'CREDIT'
  /** 贷款用途 */
  type loanPurpose =
    | 'RND_PRODUCTION_EXPENSE'
    | 'RAW_MATERIALS_PURCHASE'
    | 'SALARY_PAYMENT'
    | 'PROJECT_MARKETING'
    | 'LIQUIDITY_TURNOVER'
    | 'OTHER'
  type Filetype =
    | 'USER_MARK'
    | 'LOAN_CONTRACT'
    | 'LOAN_RECEIPT'
    | 'GUARANTEE_LETTER'
    | 'BANK_PERMISSION_CONTRACT'
    | 'COMPENSATION_APPLICATION_FORM'
    | 'BANK_COLLECTION_EVIDENCE'
    | 'LETTER_OF_COMMITMENT'
    | 'PAYING_RECEIPT_VOUCHER'
    | 'COMPENSATION_NOTICE'
    | 'COMPENSATION_CERTIFICATE'
    | 'COMPENSATION_TRANSACTION_RECORD'
    | 'FUND_TRANSFER_VOUCHER'
    | 'OTHER'

  /** 变更状态 */
  type chagedStatus =
    | 'RECORDED'
    | 'SYSTEM_DETERMINED_EXPIRED'
    | 'TEMP_SAVED'
    | 'LOAN_RENEWAL'
    | 'MANUAL_CONFIRMED_REPAID'
    | 'MANUAL_CONFIRMED_EXPIRED'
    | 'APPLIED_TERMINATED'

  /** 日志类型 */
  type OperType =
    | 'LOGIN'
    | 'LOGOUT'
    | 'ADD'
    | 'UPDATE'
    | 'DELETE'
    | 'IMPORT'
    | 'EXPORT'
    | 'ENABLE'
    | 'DISABLE'
    | 'PUBLISH'
    | 'RESET_PWD'
    | 'QUERY'
    | 'GET'
    | 'SORT'
    | 'AUDIT'
    | 'CANCEL'
    | 'WITHDRAW'
    | 'SIGN'
    | 'UPDATE_PWD'
    | 'SUBMIT'

  /**
   * Tab mode
   *
   * - Chrome: chrome style
   * - Button: button style
   */
  type ThemeTabMode = import('@yc/materials').PageTabMode

  /** Unocss animate key */
  type UnoCssAnimateKey =
    | 'pulse'
    | 'bounce'
    | 'spin'
    | 'ping'
    | 'bounce-alt'
    | 'flash'
    | 'pulse-alt'
    | 'rubber-band'
    | 'shake-x'
    | 'shake-y'
    | 'head-shake'
    | 'swing'
    | 'tada'
    | 'wobble'
    | 'jello'
    | 'heart-beat'
    | 'hinge'
    | 'jack-in-the-box'
    | 'light-speed-in-left'
    | 'light-speed-in-right'
    | 'light-speed-out-left'
    | 'light-speed-out-right'
    | 'flip'
    | 'flip-in-x'
    | 'flip-in-y'
    | 'flip-out-x'
    | 'flip-out-y'
    | 'rotate-in'
    | 'rotate-in-down-left'
    | 'rotate-in-down-right'
    | 'rotate-in-up-left'
    | 'rotate-in-up-right'
    | 'rotate-out'
    | 'rotate-out-down-left'
    | 'rotate-out-down-right'
    | 'rotate-out-up-left'
    | 'rotate-out-up-right'
    | 'roll-in'
    | 'roll-out'
    | 'zoom-in'
    | 'zoom-in-down'
    | 'zoom-in-left'
    | 'zoom-in-right'
    | 'zoom-in-up'
    | 'zoom-out'
    | 'zoom-out-down'
    | 'zoom-out-left'
    | 'zoom-out-right'
    | 'zoom-out-up'
    | 'bounce-in'
    | 'bounce-in-down'
    | 'bounce-in-left'
    | 'bounce-in-right'
    | 'bounce-in-up'
    | 'bounce-out'
    | 'bounce-out-down'
    | 'bounce-out-left'
    | 'bounce-out-right'
    | 'bounce-out-up'
    | 'slide-in-down'
    | 'slide-in-left'
    | 'slide-in-right'
    | 'slide-in-up'
    | 'slide-out-down'
    | 'slide-out-left'
    | 'slide-out-right'
    | 'slide-out-up'
    | 'fade-in'
    | 'fade-in-down'
    | 'fade-in-down-big'
    | 'fade-in-left'
    | 'fade-in-left-big'
    | 'fade-in-right'
    | 'fade-in-right-big'
    | 'fade-in-up'
    | 'fade-in-up-big'
    | 'fade-in-top-left'
    | 'fade-in-top-right'
    | 'fade-in-bottom-left'
    | 'fade-in-bottom-right'
    | 'fade-out'
    | 'fade-out-down'
    | 'fade-out-down-big'
    | 'fade-out-left'
    | 'fade-out-left-big'
    | 'fade-out-right'
    | 'fade-out-right-big'
    | 'fade-out-up'
    | 'fade-out-up-big'
    | 'fade-out-top-left'
    | 'fade-out-top-right'
    | 'fade-out-bottom-left'
    | 'fade-out-bottom-right'
    | 'back-in-up'
    | 'back-in-down'
    | 'back-in-right'
    | 'back-in-left'
    | 'back-out-up'
    | 'back-out-down'
    | 'back-out-right'
    | 'back-out-left'

  /** 业务定义 - 如下 */

  /** 机构类型 */
  type OrgType = 'BANK' | 'GUARANTEE'

  /** 信贷备案审批状态 */
  type CreditAuditStatus =
    | 'FOO'
    | 'TO_BE_SUBMITTED'
    | 'REGIONAL_AUDITING'
    | 'PROVINCIAL_AUDITING'
    | 'AWAITING_VOUCHER_SUPPLEMENT'
    | 'PENDING_PAYMENT'
    | 'PAID'
    | 'TERMINATED'

  /** 担保风补审批状态 */
  type GuaranteeAuditStatus =
    | 'TO_BE_SUBMITTED'
    | 'PROVINCIAL_AUDITING'
    | 'PENDING_PAYMENT'
    | 'PAID'
    | 'TERMINATED'
    | 'FOO'
    | 'REGION_AUDITING'

  /**
   * 通知类型
   */
  type MsgType =
    | 'RISK_COMPENSATION_PROCESS'
    | 'INSTITUTIONAL_EARLY_WARNING_NOTICE'
    | 'INSTITUTIONAL_CIRCUIT_BREAKER_NOTICE'
    | 'REGION_EARLY_WARNING_NOTICE'
    | 'REGION_CIRCUIT_BREAKER_NOTICE'
}
