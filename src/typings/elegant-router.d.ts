/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

declare module "@elegant-router/types" {
  type ElegantConstRoute = import('@elegant-router/vue').ElegantConstRoute;

  /**
   * route layout
   */
  export type RouteLayout = "base" | "blank";

  /**
   * route map
   */
  export type RouteMap = {
    "root": "/";
    "not-found": "/:pathMatch(.*)*";
    "exception": "/exception";
    "exception_403": "/exception/403";
    "exception_404": "/exception/404";
    "exception_500": "/exception/500";
    "403": "/403";
    "404": "/404";
    "500": "/500";
    "board": "/board";
    "board_center": "/board/center";
    "board_left": "/board/left";
    "board_right": "/board/right";
    "creditrecord": "/creditrecord";
    "creditrecord_bank": "/creditrecord/bank";
    "creditrecord_bank_add": "/creditrecord/bank/add";
    "creditrecord_guarantee": "/creditrecord/guarantee";
    "creditrecord_guarantee_add": "/creditrecord/guarantee/add";
    "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?";
    "manage": "/manage";
    "manage_bank": "/manage/bank";
    "manage_bank_add": "/manage/bank/add";
    "manage_bank_list": "/manage/bank/list";
    "manage_config": "/manage/config";
    "manage_dict": "/manage/dict";
    "manage_log": "/manage/log";
    "manage_menu": "/manage/menu";
    "manage_messages": "/manage/messages";
    "manage_organization": "/manage/organization";
    "manage_organization_add": "/manage/organization/add";
    "manage_organization_list": "/manage/organization/list";
    "manage_role": "/manage/role";
    "manage_user": "/manage/user";
    "science": "/science";
    "science_add": "/science/add";
    "science_info": "/science/info";
    "science_list": "/science/list";
    "statistics": "/statistics";
    "statistics_bank-loan": "/statistics/bank-loan";
    "statistics_bank-loan_detail": "/statistics/bank-loan/detail";
    "statistics_bank-risk": "/statistics/bank-risk";
    "statistics_company": "/statistics/company";
    "statistics_regional-loan": "/statistics/regional-loan";
    "statistics_regional-loan_detail": "/statistics/regional-loan/detail";
    "statistics_regional-risk": "/statistics/regional-risk";
    "supplement": "/supplement";
    "supplement_bank": "/supplement/bank";
    "supplement_bank_add": "/supplement/bank/add";
    "supplement_done": "/supplement/done";
    "supplement_done_info": "/supplement/done/info";
    "supplement_guarantee": "/supplement/guarantee";
    "supplement_guarantee_add": "/supplement/guarantee/add";
    "supplement_todo": "/supplement/todo";
    "supplement_todo_info": "/supplement/todo/info";
  };

  /**
   * route key
   */
  export type RouteKey = keyof RouteMap;

  /**
   * route path
   */
  export type RoutePath = RouteMap[RouteKey];

  /**
   * custom route key
   */
  export type CustomRouteKey = Extract<
    RouteKey,
    | "root"
    | "not-found"
    | "exception"
    | "exception_403"
    | "exception_404"
    | "exception_500"
  >;

  /**
   * the generated route key
   */
  export type GeneratedRouteKey = Exclude<RouteKey, CustomRouteKey>;

  /**
   * the first level route key, which contain the layout of the route
   */
  export type FirstLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "board"
    | "creditrecord"
    | "login"
    | "manage"
    | "science"
    | "statistics"
    | "supplement"
  >;

  /**
   * the custom first level route key
   */
  export type CustomFirstLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
    | "exception"
  >;

  /**
   * the last level route key, which has the page file
   */
  export type LastLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "login"
    | "board_center"
    | "board_left"
    | "board_right"
    | "board"
    | "creditrecord_bank_add"
    | "creditrecord_bank"
    | "creditrecord_guarantee_add"
    | "creditrecord_guarantee"
    | "manage_bank_add"
    | "manage_bank_list"
    | "manage_config"
    | "manage_dict"
    | "manage_log"
    | "manage_menu"
    | "manage_messages"
    | "manage_organization_add"
    | "manage_organization_list"
    | "manage_role"
    | "manage_user"
    | "science_add"
    | "science_info"
    | "science_list"
    | "statistics_bank-loan_detail"
    | "statistics_bank-loan"
    | "statistics_bank-risk"
    | "statistics_company"
    | "statistics_regional-loan_detail"
    | "statistics_regional-loan"
    | "statistics_regional-risk"
    | "supplement_bank_add"
    | "supplement_bank"
    | "supplement_done"
    | "supplement_done_info"
    | "supplement_guarantee_add"
    | "supplement_guarantee"
    | "supplement_todo"
    | "supplement_todo_info"
  >;

  /**
   * the custom last level route key
   */
  export type CustomLastLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
    | "exception_403"
    | "exception_404"
    | "exception_500"
  >;

  /**
   * the single level route key
   */
  export type SingleLevelRouteKey = FirstLevelRouteKey & LastLevelRouteKey;

  /**
   * the custom single level route key
   */
  export type CustomSingleLevelRouteKey = CustomFirstLevelRouteKey & CustomLastLevelRouteKey;

  /**
   * the first level route key, but not the single level
  */
  export type FirstLevelRouteNotSingleKey = Exclude<FirstLevelRouteKey, SingleLevelRouteKey>;

  /**
   * the custom first level route key, but not the single level
   */
  export type CustomFirstLevelRouteNotSingleKey = Exclude<CustomFirstLevelRouteKey, CustomSingleLevelRouteKey>;

  /**
   * the center level route key
   */
  export type CenterLevelRouteKey = Exclude<GeneratedRouteKey, FirstLevelRouteKey | LastLevelRouteKey>;

  /**
   * the custom center level route key
   */
  export type CustomCenterLevelRouteKey = Exclude<CustomRouteKey, CustomFirstLevelRouteKey | CustomLastLevelRouteKey>;

  /**
   * the center level route key
   */
  type GetChildRouteKey<K extends RouteKey, T extends RouteKey = RouteKey> = T extends `${K}_${infer R}`
    ? R extends `${string}_${string}`
      ? never
      : T
    : never;

  /**
   * the single level route
   */
  type SingleLevelRoute<K extends SingleLevelRouteKey = SingleLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}$view.${K}`;
      }
    : never;

  /**
   * the last level route
   */
  type LastLevelRoute<K extends GeneratedRouteKey> = K extends LastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `view.${K}`;
      }
    : never;
  
  /**
   * the center level route
   */
  type CenterLevelRoute<K extends GeneratedRouteKey> = K extends CenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the multi level route
   */
  type MultiLevelRoute<K extends FirstLevelRouteNotSingleKey = FirstLevelRouteNotSingleKey> = K extends string
    ? ElegantConstRoute & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}`;
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;
  
  /**
   * the custom first level route
   */
  type CustomSingleLevelRoute<K extends CustomFirstLevelRouteKey = CustomFirstLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `layout.${RouteLayout}$view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom last level route
   */
  type CustomLastLevelRoute<K extends CustomRouteKey> = K extends CustomLastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom center level route
   */
  type CustomCenterLevelRoute<K extends CustomRouteKey> = K extends CustomCenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the custom multi level route
   */
  type CustomMultiLevelRoute<K extends CustomFirstLevelRouteNotSingleKey = CustomFirstLevelRouteNotSingleKey> =
    K extends string
      ? ElegantConstRoute & {
          name: K;
          path: RouteMap[K];
          component: `layout.${RouteLayout}`;
          children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
        }
      : never;

  /**
   * the custom route
   */
  type CustomRoute = CustomSingleLevelRoute | CustomMultiLevelRoute;

  /**
   * the generated route
   */
  type GeneratedRoute = SingleLevelRoute | MultiLevelRoute;

  /**
   * the elegant route
   */
  type ElegantRoute = GeneratedRoute | CustomRoute;
}
