import type { DataTableBaseColumn, DataTableExpandColumn, DataTableSelectionColumn, PaginationProps } from 'naive-ui'
import type { TableColumnGroup } from 'naive-ui/es/data-table/src/interface'
import { VNodeChild } from 'vue'

declare namespace TableUtil {
  type DataTableBaseColumn<T> = import('naive-ui').DataTableBaseColumn<T>
  type DataTableExpandColumn<T> = import('naive-ui').DataTableExpandColumn<T>
  type DataTableSelectionColumn<T> = import('naive-ui').DataTableSelectionColumn<T>
  type TableColumnGroup<T> = import('naive-ui/es/data-table/src/interface').TableColumnGroup<T>
  type PaginationProps = import('naive-ui').PaginationProps

  type CustomColumnKey = 'operate'

  type SetTableColumnKey<C, T> = Omit<C, 'key'> & { key: keyof T | CustomColumnKey }

  type TableColumnWithKey<T> = SetTableColumnKey<DataTableBaseColumn<T>, T> | SetTableColumnKey<TableColumnGroup<T>, T>

  type BaseData = Record<string, unknown>

  type ApiFn = (args: any) => Promise<unknown>

  type TableColumnCheckTitle = string | ((...args: any) => VNodeChild)

  type TableColumnCheck = {
    key: string
    title: string
    checked: boolean
    children?: TableColumnCheck[]
  }

  type TableDataWithIndex<T> = T & { index: number }

  type TableColumn<T extends BaseData = BaseData, CustomColumnKey = never> =
    | (Omit<TableColumnGroup<T>, 'key'> & { key: keyof T | CustomColumnKey })
    | (Omit<DataTableBaseColumn<T>, 'key'> & { key: keyof T | CustomColumnKey })
    | DataTableSelectionColumn<T>
    | DataTableExpandColumn<T>

  type TransformedData<TableData extends BaseData = BaseData> = {
    data: TableData[]
    pageNum: number
    pageSize: number
    total: number
  }

  /** transform api response to table data */
  type Transformer<TableData extends BaseData = BaseData, Response = NonNullable<unknown>> = (
    response: Response
  ) => TransformedData<TableData>

  type TableConfig<TableData extends BaseData = BaseData, Fn extends ApiFn = ApiFn, CustomColumnKey = never> = {
    /** api function to get table data */
    apiFn: Fn
    /** api params */
    apiParams?: Parameters<Fn>[0]
    /** transform api response to table data */
    transformer?: Transformer<TableData, Awaited<ReturnType<Fn>>>
    /** pagination */
    pagination?: PaginationProps
    /**
     * callback when pagination changed
     *
     * @param pagination
     */
    onPaginationChanged?: (pagination: PaginationProps) => void | Promise<void>
    /**
     * whether to get data immediately
     *
     * @default true
     */
    immediate?: boolean
    /** columns factory */
    columns: () => TableColumn<TableData, CustomColumnKey>[]
    /** default data */
    defaultData?: TableData[]
  }

  type FilteredColumn = {
    type?: 'selection' | 'expand'
    key: string
    title: string
    checked: boolean
    expandable?: any
    renderExpand?: any
    render?: any
  }
}
