import { ref } from 'vue'
import type { FormInst } from 'naive-ui'
import { REG_CODE_SIX, REG_EMAIL, REG_PHONE, REG_PWD, REG_USER_NAME } from '@/constants/reg'
import { $t } from '@/locales'
import { cloneDeep } from 'lodash-es'

export function useFormRules() {
  const patternRules = {
    userName: {
      pattern: REG_USER_NAME,
      message: $t('form.userName.invalid'),
      trigger: 'change'
    },
    phone: {
      key: 'phone',
      pattern: REG_PHONE,
      message: $t('form.phone.invalid'),
      trigger: 'change'
    },
    pwd: {
      pattern: REG_PWD,
      message: $t('form.pwd.invalid'),
      trigger: 'change'
    },
    code: {
      pattern: REG_CODE_SIX,
      message: $t('form.code.invalid'),
      trigger: 'change'
    },
    email: {
      pattern: REG_EMAIL,
      message: $t('form.email.invalid'),
      trigger: 'change'
    }
  } satisfies Record<string, App.Global.FormRule>

  const formRules = {
    userName: [createRequiredRule($t('form.userName.required')), patternRules.userName],
    phone: [createRequiredRule($t('form.phone.required'), 'phone'), patternRules.phone],
    pwd: [createRequiredRule($t('form.pwd.required')), patternRules.pwd],
    password: [createRequiredRule($t('form.pwd.required')), patternRules.pwd],
    code: [createRequiredRule($t('form.code.required')), patternRules.code],
    email: [createRequiredRule($t('form.email.required')), patternRules.email]
  } satisfies Record<string, App.Global.FormRule[]>

  /** the default required rule */
  const defaultRequiredRule = createRequiredRule('不能为空')

  function createRequiredRule(message: string, key?: string): App.Global.FormRule {
    return {
      key,
      required: true,
      message
    }
  }

  return {
    patternRules,
    formRules,
    defaultRequiredRule,
    createRequiredRule
  }
}

export function useNaiveForm(initialFormData: Record<string, any> = {}) {
  const formRef = ref<FormInst | null>(null)
  const initFormData = cloneDeep(initialFormData)

  const formData = ref({ ...initialFormData })

  // 重置表单数据
  const resetFormData = () => {
    formData.value = cloneDeep(initFormData)
  }

  // 设置表单数据
  const setFormData = (newFormData: Record<string, any> = {}) => {
    formData.value = { ...formData.value, ...newFormData }
  }

  async function validate() {
    await formRef.value?.validate()
  }

  async function restoreValidation() {
    formRef.value?.restoreValidation()
  }

  return {
    formRef,
    formData,
    resetFormData,
    setFormData,
    validate,
    restoreValidation
  }
}
