/*
 * @Author: yyh <EMAIL>
 * @Date: 2024-11-21 09:00:09
 * @LastEditors: yyh <EMAIL>
 * @LastEditTime: 2025-02-07 09:43:55
 * @FilePath: \szhjj-html-manage\src\hooks\workRouter.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// 定义一个函数来生成路径映射
const createRouteMap = (path:any, route:any, type:any, ) => {
  // 使用正则表达式替换最后的操作类型
  const addRoute = route.replace(/(_audit|_info|_review)$/, '_add');
  return {
    [path]: {
      route,
      params: { type },
    },
    [path+'_add']: {
      route: addRoute,
      params: { type },
    },
  };
}
// 使用函数生成路径映射
export const RouteMapList = {
  ...createRouteMap("proposed_forecast_product_task", "contract-reserve_schedulingplan_audit", "audit", ),
  ...createRouteMap("quality_assurance_code", "contract-supervision_quality_guarantee_info", "audit", ),
  ...createRouteMap("proposed_forecast_project", "contract-reserve_forecast_audit", "audit", ),
  ...createRouteMap("purchas_plan_code", "contract-reserve_purchasing_audit", "audit", ),
  ...createRouteMap("project_planner_code", "contract-reserve_planner_audit", "audit", ),
  ...createRouteMap("company_code", "supplier_company_audit", "audit", ),
  ...createRouteMap("company_update_code", "supplier_company_audit", "audit", ),
  ...createRouteMap("unqualified_code", "contract-supervision_quality_unaccepted_info", "audit", ),
  ...createRouteMap("qualified_over_zl_code", "contract-supervision_quality_problem_info", "audit", ),
  ...createRouteMap("qualified_over_jd_code", "contract-supervision_schedule_schedule-prob_info", "audit", ),
  ...createRouteMap("qualified_over_fy_code", "contract-supervision_expense_expense-prob_info", "audit", ),
  ...createRouteMap("risk_zl_code", "contract-supervision_quality_risk_info", "audit", ),
  ...createRouteMap("risk_jd_code", "contract-supervision_schedule_schedule-risk_info", "audit", ),
  ...createRouteMap("risk_fy_code", "contract-supervision_expense_expense-risk_info", "audit", ),
  ...createRouteMap("checkplan", "inspection-acceptance_plan_info", "audit", ),
  ...createRouteMap("checkapply", "inspection-acceptance_apply_info", "audit", ),
  ...createRouteMap("certificate", "inspection-acceptance_cert-apply_inner_info", "audit", ),
  ...createRouteMap("certificatejj", "inspection-acceptance_cert-apply_jcheck_info", "audit", ),
  ...createRouteMap("checkfactory", "inspection-acceptance_factory_info", "audit", ),
  ...createRouteMap("shipping", "inspection-acceptance_handover_info", "audit", ),
  ...createRouteMap("contract_manufacture", "contract-reserve_contractreview_audit", "audit", ),
  ...createRouteMap("pick_library_code", "inventory_materialsoutwarehouse_audit", "audit", ),
  ...createRouteMap("receipt_code", "inventory_receive_audit", "audit", ),
  ...createRouteMap("sale_library_code", "inventory_sell-library_info", "audit", ),
  ...createRouteMap("technical_conditions", "contract-supervision_quality_technique_audit", "audit", ),
  ...createRouteMap("technical_conditions_new", "contract-supervision_quality_technique_audit", "audit", ),
  ...createRouteMap("check_product", "inspection-acceptance_rules_info", "audit", ),
  // 可以继续添加其他路径映射
};

