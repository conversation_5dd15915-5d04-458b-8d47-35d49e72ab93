import { ref } from 'vue';

interface IUseExport {
  exportFn: (args: any) => Promise<{
    error?: any;
    response?: any;
    data?: any;
  }>;
  apiParams: Record<string, any>;
  exportFileType?: 'excel' | 'zip';
  fileName?: string;
}

export const useExport = () => {
  const exportLoading = ref(false);

  const exportData = async (params: IUseExport) => {
    const fileType = {
      excel: 'application/vnd.ms-excel',
      zip: 'application/zip'
    };
    try {
      exportLoading.value = true;
      const res = await params.exportFn(params.apiParams);
      if (res.error) throw res.error;
      const { response, data } = res;
      const element = document.createElement('a');
      const blob = new Blob([data], { type: fileType[params?.exportFileType ?? 'excel'] });
      const href = window.URL.createObjectURL(blob);
      const fileName =
        params?.fileName ?? decodeURIComponent(response.headers['content-disposition'].split('filename=')[1]);
      element.href = href;
      element.download = fileName;
      element.click();
      URL.revokeObjectURL(href);
    } finally {
      exportLoading.value = false;
    }
  };

  return {
    exportLoading,
    exportData
  };
};
