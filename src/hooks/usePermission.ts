import { getUserInfo } from '@/store/modules/auth/shared';

export function usePermission() {
  const useInfo = getUserInfo();
  const buttonItemList = useInfo.buttonItemList;
  const permissions = buttonItemList.map(i => i.pageUrl);

  /** 判断是否存在权限 可用于 v-if 显示逻辑 */
  function hasPermission(code: string): boolean {
    // 如果没有code，直接返回true
    if (!code) return true;
    if (permissions && permissions.length > 0) {
      // 判断传过来的值是否在获取的权限中，在就返回true，否则返回false
      return Boolean(permissions.includes(code));
    }
    return false;
  }

  return { hasPermission };
}
