import type { Ref } from 'vue'
import { computed, ref, unref } from 'vue'
import { Expose } from '@/typings/expose'

export function useYcTable() {
  const tableRef: Ref<Expose.YCTable | null> = ref(null)

  // 刷新表格
  const refreshTable = (isReset?: boolean) => {
    tableRef.value?.refreshData(isReset)
  }

  // 是否有数据
  const tableHasData = computed(() => unref(tableRef.value?.hasData))

  return {
    tableRef,
    refreshTable,
    tableHasData
  }
}
