import type { Ref } from 'vue'
import { computed, effectScope, h, onScopeDispose, reactive, ref, watch } from 'vue'
import type { PaginationProps } from 'naive-ui'
import { cloneDeep } from 'lodash-es'
import { useBoolean, useLoading } from '~/packages/hooks'
import { useAppStore } from '@/store/modules/app'
import type { TableUtil } from '@/typings/table'

export function useTable<TableData extends TableUtil.BaseData, Fn extends TableUtil.ApiFn, CustomColumnKey = never>(
  config: TableUtil.TableConfig<TableData, Fn, CustomColumnKey>
) {
  // 新增排序状态
  const sortState = reactive<SortState>({
    columnKey: undefined,
    order: false
  });

  // 处理排序变化
  const handleSortChange = (sorter: SortState) => {
    Object.assign(sortState, sorter);
    
    // 如果有自定义排序器
    if (config.sorter) {
      data.value = config.sorter(data.value, sortState);
      return;
    }

    // 默认客户端排序
    if (sorter.order && sorter.columnKey) {
      const column = allColumns.value.find(
        col => col.key === sorter.columnKey
      );
      
      if (column?.sorter) {
        const sorted = [...data.value].sort(column.sorter);
        data.value = sorter.order === 'ascend' ? sorted : sorted.reverse();
      }
    }
  };

  const scope = effectScope()
  const appStore = useAppStore()

  const { loading, startLoading, endLoading } = useLoading()
  const { bool: empty, setBool: setEmpty } = useBoolean()

  const { apiFn, apiParams, transformer, immediate = true, defaultData } = config

  const searchParams: NonNullable<Parameters<Fn>[0]> = reactive(cloneDeep({ ...apiParams }))

  const allColumns = ref(config.columns()) as Ref<TableUtil.TableColumn<TableData, CustomColumnKey>[]>

  const data = ref(defaultData ?? []) as Ref<TableData[]>
  const totalData = ref(defaultData ?? []) as Ref<TableData[]>

  const { getColumnChecks, getColumns } = useTableColumn(config.columns)

  const columnChecks: Ref<TableUtil.TableColumnCheck[]> = ref(getColumnChecks(config.columns()))

  const columns = computed(() => getColumns(allColumns.value, columnChecks.value))

  // 重新加载列
  function reloadColumns() {
    allColumns.value = config.columns()

    const checkMap = new Map(columnChecks.value.map((col) => [col.key, col.checked]))

    const defaultChecks = getColumnChecks(allColumns.value)

    columnChecks.value = defaultChecks.map((col) => ({
      ...col,
      checked: checkMap.get(col.key) ?? col.checked
    }))
  }

  const pagination = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 15, 20, 25, 30],
    prefix: () => {
      return h('div', { class: 'text-14px text-[#636878] absolute', style: { left: 0 } }, `共 ${pagination.itemCount} 项数据`)
    },
    onUpdatePage: async (page: number) => {
      pagination.page = page
      updateSearchParams({
        pageNo: page,
        pageSize: pagination.pageSize!
      })

      getData()
    },
    onUpdatePageSize: async (pageSize: number) => {
      pagination.pageSize = pageSize
      pagination.page = 1

      updateSearchParams({
        pageNo: pagination.page,
        pageSize
      })

      getData()
    },
    ...config.pagination
  }) as PaginationProps

  function updatePagination(update: Partial<PaginationProps>) {
    Object.assign(pagination, update)
  }

  async function getData() {
    startLoading()

    const formattedParams = formatSearchParams(searchParams)
    //工作流需增加pageNum
    const response: any = await apiFn({
      ...formattedParams,
      pageNo: pagination.page,
      pageNum: pagination.page,
      pageSize: pagination.pageSize
    })
    if (response && response.data !== null) {
  const { data: tableData, pageNum,totalData, pageSize, total } = transformer ? transformer(response as Awaited<ReturnType<Fn>>) : response
      data.value = tableData
      if(totalData){
        totalData.value = totalData
      }
      setEmpty(tableData.length === 0)
      updatePagination({ page: pagination.page, pageSize:pagination.pageSize, itemCount: total })
      endLoading()
    } else {
      setEmpty(true)
      endLoading()
    }
  }

  function formatSearchParams(params: Record<string, unknown>) {
    const formattedParams: Record<string, unknown> = {}

    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        formattedParams[key] = value
      }
    })

    return formattedParams
  }

  /**
   * update search params
   *
   * @param params
   */
  function updateSearchParams(params: Partial<Parameters<Fn>[0]>) {
    Object.assign(searchParams, params)
  }

  if (immediate) {
    getData()
  }

  scope.run(() => {
    watch(
      () => appStore.locale,
      () => {
        reloadColumns()
      }
    )
  })

  onScopeDispose(() => {
    scope.stop()
  })

  return {
    loading,
    empty,
    data,totalData,
    columns,
    columnChecks,
    reloadColumns,
    pagination,
    updatePagination,
    getData,
    searchParams,
    updateSearchParams,sortState,
    handleSortChange
  }
}

function useTableColumn<TableData extends TableUtil.BaseData, CustomColumnKey = never>(
  factory: () => TableUtil.TableColumn<TableData, CustomColumnKey>[]
) {
  const SELECTION_KEY = '__selection__'
  const EXPAND_KEY = '__expand__'

  // 判断是否存在key
  function isTableColumnHasKey(
    column: TableUtil.TableColumn<TableData, CustomColumnKey>
  ): column is TableUtil.TableColumn<TableData, CustomColumnKey> {
    return Boolean((column as NaiveUI.TableColumnWithKey<TableData>).key)
  }

  // 获取勾选显示列
  function getColumnChecks(cols: TableUtil.TableColumn<TableData, CustomColumnKey>[]) {
    const checks: TableUtil.TableColumnCheck[] = []
    cols.forEach((column: TableUtil.TableColumn<TableData, CustomColumnKey>) => {
      if (isTableColumnHasKey(column)) {
        checks.push({
          key: column.key as string,
          title: column.title!,
          checked: true
        })
      } else if (column.type === 'selection') {
        checks.push({
          key: SELECTION_KEY,
          title: '勾选',
          checked: true
        })
      } else if (column.type === 'expand') {
        checks.push({
          key: EXPAND_KEY,
          title: '展开',
          checked: true
        })
      }
    })
    return checks
  }

  // 获取显示列
  function getColumns(cols: TableUtil.TableColumn<TableData, CustomColumnKey>[], checks: TableUtil.TableColumnCheck[]) {
    const columnMap = new Map<string, TableUtil.TableColumn<TableData, CustomColumnKey>>()

    cols.forEach((column) => {
      if (isTableColumnHasKey(column)) {
        columnMap.set(column.key as string, { ...{ resizable: true }, ...column })
      } else if (column.type === 'selection') {
        columnMap.set(SELECTION_KEY, column)
      } else if (column.type === 'expand') {
        columnMap.set(EXPAND_KEY, column)
      }
    })

    const filteredColumns = checks
      .filter((item) => item.checked)
      .map((check) => columnMap.get(check.key) as TableUtil.TableColumn<TableData, CustomColumnKey>)

    return filteredColumns
  }

  return {
    getColumnChecks,
    getColumns
  }
}
