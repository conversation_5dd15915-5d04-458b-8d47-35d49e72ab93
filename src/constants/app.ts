/*
 * @Author: yyh <EMAIL>
 * @Date: 2024-10-28 08:54:05
 * @LastEditors: yyh <EMAIL>
 * @LastEditTime: 2025-02-24 10:32:50
 * @FilePath: \szhjj-html-manage\src\constants\app.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { transformRecordToOption } from '@/utils/common'

export const themeSchemaRecord: Record<UnionKey.ThemeScheme, App.I18n.I18nKey> = {
  light: 'theme.themeSchema.light',
  dark: 'theme.themeSchema.dark',
  auto: 'theme.themeSchema.auto'
}

export const themeSchemaOptions = transformRecordToOption(themeSchemaRecord)

export const loginModuleRecord: Record<UnionKey.LoginModule, App.I18n.I18nKey> = {
  'pwd-login': 'page.login.pwdLogin.title',
  'code-login': 'page.login.codeLogin.title',
  register: 'page.login.register.title',
  'reset-pwd': 'page.login.resetPwd.title',
  'bind-wechat': 'page.login.bindWeChat.title'
}

export const themeLayoutModeRecord: Record<UnionKey.ThemeLayoutMode, App.I18n.I18nKey> = {
  vertical: 'theme.layoutMode.vertical',
  'vertical-mix': 'theme.layoutMode.vertical-mix',
  horizontal: 'theme.layoutMode.horizontal',
  'horizontal-mix': 'theme.layoutMode.horizontal-mix'
}

export const themeLayoutModeOptions = transformRecordToOption(themeLayoutModeRecord)

export const themeScrollModeRecord: Record<UnionKey.ThemeScrollMode, App.I18n.I18nKey> = {
  wrapper: 'theme.scrollMode.wrapper',
  content: 'theme.scrollMode.content'
}

export const themeScrollModeOptions = transformRecordToOption(themeScrollModeRecord)

export const themeTabModeRecord: Record<UnionKey.ThemeTabMode, App.I18n.I18nKey> = {
  chrome: 'theme.tab.mode.chrome',
  button: 'theme.tab.mode.button'
}

export const themeTabModeOptions = transformRecordToOption(themeTabModeRecord)

export const themePageAnimationModeRecord: Record<UnionKey.ThemePageAnimateMode, App.I18n.I18nKey> = {
  'fade-slide': 'theme.page.mode.fade-slide',
  fade: 'theme.page.mode.fade',
  'fade-bottom': 'theme.page.mode.fade-bottom',
  'fade-scale': 'theme.page.mode.fade-scale',
  'zoom-fade': 'theme.page.mode.zoom-fade',
  'zoom-out': 'theme.page.mode.zoom-out',
  none: 'theme.page.mode.none'
}

export const themePageAnimationModeOptions = transformRecordToOption(themePageAnimationModeRecord)

export const ElementTypeModeRecord: Record<UnionKey.ElementType, string> = {
  MENU: '菜单',
  PAGE: '页面',
  BUTTON: '按钮'
}
export const ElementTypeModeOptions = transformRecordToOption(ElementTypeModeRecord)

export const SexTypeModeRecord: Record<UnionKey.SexType, string> = {
  MALE: '男',
  FEMALE: '女'
}
export const SexTypeModeOptions = transformRecordToOption(SexTypeModeRecord)

export const UserTypeModeRecord: Record<UnionKey.UserType, string> = {
  ADMIN: '系统管理员',
  USER: '金融机构用户',
  JUSER: '监管用户'
}
export const UserTypeModeOptions = transformRecordToOption(UserTypeModeRecord)

export const logTypeModeRecord: Record<UnionKey.LogType, string> = {
  BUSINESS: '业务日志',
  SYSTEM: '系统日志'
}
export const logTypeModeOptions = transformRecordToOption(logTypeModeRecord)

export const operTargetTypeModeRecord: Record<UnionKey.OperTargetType, string> = {
  MANAGE_USER: '管理端用户',
  ROLE: '角色',
  ELEMENT: '菜单权限'
}
export const operTargetTypeModeOptions = transformRecordToOption(operTargetTypeModeRecord)

export const operTypeModeRecord: Record<UnionKey.OperType, string> = {
  LOGIN: '登录',
  LOGOUT: '登出',
  ADD: '新增',
  UPDATE: '编辑',
  DELETE: '删除',
  IMPORT: '导入',
  EXPORT: '导出',
  ENABLE: '启用',
  DISABLE: '停用',
  PUBLISH: '发布',
  RESET_PWD: '重置密码',
  QUERY: '查询',
  GET: '详情',
  SORT: '排序',
  AUDIT: '审核',
  CANCEL: '返回',
  WITHDRAW: '撤回',
  SIGN: '签到',
  UPDATE_PWD: '修改密码',
  SUBMIT: '提交'
}
export const operTypeModeOptions = transformRecordToOption(operTypeModeRecord)

/** 业务定义 - 如下 */

/** 机构类型 */
export const OrgTypeModeRecord: Record<UnionKey.OrgType, string> = {
  BANK: '银行机构',
  GUARANTEE: '担保机构'
}
export const OrgTypeModeOptions = transformRecordToOption(OrgTypeModeRecord)

/** 备案类型 */
export const loanStatusModeRecord: Record<UnionKey.loanStatus, string> = {
  RECORDED: '已备案',
  TEMP_SAVED: '暂存',
  SYSTEM_DETERMINED_EXPIRED: '系统判定贷款已到期',
  LOAN_RENEWAL: '续贷',
  MANUAL_CONFIRMED_REPAID: '人工确认已还款',
  MANUAL_CONFIRMED_EXPIRED: '人工确认贷款已到期',
  APPLIED: '已申请风补',
  APPLIED_TERMINATED: '已终止'
}
export const loanStatusModeOptions = transformRecordToOption(loanStatusModeRecord)

/** 贷款方式 */
export const loanTypeModeRecord: Record<UnionKey.loanType, string> = {
  CREDIT: '信用贷款',
  GUARANTEE: '担保贷款'
}
export const loanTypeModeOptions = transformRecordToOption(loanTypeModeRecord)

/** 贷款用途 */
export const loanPurposeModeRecord: Record<UnionKey.loanPurpose, string> = {
  RND_PRODUCTION_EXPENSE: '研发生产支出',
  RAW_MATERIALS_PURCHASE: '采购原材料',
  SALARY_PAYMENT: '工资发放',
  PROJECT_MARKETING: '项目营销推广',
  LIQUIDITY_TURNOVER: '流动资金周转',
  OTHER: '其他'
}
export const loanPurposeModeOptions = transformRecordToOption(loanPurposeModeRecord)

/** 文件用途 */
export const FielModeRecord: Record<UnionKey.Filetype, string> = {
  USER_MARK: '用户签名',
  LOAN_CONTRACT: '贷款合同',

  LOAN_RECEIPT: '借据',

  GUARANTEE_LETTER: '委托担保函',

  BANK_PERMISSION_CONTRACT: '银行保证合同',

  COMPENSATION_APPLICATION_FORM: '补偿申请表',

  BANK_COLLECTION_EVIDENCE: '银行催收依据',

  LETTER_OF_COMMITMENT: '承诺函',

  PAYING_RECEIPT_VOUCHER: '放款凭证',

  COMPENSATION_NOTICE: '代偿通知书',

  COMPENSATION_CERTIFICATE: '代偿证明',

  COMPENSATION_TRANSACTION_RECORD: '代偿流水',

  FUND_TRANSFER_VOUCHER: '资金划拨凭证',

  OTHER: '其他'
}
export const FielModeOptions = transformRecordToOption(FielModeRecord)
/** 更新状态 */
export const chagedStatusModeRecord: Record<UnionKey.chagedStatus, string> = {
  RECORDED: '已备案',
  TEMP_SAVED: '暂存',
  SYSTEM_DETERMINED_EXPIRED: '系统判定贷款已到期',
  LOAN_RENEWAL: '续贷',
  MANUAL_CONFIRMED_REPAID: '人工确认已还款',
  MANUAL_CONFIRMED_EXPIRED: '人工确认贷款已到期',
  APPLIED_TERMINATED: '已终止'
}
export const chagedStatusFormModeOptions = transformRecordToOption(chagedStatusModeRecord)

/** 信贷备案审批状态 */
export const CreditAuditStatusModeRecord: Record<UnionKey.CreditAuditStatus, string> = {
  FOO: '暂存',
  TO_BE_SUBMITTED: '待提交',
  REGIONAL_AUDITING: '区域审核中',
  PROVINCIAL_AUDITING: '省级审核中',
  AWAITING_VOUCHER_SUPPLEMENT: '待补充凭证',
  PENDING_PAYMENT: '待拨付',
  PAID: '已拨付',
  TERMINATED: '终止'
}
export const CreditAuditStatusModeOptions = transformRecordToOption(CreditAuditStatusModeRecord)

/** 担保风补审批状态 */
export const GuaranteeAuditStatusModeRecord: Record<UnionKey.GuaranteeAuditStatus, string> = {
  FOO: '暂存',
  TO_BE_SUBMITTED: '待提交',
  PROVINCIAL_AUDITING: '省级审核中',
  REGION_AUDITING: '区域审核中',
  PENDING_PAYMENT: '待拨付',
  PAID: '已拨付',
  TERMINATED: '终止'
}
export const GuaranteeAuditStatusModeOptions = transformRecordToOption(GuaranteeAuditStatusModeRecord)

/**
 * 通知类型
 */
export const MsgTypeModeRecord: Record<UnionKey.MsgType, string> = {
  RISK_COMPENSATION_PROCESS: '风险补偿流程',
  INSTITUTIONAL_EARLY_WARNING_NOTICE: '机构预警通知',
  INSTITUTIONAL_CIRCUIT_BREAKER_NOTICE: '机构熔断通知',
  REGION_EARLY_WARNING_NOTICE: '区域预警通知',
  REGION_CIRCUIT_BREAKER_NOTICE: '区域熔断通知'
}
export const MsgTypeModeOptions = transformRecordToOption(MsgTypeModeRecord)
