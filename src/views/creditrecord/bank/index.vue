<script setup lang="ts">
import { NButton, NFlex } from 'naive-ui'
import type { Ref } from 'vue'
import { h, ref } from 'vue'
import { useYcTable } from '@/hooks/useYcTable'
import type { TableUtil } from '@/typings/table'
import { loanStatusModeRecord, loanTypeModeRecord } from '@/constants/app'
import { createConfirmTextButton, createTextButton } from '@/utils/common'
import { useRouterPush } from '@/hooks/useRouterPush'
import { useNaiveForm } from '@/hooks/useForm'
import dayjs from 'dayjs'

const { tableRef, refreshTable } = useYcTable()
const { routerPushByKey } = useRouterPush()

const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchManageApiRecordQueryPage,
  apiParams: {
    entName: null,
    loanBank: null,
    socialCreditCode: null,
    region: null,
    loanType: 'CREDIT',
    recordDateBegin: null,
    recordDateEnd: null
  },
  columns: () => [
    {
      type: 'selection',
      fixed: 'left',
      width: 50,
      disabled: (rowData: any) => ['APPLIED_TERMINATED', 'APPLIED', 'TEMP_SAVED'].includes(rowData.status)
    },
    {
      title: '贷款状态',
      key: 'status',
      width: 150,
      // fixed: 'left',
      ellipsis: {
        tooltip: true
      },
      resizable: true,
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => loanStatusModeRecord[rowData.status as UnionKey.loanStatus]
          }
        )
      }
    },
    {
      title: '贷款方式',
      key: 'loanType',
      width: 100,
      ellipsis: {
        tooltip: true
      },
      resizable: true,
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => loanTypeModeRecord[rowData.loanType as UnionKey.loanType]
          }
        )
      }
    },
    {
      title: '企业名称',
      resizable: true,
      width: 180,
      key: 'entName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业信用代码',
      key: 'socialCreditCode',
      width: 200,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款金额（万元）',
      key: 'loanAmount',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '申请金额（万元）',
      key: 'recordAmount',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款开始日期',
      key: 'loanBeginDate',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      render: (rowData: any) => {
        return h(
          'span',
          {},
          {
            default: () => dayjs(rowData.loanBeginDate).format('YYYY-MM-DD')
          }
        )
      }
    },
    {
      title: '贷款结束日期',
      key: 'loanEndDate',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      render: (rowData: any) => {
        return h(
          'span',
          {},
          {
            default: () => dayjs(rowData.loanEndDate).format('YYYY-MM-DD')
          }
        )
      }
    },
    {
      title: '贷款期限(月)',
      key: 'loanTerm',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '利率(%)',
      key: 'loanRate',
      width: 150
    },
    {
      title: '贷款发行银行',
      key: 'loanBankName',
      width: 150
    },
    {
      title: '企业负责人',
      key: 'enterprisePrincipal',
      width: 150
    },
    {
      title: '企业联系人',
      key: 'enterpriseContact',
      width: 150
    },
    {
      title: '手机号',
      key: 'mobilePhoneNumber',
      width: 150
    },
    {
      title: '座机号',
      key: 'landlinePhoneNumber',
      width: 150
    },
    {
      title: '区域',
      key: 'regionName',
      width: 150
    },
    {
      title: '首贷户',
      key: 'firstTimeBorrower',
      width: 150,
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => (rowData.firstTimeBorrower ? '是' : '否')
          }
        )
      }
    },
    {
      title: '备注',
      key: 'remark',
      width: 150
    },
    {
      title: '更新时间',
      key: 'updatedTime',
      width: 150,
      render: (rowData: any) => {
        return h(
          'span',
          {},
          {
            default: () => ($isEmpty(rowData.updatedTime) ? '--' : dayjs(rowData.updatedTime).format('YYYY-MM-DD'))
          }
        )
      }
    },
    {
      title: '操作',
      key: 'operator',
      fixed: 'right',
      align: 'center',
      width: 150,
      render: (rowData: any) => {
        const viewBtn = createTextButton({
          text: '详情',
          onClick: async () => {
            routerPushByKey('creditrecord_bank_add', {
              query: { id: rowData.id, type: 'view' }
            })
          }
        })
        const editBtn = createTextButton({
          text: '编辑',
          permissionCode: 'creditrecord-edit',
          onClick: async () => {
            routerPushByKey('creditrecord_bank_add', {
              query: { id: rowData.id, type: 'edit' }
            })
          }
        })
        const delBtn = createConfirmTextButton({
          text: '删除',
          permissionCode: 'creditrecord-edit',
          confirmText: '确定删除该数据？',
          onClick: async () => {
            const res = await fetchManageApiRecordDelete({ id: rowData.id as number })
            if (res.error) return
            window?.$message?.success('删除成功')
            refreshTable(true)
          }
        })
        let btnList = [viewBtn]
        if (rowData.status == 'TEMP_SAVED') {
          btnList.push(editBtn)
          btnList.push(delBtn)
        }
        return h(NFlex, {}, { default: () => btnList })
      }
    }
  ]
})
const searchTimeRang = ref(null)
// 查询
const handleSearch = () => {
  idList.value = []
  checkedRowKey.value = []
  refreshTable(true)
}
const Add = () => {
  routerPushByKey('creditrecord_bank_add', {
    query: { type: 'add' }
  })
}
// 重置
const handleReset = () => {
  idList.value = []
  checkedRowKey.value = []
  tableConfig.value.apiParams = {
    entName: null,
    loanBank: null,
    socialCreditCode: null,
    region: null,
    loanType: 'CREDIT',
    recordDateBegin: null,
    recordDateEnd: null
  }
  searchTimeRang.value = null
  refreshTable()
}
const dateConfirm = (val: any) => {
  if (val) {
    tableConfig.value.apiParams.recordDateBegin = val[0]
    tableConfig.value.apiParams.recordDateEnd = val[1]
  } else {
    tableConfig.value.apiParams.recordDateBegin = null
    tableConfig.value.apiParams.recordDateEnd = null
  }
}

const Modalclose = () => {
  modalRef.value.close()
  reset()
}

const { formData, formRef, setFormData, validate } = useNaiveForm({
  renewalDate: null,
  chagedStatus: ''
})
const reset = () => {
  formData.value.chagedStatus = ''
  formData.value.renewalDate = null
}

//批量更改状态
const loading = ref(false)
const modalRef: any = ref(null)
const batchHandle = () => {
  if (!idList.value.length) {
    return window?.$message?.warning(`请选择需要操作的数据！`)
  }
  modalRef.value.open()
}

// 申请风险补偿
const submitHandle = async () => {
  if (!idList.value.length) {
    return window?.$message?.warning(`请选择需要操作的数据！`)
  }
  if (checkedRowKey.value.length > 1) {
    return window?.$message?.warning(`只能选择一条数据进行操作！`)
  }
  const rowData = tableRef.value?.currentPageData.find((item: any) => item.id === idList.value[0])
  console.log('rowData', rowData)
  if (['APPLIED_TERMINATED'].includes(rowData.status)) {
    return window?.$message?.warning('当前状态无法进行操作')
  }
  const { data, error } = await fetchManageApiRiskSubmit({ serialNo: checkedRowKey.value[0].serialNo })
  if (!error) {
    await routerPushByKey('supplement_bank_add', {
      query: { type: 'edit', id: data.id }
    })
  }
}
const handleSubmit = async () => {
  try {
    await validate()
    loading.value = true
    checkedRowKey.value.map((item: any) => {
      item['chagedStatus'] = formData.value.chagedStatus
      item['renewalDate'] = formData.value.renewalDate
      return item
    })

    const res = await fetchManageApiRecordChangeBatchStatus({ list: checkedRowKey.value })
    if (res.error) return
    window?.$message?.success(`操作成功！`)
    idList.value = []
    checkedRowKey.value = []
    loading.value = false
    refreshTable()
    Modalclose()
  } catch (error) {
    loading.value = false
  } finally {
    loading.value = false
  }
}
const chagedStatusFormModeOptions = ref([
  { label: '人工确认已逾期', value: 'MANUAL_CONFIRMED_EXPIRED' },
  { label: '续贷', value: 'LOAN_RENEWAL' },
  { label: '人工确认已还款', value: 'MANUAL_CONFIRMED_REPAID' }
])
const rules = {
  chagedStatus: [{ required: true, message: '请选择变更状态', trigger: 'blur' }]
}
const idList = ref([])
const checkedRowKey: any = ref([])
const checkedRowKeys = (keys: any, rows: any, meta: any) => {
  idList.value = keys
  // 使用 Set 优化性能
  const currentPageIdSet = new Set(tableRef.value?.currentPageData.map((item: any) => item.id))
  const selectedIdSet = new Set(keys)

  // 保留不在当前页的选中项
  checkedRowKey.value = checkedRowKey.value.filter((item: any) => !currentPageIdSet.has(item.id))

  // 添加当前页选中的项
  const curPageChecked = tableRef.value?.currentPageData.filter((item: any) => selectedIdSet.has(item.id))
  checkedRowKey.value = [...checkedRowKey.value, ...curPageChecked]
}
const chanceNRadio = (value: any) => {
  formData.value.chagedStatus = value
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="银行信用贷款" />
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
      :scrollX="3200"
      :checkedRowKeys="idList"
      @update:checkedRowKeys="checkedRowKeys"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
          ><YcSearchItem label="企业名称">
            <NInput
              v-model:value="tableConfig.apiParams.entName"
              placeholder="请输入企业名称"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="贷款发放银行">
            <NInput
              v-model:value="tableConfig.apiParams.loanBank"
              placeholder="请输入贷款发放银行"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="企业信用代码">
            <NInput
              v-model:value="tableConfig.apiParams.socialCreditCode"
              placeholder="请输入企业信用代码"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="区域">
            <RegionSelect v-model="tableConfig.apiParams.region" />
          </YcSearchItem>
          <!-- <YcSearchItem label="贷款方式">
            <NSelect
              v-model:value="tableConfig.apiParams.loanType"
              placeholder="请输入贷款方式"
              clearable
              :options="loanTypeModeOptions"
            />
          </YcSearchItem> -->
          <YcSearchItem label="录入日期">
            <NDatePicker
              v-model:value="searchTimeRang"
              clearable
              type="daterange"
              class="w-full"
              separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @update:formatted-value="dateConfirm"
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <div class="flex items-center gap-8px">
          <NButton
            type="primary"
            @click="Add"
            v-permission="'creditrecord-add'"
            >贷款项目录入</NButton
          >
          <NButton
            @click="batchHandle"
            v-permission="'creditrecord-batch'"
            >批量更改状态</NButton
          >
          <NButton
            @click="submitHandle"
            v-permission="'creditrecord-apply'"
            >申请风险补偿</NButton
          >
        </div>
      </template>
    </YcTable>
    <YcModal
      ref="modalReftime"
      title="设置有效期"
      width="550px"
      content-css="!pb-0"
    >
      <NForm
        ref="formRef"
        :model="formDataTime"
        label-placement="left"
        label-width="100"
        label-align="left"
        require-mark-placement="left"
      >
        <NFormItem
          part="renewalDate"
          required
          show-message
          label="日期"
          path="renewalDate"
        >
          <NDatePicker
            v-model:formatted-value="formData.renewalDate"
            placeholder="请选择日期"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            @update:formatted-value="changeTime"
          ></NDatePicker>
        </NFormItem>
      </NForm>
      <div class="pl-[100px] mb-10px">
        有效时间为<span class="text-[red]">{{ loanTerm }}</span
        >天（含节假日）。
      </div>
      <template #footer>
        <NButton @click="ModalcloseTime">取消</NButton>
        <NButton
          type="primary"
          :loading="loadingTime"
          @click="handleSubmitTime"
          >确认</NButton
        >
      </template>
    </YcModal>
    <YcModal
      ref="modalRef"
      title="批量更改状态"
      width="550px"
      content-css="!pb-0"
    >
      <NForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="100"
        label-align="left"
        require-mark-placement="left"
      >
        <NFormItem
          label="变更状态"
          part="chagedStatus"
          required
          show-message
        >
          <n-radio-group
            v-model:value="formData.chagedStatus"
            name="radiogroup"
            @update:value="chanceNRadio"
          >
            <n-space>
              <n-radio
                v-for="(song, index) in chagedStatusFormModeOptions"
                :key="index"
                :value="song.value"
              >
                {{ song.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </NFormItem>
        <NFormItem
          part="renewalDate"
          required
          show-message
          v-if="formData.chagedStatus === 'LOAN_RENEWAL'"
          label="续贷日期"
          path="renewalDate"
        >
          <NDatePicker
            v-model:formatted-value="formData.renewalDate"
            placeholder="请选择续贷日期"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></NDatePicker>
        </NFormItem>
      </NForm>
      <template #footer>
        <NButton @click="Modalclose">取消</NButton>
        <NButton
          type="primary"
          :loading="loading"
          @click="handleSubmit"
          >确认</NButton
        >
      </template>
    </YcModal>
  </YcContent>
</template>

<style lang="scss">
.aline:hover {
  text-decoration: underline;
}
/* 在全局样式添加 */
.n-collapse-transition {
  transition:
    height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: height;
}

/* 防止flex布局导致的过渡问题 */
.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  overflow: hidden; /* 防止内容溢出影响动画 */
}
.search-container {
  display: block;
  min-width: 100%;
  contain: content; /* 限制浏览器重绘范围 */
}
</style>
