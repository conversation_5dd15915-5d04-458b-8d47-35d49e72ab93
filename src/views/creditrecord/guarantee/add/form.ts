//导出
import type { FormGroup } from '@/typings/dynamic-form'
import * as api from '@/service/api'

//@ts-ignore
import { loanPurposeModeOptions, loanTypeModeOptions } from '@/constants/app'
//获取下拉选择
//@ts-ignore
const fetchOptions = async (url?: keyof ApiMethods, name?: any, id?: any, params?: any = {}): Promise<Option[]> => {
  //@ts-ignore
  const { data = [] } = await api[url](params) // 调用对应的 API 方法
  let arr = []
  if (data.list) {
    arr = data.list.map((t: any) => {
      t.label = t[name]
      t.value = t[id]
      return t
    })
  } else if (data.rows) {
    arr = data.rows.map((t: any) => {
      t.label = t[name]
      t.value = t[id]
      return t
    })
  } else {
    arr = data.map((t: any) => {
      t.label = t[name]
      t.value = t[id]
      return t
    })
  }
  const DictList = arr.map((t: any) => {
    t.label = t[name]
    t.value = t[id]
    return t
  })
  return DictList
}
export const formList = async (param: any = {}): Promise<FormGroup[]> => {
  let guaranteeOrgList = await fetchOptions('fetchManageApiOrgQueryPage', 'orgName', 'id', {
    orgType: 'GUARANTEE',
    pageNo: 1,
    pageSize: 1000
  })
  return [
    {
      title: '基础信息',
      items: [
        {
          type: 'EntSelect',
          field: 'entName',
          label: '企业名称',
          span: 7,
          required: true,
          RuleType: 'string'
        },
        {
          type: 'input',
          field: 'socialCreditCode',
          label: '统一社会信用代码',
          disabled: true,
          span: 7,
          required: true,
          props: {}
        },
        {
          type: 'input',
          field: 'enterprisePrincipal',
          label: '企业负责人',
          span: 7,
          required: true,
          props: {}
        },
        {
          type: 'input',
          field: 'enterpriseContact',
          label: '企业联系人',
          span: 7,
          required: true,
          props: {}
        },
        {
          type: 'input',
          field: 'mobilePhoneNumber',
          label: '手机号',
          span: 7,
          required: true,
          rules: [
            {
              pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
              message: '请输入正确的手机号',
              trigger: ['blur'],
              required: true
            }
          ],
          props: {
            maxlength: 11,
            showCount: true
          }
        },
        {
          type: 'input',
          field: 'landlinePhoneNumber',
          label: '座机号',
          span: 7,
          rules: [
            {
              pattern: /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/,
              message: '座机号格式不正确',
              trigger: ['blur']
            }
          ],
          props: {}
        }
      ]
    },
    {
      title: '贷款信息',
      items: [
        {
          type: 'input',
          field: 'serialNo',
          label: '流水号',
          span: 7,
          required: true,
          disabled: true,
          props: {}
        },
        {
          type: 'select',
          field: 'loanType',
          label: '贷款方式',
          span: 7,
          required: true,
          options: loanTypeModeOptions,
          disabled: true,
          props: {
            placeholder: '请选择'
          }
        },
        {
          type: 'RegionSelect',
          field: 'region',
          label: '区域',
          required: true,
          span: 7,
          props: {
            placeholder: '请选择'
          }
        },
        {
          type: 'select',
          field: 'guaranteeOrg',
          label: '担保机构',
          span: 7,
          props: {
            placeholder: '请选择'
          },
          options: guaranteeOrgList
        },
        {
          type: 'select',
          field: 'loanPurpose',
          label: '贷款用途',
          span: 7,
          options: loanPurposeModeOptions,
          props: {
            placeholder: '请选择'
          }
        },
        {
          type: 'BankSelectTree',
          field: 'loanBank',
          fieldLabel: 'loanBankName',
          label: '贷款发放银行',
          span: 7,
          required: true,
          RuleType: 'number'
        },
        {
          type: 'inputgroupNumber',
          field: 'loanAmount',
          label: '贷款金额',
          span: 7,
          required: true,
          RuleType: 'number',
          props: {
            placeholder: '请输入',
            triggerText: '万元',
            showButton: false
          }
        },
        {
          type: 'inputgroupNumber',
          field: 'recordAmount',
          label: '申请备案金额',
          span: 7,
          RuleType: 'number',
          required: true,
          props: {
            placeholder: '请输入',
            triggerText: '万元',
            showButton: false
          }
        },
        {
          type: 'inputgroupNumber',
          RuleType: 'number',
          field: 'loanRate',
          label: '贷款利率',
          required: true,
          props: {
            placeholder: '请输入',
            triggerText: ' %',
            showButton: false
          }
        },
        {
          type: 'date',
          field: 'loanBeginDate',
          label: '贷款开始日期',
          span: 7,
          required: true,
          RuleType: 'number',
          props: {
            type: 'date'
          }
        },
        {
          type: 'date',
          field: 'loanEndDate',
          label: '贷款结束日期',
          span: 7,
          required: true,
          RuleType: 'number',
          props: {
            type: 'date'
          }
        },
        {
          type: 'inputgroupNumber',
          RuleType: 'number',
          field: 'loanTerm',
          label: '主债权期限',
          disabled: true,
          props: {
            placeholder: '请输入',
            max: 36,
            triggerText: ' 月',
            showButton: false
          }
        },
        {
          type: 'inputgroupNumber',
          RuleType: 'number',
          field: 'guaranteeFeeRate',
          label: '担保费率(/年)',
          required: true,
          span: 7,
          props: {
            max: 0.5,
            placeholder: '请输入',
            triggerText: '%',
            showButton: false
          }
        },
        {
          type: 'input',
          field: 'loanContractNo',
          label: '贷款合同编号',
          span: 7,
          props: {
            placeholder: '请输入'
          }
        },
        {
          type: 'input',
          field: 'bankPermissionContractNo',
          label: '银行保证合同编号',
          span: 7,
          props: {
            placeholder: '请输入'
          }
        },
        {
          type: 'input',
          field: 'entrustedGuaranteeLetterNo',
          label: '委托担保协议/委托担保函编号',
          span: 7,
          props: {
            placeholder: '请输入'
          }
        },
        {
          type: 'NRadioGroup',
          field: 'firstTimeBorrower',
          label: '首贷户',
          tooltip: '按人民银行首贷户口径',
          span: 7,
          slotName: 'firstTimeBorrower',
          required: true,
          RuleType: 'number',
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 }
          ],
          props: {}
        },
        {
          type: 'NRadioGroup',
          field: 'principalFreeRenewal',
          label: '无还本续贷(选择是则贷款期限不可超过两年)',
          slotName: 'principalFreeRenewal',
          span: 7,
          required: true,
          RuleType: 'number',
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 }
          ],
          props: {}
        }
      ]
    },

    {
      title: '其他信息',
      items: [
        {
          type: 'uploadFile',
          field: 'LOAN_CONTRACT',
          label: '贷款合同',
          tooltip: '线上贷款可提供相关系统截图',
          required: true,
          RuleType: 'array',
          span: 7,
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'uploadFile',
          field: 'GUARANTEE_LETTER',
          label: '委托担保协议/委托担保函',
          required: true,
          RuleType: 'array',
          span: 7,
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'uploadFile',
          field: 'BANK_PERMISSION_CONTRACT',
          label: '银行保证合同',
          required: true,
          RuleType: 'array',
          span: 7,
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'uploadFile',
          field: 'OTHER',
          label: '其他',
          span: 7,
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'input',
          field: 'remark',
          label: '备注',
          span: 10,
          props: {
            type: 'textarea',
            rows: 3,
            maxlength: 500,
            showCount: true,
            class: '!w-full'
          }
        }
      ]
    }
  ]
}
