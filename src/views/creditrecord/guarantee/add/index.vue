<template>
  <yc-form-page :loading="mainLoading">
    <DynamicForm
      :config="formConfig"
      :showRegion="true"
      :disabled="type === 'view'"
      v-model:modelValue="newformData"
      :initial-data="formData"
      @event="handleFormEvent"
      @update:modelValue="handleDataChange"
      class="mb-10px"
      ref="DynamicFormRef"
    >
      <!-- 自定义插槽示例 -->
    </DynamicForm>
    <div
      class="bg-white p-10px"
      v-if="isShowtabal"
    >
      <BorderTitleBar
        title="备案记录"
        class="mb-10px !w-4/5 !w-3/5"
      />
      <AuditProcess :instanceId="formData.instanceId"></AuditProcess>
    </div>
    <template #footer>
      <div class="w-full flex items-center justify-end gap-12px">
        <NButton @click="handleCancel">取消</NButton>
        <NButton
          strong
          secondary
          type="info"
          @click="handleSubmit(false)"
          :loading="saveLoading"
          v-if="type !== 'view'"
        >
          保存草稿
        </NButton>
        <NButton
          type="primary"
          @click="handleSubmit(true)"
          :loading="submitLoading"
          v-if="type !== 'view'"
        >
          提交
        </NButton>
      </div>
    </template>
  </yc-form-page>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import AuditProcess from '@/components/business/auditProcess.vue'
// import type { FormConfig } from './_components/types'
import { formList } from './form'
import useLoading from '~/packages/hooks/src/use-loading'
import { useRouterPush } from '@/hooks/useRouterPush'
import dayjs from 'dayjs'
import { generateSerial } from '@/utils/common'
import { cloneDeep, groupBy } from 'lodash-es'
import YcFormPage from '@/components/common/yc-form-page.vue'
import { fetchManageApiRecordQueryRestAmount } from '@/service/api'

const route = useRoute()
const { routerBack } = useRouterPush()
const query = computed(() => {
  return route.query
})

// 是否为编辑
const isEdit = computed(() => {
  return query.value.id
})

// 贷款利率配置
const loanRate = ref({
  guaranteeLprOneYear: 0,
  guaranteeLprThreeYear: 0
})
const getLoanRate = async (regionNo: string) => {
  const res = await fetchManageApiConfigQueryPage({
    pageNo: 1,
    pageSize: 1000,
    configLevel: 'REGION',
    configNo: regionNo
  })
  if (!res.error) {
    loanRate.value = res.data.list[0]
  }
}

const sciencename = ref(query.value.scienceid)
const uscCode = ref(query.value.uscCode)
const formData: any = ref({
  loanType: 'GUARANTEE',
  principalFreeRenewal: 0,
  serialNo: generateSerial(),
  loanBankName: ''
})

const isShowtabal = computed(() => {
  return formData.value.instanceId
})
const type = ref(query.value.type)
const newformData: any = ref({})
const regionName: any = ref('')
const loanBankName: any = ref('')
const guaranteeOrgName: any = ref('')
const formConfig: any = ref(await formList(newformData.value))
const handleFormEvent = async (payload: any) => {
  console.log('🚀 ~ handleFormEvent ~ payload:', payload)
  // formConfig.value[1].items[1].label = '贷款利率（xx区域的1年期利率低于3.45%，3年期利率低于4.34%）'
  // 选择是则贷款期限不可超过两年（24个月）
  if (payload.field == 'principalFreeRenewal') {
    if (payload.value == 1) {
      formConfig.value[1].items[9].max = 24
    } else {
      formConfig.value[1].items[9].max = 36
    }
  }
  if (payload.field == 'loanBeginDate' || payload.field == 'loanEndDate') {
    if (newformData.value.loanBeginDate && newformData.value.loanEndDate) {
      const start = dayjs(newformData.value.loanBeginDate)
      const end = dayjs(newformData.value.loanEndDate)
      const diff = end.diff(start, 'month')
      newformData.value.loanTerm = diff
    }
  }
  //如果为1 验证时间间隔是否满足要求
  if (payload.field == 'principalFreeRenewal') {
    if (payload.value == 1) {
      formConfig.value[1].items[9].props.max = 24
      if (newformData.value.loanBeginDate && newformData.value.loanEndDate) {
        const start = dayjs(newformData.value.loanBeginDate)
        const end = dayjs(newformData.value.loanEndDate)
        const diff = end.diff(start, 'month')
        if (diff > 24) {
          window?.$message?.error('贷款期限不能超过24个月')
          formData.value.loanEndDate = null
          newformData.value.loanEndDate = null
          formData.value.loanBeginDate = null
          newformData.value.loanBeginDate = null
          payload.value = null
        }
      }
    } else {
      formConfig.value[1].items[9].props.max = 36
    }
  }
  // 授信金额>=贷款金额>=备案金额
  if (payload.field == 'loanAmount') {
    if (payload.value > newformData.value.creditAmount) {
      window?.$message?.error('贷款金额不能大于授信金额')
      formData.value.loanAmount = null
      newformData.value.loanAmount = null
      payload.value = null
    }
  }
  if (payload.field == 'recordAmount') {
    if (payload.value > newformData.value.loanAmount) {
      window?.$message?.error('贷款金额不能大于授信金额')
      formData.value.recordAmount = null
      newformData.value.recordAmount = null
      payload.value = null
    }
  }
  if (payload.field === 'entName') {
    formData.value.entName = payload.item.entName || null
    formData.value.socialCreditCode = payload.item.uscCode || null
    // 更改可申请备案金额
    const recordAmounts = await fetchManageApiRecordQueryRestAmount({
      socialCreditCode: formData.value.socialCreditCode
    })
    const recordAmountItem = formConfig.value[1].items.find((item: any) => item.field === 'recordAmount')
    recordAmountItem.label = `申请备案金额 (可备案额度为 ${recordAmounts.data.restGuaranteeAmount} 万元)`
    recordAmountItem.props.max = recordAmounts.data.restGuaranteeAmount
  }
  if (payload.field === 'region') {
    const dynamicFormData = DynamicFormRef.value.getFormData()
    dynamicFormData.loanBank = null
    await getLoanRate(payload.value.regionSelectValue)
    if (loanRate.value.guaranteeLprOneYear) {
      formConfig.value[1].items[8].label = `贷款利率（${payload.value.regionSelectLabel}区域的1年期利率低于${loanRate.value.guaranteeLprOneYear}%，3年期利率低于${loanRate.value.guaranteeLprThreeYear}%）`
    } else {
      formConfig.value[1].items[8].label = `贷款利率`
      formConfig.value[1].items[8].props.max = null
    }
    regionName.value = payload.value.regionSelectLabel
  }
  if (payload.field === 'loanBank') {
    // formData.value.loanBank = payload.value
    payload.item.options.map((item: any) => {
      if (item.id == payload.value) {
        loanBankName.value = item.label
      }
    })
  }
  if (payload.field === 'guaranteeOrg') {
    // formData.value.loanBank = payload.value
    payload.item.options.map((item: any) => {
      if (item.id == payload.value) {
        guaranteeOrgName.value = item.label
      }
    })
  }
}

const handleDataChange = (data: any) => {
  // formData.value = data
}
const DynamicFormRef: any = ref(null)

// 保存为草稿 / 提交
const typeMap = ['OTHER', 'LOAN_CONTRACT', 'GUARANTEE_LETTER', 'BANK_PERMISSION_CONTRACT']
const { loading: submitLoading, startLoading: startSubmitLoading, endLoading: endSubmitLoading } = useLoading()
const { loading: saveLoading, startLoading: startSaveLoading, endLoading: endSaveLoading } = useLoading()
const handleSubmit = async (isSubmit?: any) => {
  await DynamicFormRef.value.validate()
  const dynamicFormData = DynamicFormRef.value.getFormData()
  // 判断贷款利率
  if (dynamicFormData.loanTerm <= 12 && dynamicFormData.loanRate > loanRate.value.guaranteeLprOneYear) {
    window?.$message?.error('贷款利率不能大于1年期利率')
    return
  } else if (dynamicFormData.loanRate > 12 && dynamicFormData.loanRate > loanRate.value.guaranteeLprThreeYear) {
    window?.$message?.error('贷款利率不能大于3年期利率')
    return
  }
  if (regionName.value) {
    dynamicFormData.regionName = regionName.value
  }
  if (loanBankName.value) {
    dynamicFormData.loanBankName = loanBankName.value
  }
  if (guaranteeOrgName.value) {
    dynamicFormData.guaranteeOrgName = guaranteeOrgName.value
  }
  const fileList: any[] = []
  typeMap.forEach((key) => {
    if (dynamicFormData[key] && dynamicFormData[key].length) {
      dynamicFormData[key].map((item: any) => {
        const files = {
          fileUrl: item.url,
          fileName: item.name,
          fileType: 'OTHER',
          fileBusiness: key
        }
        fileList.push(files)
      })
    }
  })
  const fetchParams = Object.assign({}, cloneDeep(dynamicFormData), { fileList: fileList })
  try {
    if (isSubmit) {
      startSubmitLoading()
    } else {
      startSaveLoading()
    }
    const fetchApi = isEdit.value ? fetchManageApiRecordEdit : fetchManageApiRecordAdd
    const res = await fetchApi(fetchParams)
    if (!res.error) {
      if (isSubmit) {
        const recordRes = await fetchManageApiRecordRecordApply({ id: res.data?.id ?? query.value.id })
        if (!recordRes.error) {
          window?.$message?.success('提交成功')
          routerBack()
        }
      } else {
        window?.$message?.success('保存成功')
        routerBack()
      }
    }
  } finally {
    endSaveLoading()
    endSubmitLoading()
  }
}

// 取消
const handleCancel = () => {
  routerBack()
}

// 页面初始化
const { loading: mainLoading, startLoading: startMainLoading, endLoading: endMainLoading } = useLoading()
const init = async () => {
  try {
    startMainLoading()
    if (isEdit.value) {
      const { data } = await fetchManageApiRecordQueryDetail({ id: query.value.id })
      data.loanBeginDate = new Date(data.loanBeginDate).getTime()
      data.loanEndDate = new Date(data.loanEndDate).getTime()
      data.loanBank = +data.loanBank
      data.guaranteeOrg = +data.guaranteeOrg
      const files = groupBy(data.fileList, 'fileBusiness')
      Object.keys(files).forEach((key) => {
        files[key] = files[key].map((item: any) => {
          return {
            ...item,
            url: item.fileUrl,
            name: item.fileName,
            status: 'finished'
          }
        })
      })
      formData.value = Object.assign({}, data, files)
      await getLoanRate(formData.value.region)
      formConfig.value[1].items[8].label = `贷款利率（${formData.value.regionName}区域的1年期利率低于${loanRate.value.guaranteeLprOneYear}%，3年期利率低于${loanRate.value.guaranteeLprThreeYear}%）`
    } else {
      formData.value = Object.assign(formData.value, { entName: sciencename.value, socialCreditCode: uscCode.value })
    }
  } finally {
    endMainLoading()
  }
}

onMounted(() => {
  init()
})
</script>
