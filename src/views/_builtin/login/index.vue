<script setup lang="ts">
import type { Component } from 'vue'
import { getColorPalette, mixColor } from '@yc/utils'
import { useAppStore } from '@/store/modules/app'
import { useThemeStore } from '@/store/modules/theme'
import { loginModuleRecord } from '@/constants/app'
import PwdLogin from './modules/pwd-login.vue'
import CodeLogin from './modules/code-login.vue'
import Register from './modules/register.vue'
import ResetPwd from './modules/reset-pwd.vue'
import BindWechat from './modules/bind-wechat.vue'
import autofit from 'autofit.js'

interface Props {
  /** The login module */
  module?: UnionKey.LoginModule
}

const props = withDefaults(defineProps<Props>(), {
  module: 'pwd-login'
})

const appStore = useAppStore()
const themeStore = useThemeStore()

interface LoginModule {
  key: UnionKey.LoginModule
  label: string
  component: Component
}

const modules: LoginModule[] = [
  {
    key: 'pwd-login',
    label: loginModuleRecord['pwd-login'],
    component: PwdLogin
  },
  {
    key: 'code-login',
    label: loginModuleRecord['code-login'],
    component: CodeLogin
  },
  { key: 'register', label: loginModuleRecord.register, component: Register },
  {
    key: 'reset-pwd',
    label: loginModuleRecord['reset-pwd'],
    component: ResetPwd
  },
  {
    key: 'bind-wechat',
    label: loginModuleRecord['bind-wechat'],
    component: BindWechat
  }
]

const activeModule = computed(() => {
  const findItem = modules.find((item) => item.key === props.module)
  return findItem || modules[0]
})

const bgThemeColor = computed(() => (themeStore.darkMode ? getColorPalette(themeStore.themeColor, 7) : themeStore.themeColor))

const bgColor = computed(() => {
  const COLOR_WHITE = '#ffffff'

  const ratio = themeStore.darkMode ? 0.5 : 0.2

  return mixColor(COLOR_WHITE, themeStore.themeColor, ratio)
})

onMounted(() => {
  autofit.init({
    el: '#login-fit',
    dw: 1920,
    dh: 900
  })
})

onUnmounted(() => {
  autofit.off()
})
</script>

<template>
  <div
    class="relative flex-center wh-full overflow-hidden main-wrap"
    id="login-fit"
    :style="{ backgroundColor: bgColor }"
  >
    <div class="py-50px px-60px absolute right-120px top-50% -translate-y-1/2 bg-#fff rounded-8px z-10">
      <div class="text-34px text-#3E4373 font-400">湖南省科技型企业</div>
      <div class="text-34px text-#3E4373 font-600">知识价值贷款风险补偿<span class="text-#0D61B8">管理系统</span></div>
      <main class="pt-30px">
        <!-- <h3 class="text-18px text-primary font-medium">
          {{ $t(activeModule.label) }}
        </h3> -->
        <div>
          <Transition
            :name="themeStore.page.animateMode"
            mode="out-in"
            appear
          >
            <component :is="activeModule.component" />
          </Transition>
        </div>
      </main>
      <footer class="footer pt-30px flex items-center justify-center">
        <img
          src="@/assets/images/login-logo.png"
          alt=""
          class="w-213px h-103px"
        />
      </footer>
    </div>
    <div class="login-icon"></div>
  </div>
</template>

<style scoped lang="scss">
.main-wrap {
  background: url('@/assets/images/login-bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
}

.login-icon {
  position: absolute;
  left: 26px;
  top: 110px;
  z-index: 1;
  width: 1216px;
  height: 762px;
  background: url('@/assets/images/login-icon.png') no-repeat center center;
  background-size: cover;
}

.footer {
}
</style>
