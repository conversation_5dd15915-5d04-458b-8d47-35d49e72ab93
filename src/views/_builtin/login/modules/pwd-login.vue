<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { useIntervalFn } from '@vueuse/core'
import { $t } from '@/locales'
import { useFormRules, useNaiveForm } from '@/hooks/useForm'
import { useAuthStore } from '@/store/modules/auth'
import { localStg } from '@/utils/storage'
import { Base64 } from 'js-base64'
import { fetchManageApiAuthSendCode } from '@/service/api'

defineOptions({
  name: 'PwdLogin'
})

const authStore = useAuthStore()
const { formRef, validate } = useNaiveForm()
const { formRules } = useFormRules()

// 记住密码相关信息
const rememberInfo: ComputedRef<Record<string, any> | null> = computed(() => {
  return localStg.get('rememberInfo')
})
// 记住用户名
const isRememberUserName = ref(rememberInfo.value?.isRememberUserName ?? false)
// 记住密码
const isRememberPassword = ref(rememberInfo.value?.isRememberPassword ?? false)

interface FormModel {
  userName: string | null
  password: string | null
  verifyCode: string | null
}

const model: FormModel = reactive({
  userName: rememberInfo.value?.userName ? Base64.decode(rememberInfo.value?.userName) : null,
  password: rememberInfo.value?.password ? Base64.decode(rememberInfo.value?.password) : null,
  verifyCode: null
})

const rules: Record<keyof FormModel, App.Global.FormRule[]> = {
  userName: formRules.userName,
  password: formRules.pwd,
  verifyCode: formRules.code
}

// 验证码倒计时
const countdown = ref(0)
const { pause, resume } = useIntervalFn(
  () => {
    countdown.value--
    if (countdown.value <= 0) {
      pause()
    }
  },
  1000,
  { immediate: false }
)

// 获取验证码按钮文本
const verifyCodeButtonText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}秒` : '发送验证码'
})

// 获取验证码
async function handleGetVerifyCode() {
  // 检查用户名是否已输入
  if (!model.userName) {
    window?.$message?.error('请输入用户名')
    return
  }
  try {
    // 调用发送验证码API
    const { error } = await fetchManageApiAuthSendCode({
      loginNumber: model.userName
    })
    if (error) return

    // 开始倒计时
    countdown.value = 60
    resume()
    window?.$message?.success('验证码已发送')
  } catch (error) {
    window?.$message?.error('验证码发送失败')
  }
}

// 提交
async function handleSubmit() {
  await validate()
  await authStore.login(model.userName as string, model.password as string, model.verifyCode as string)
  localStg.set('rememberInfo', {
    isRememberUserName: isRememberUserName.value,
    isRememberPassword: isRememberPassword.value,
    userName: isRememberUserName.value ? Base64.encode(model.userName as string) : null,
    password: isRememberPassword.value ? Base64.encode(model.password as string) : null
  })
}
</script>

<template>
  <NForm
    ref="formRef"
    :model="model"
    :rules="rules"
    size="large"
    :show-label="false"
  >
    <div class="flex flex-col">
      <NFormItem
        path="userName"
        class="!w-full flex-shrink-0"
      >
        <n-input
          style="--n-color: #f0f2f5; --n-padding-left: 10px"
          v-model:value="model.userName"
          :placeholder="$t('page.login.common.userNamePlaceholder')"
          class="w-full"
        >
          <template #prefix>
            <div class="icon-ion-person text-18px text-#9195A2"></div>
          </template>
        </n-input>
      </NFormItem>
      <NFormItem
        path="password"
        class="!w-full flex-shrink-0"
      >
        <n-input
          style="--n-color: #f0f2f5; --n-padding-left: 10px"
          v-model:value="model.password"
          type="password"
          show-password-on="click"
          :placeholder="$t('page.login.common.passwordPlaceholder')"
          @keyup.stop.enter="handleSubmit"
          class="!w-full"
        >
          <template #prefix>
            <div class="icon-ion-ios-lock text-22px text-#9195A2"></div>
          </template>
        </n-input>
      </NFormItem>
      <NFormItem
        path="verifyCode"
        class="!w-full flex-shrink-0"
      >
        <n-input
          style="--n-color: #f0f2f5; --n-padding-left: 10px"
          v-model:value="model.verifyCode"
          :placeholder="$t('page.login.common.codePlaceholder')"
          class="w-full"
        >
          <template #prefix>
            <div class="icon-ion-md-key text-22px text-#9195A2"></div>
          </template>
          <template #suffix>
            <n-button
              text
              type="primary"
              style="font-size: 14px"
              @click="handleGetVerifyCode"
              :disabled="countdown > 0"
              >{{ verifyCodeButtonText }}</n-button
            >
          </template>
        </n-input>
      </NFormItem>
      <NButton
        strong
        secondary
        style="background: linear-gradient(90deg, #6993f0 0%, #3159b3 100%)"
        class="!text-#ffffffe6 hover:!text-#ffffff active:!text-#ffffff"
        size="large"
        @click="handleSubmit"
        :loading="authStore.loginLoading"
        >{{ $t('route.login') }}</NButton
      >
    </div>
    <div class="flex items-center gap-30px pt-20px justify-between">
      <NCheckbox
        v-model:checked="isRememberUserName"
        class="items-center"
      >
        <span class="text-16px text-#3E4373 font-400">记住用户名</span>
      </NCheckbox>
      <NCheckbox
        v-model:checked="isRememberPassword"
        class="items-center"
      >
        <span class="text-16px text-#3E4373 font-400">记住密码</span>
      </NCheckbox>
    </div>
  </NForm>
</template>

<style scoped></style>
