<!--
 * @Author: yyh <EMAIL>
 * @Date: 2024-10-24 08:42:02
 * @LastEditors: yyh <EMAIL>
 * @LastEditTime: 2025-01-10 17:36:51
 * @FilePath: \szhjj-html-manage\src\views\_builtin\login\modules\code-login.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { $t } from "@/locales";
import { useRouterPush } from "@/hooks/useRouterPush";

defineOptions({
  name: "CodeLogin",
});

const { toggleLoginModule } = useRouterPush();
</script>

<template>
  <NForm size="large" :show-label="false">
    <NFormItem>
      <NInput :placeholder="$t('page.login.common.phonePlaceholder')" st />
    </NFormItem>
    <NFormItem>
      <NInput :placeholder="$t('page.login.common.codePlaceholder')" />
    </NFormItem>
    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" block round>
        {{ $t("common.confirm") }}
      </NButton>
      <NButton size="large" block round @click="toggleLoginModule('pwd-login')">
        {{ $t("page.login.common.back") }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
