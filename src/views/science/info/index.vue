<template>
  <yc-form-page>
    <div class="bg-white mb-12px pb-20px px-12px">
      <BorderTitleBar
        title="企业基础信息"
        class="mt-10px mb-10px"
      />
      <div class="flex gap-12px items-center my-12px">
        <div class="text-[18px]">{{ valueData.entName }}</div>
        <n-tag
          :bordered="false"
          size="small"
          v-for="item in valueData.entScale"
        >
          {{ item }}
        </n-tag>
      </div>
      <n-descriptions
        label-placement="left"
        bordered
        :column="2"
        size="small"
        label-align="center"
        content-class="text-center w-30%"
      >
        <n-descriptions-item
          v-for="key in Object.keys(labelData)"
          :key="key"
          :label="labelData[key]"
          :span="1"
        >
          {{ valueData[key] }}
        </n-descriptions-item>
      </n-descriptions>
    </div>
    <div class="bg-white mb-12px px-12px">
      <div class="flex-center">
        <BorderTitleBar
          title="知识价值信用评分评级"
          class="mt-10px mb-10px"
        />
        <NSelect
          class="w-1/5"
          :options="valueData.cardList"
          v-model:value="years"
          placeholder="请选择年度"
          @update:value="handleChange"
        />
      </div>
      <div class="my-4 p-4 rounded-md bfbox">
        <!-- <h2 class="text-[14px] font-semibold text-center text-gray-800 mb-1">知识价值信用评分评级</h2> -->
        <!-- 主评分区 -->
        <div class="flex items-center justify-between md:justify-center mb-2">
          <!-- 评级 -->
          <div
            v-for="(item, index) in cardList"
            class="flex items-center w-full border-r-solid border-r-1px border-r-gray-200"
            :class="{ '!border-r-none': index === cardList.length - 1 }"
          >
            <div class="w-full flex flex-col items-center">
              <div
                class="text-[20px]"
                :class="item.class ? item.class : 'text-[#1D2129]'"
              >
                {{ currentYearData[item.content] || 0 }}
              </div>
              <div class="text-[13px] text-[#4E5969]">{{ item.title }}</div>
            </div>
          </div>
          <!-- 分值 -->
        </div>

        <!-- 子评分区 -->
      </div>
      <div
        v-for="item in baseDataList"
        :key="item.name"
        class="pb-14px"
      >
        <div class="text-[16px] font-500 mt-10px mb-10px">{{ item.name }}</div>
        <div class="merged-table">
          <n-table
            bordered
            center
            :single-line="false"
          >
            <thead>
              <tr class="text-center">
                <n-th width="200">指标</n-th>
                <n-th width="100">企业数据</n-th>
                <n-th width="80">分值</n-th>
                <n-th width="200">指标</n-th>
                <n-th width="100">企业数据</n-th>
                <n-th width="80">分值</n-th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(row, index) in item.max"
                :key="index"
                class="text-center"
              >
                <n-td class="!bg-[#fafafc]">{{ item.leftData[index]?.indicator || '' }}</n-td>
                <n-td align="center">{{ currentYearData[item.leftData[index]?.enterpriseData] || '' }}</n-td>
                <n-td align="center">{{ currentYearData[item.leftData[index]?.score] || '' }}</n-td>
                <n-td class="!bg-[#fafafc]">{{ item.rightData[index]?.indicator || '' }}</n-td>
                <n-td align="center">{{ currentYearData[item.rightData[index]?.enterpriseData] || '' }}</n-td>
                <n-td align="center">{{ currentYearData[item.rightData[index]?.score] || '' }}</n-td>
              </tr>
            </tbody>
          </n-table>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="w-full flex items-center justify-end gap-12px">
        <NButton @click="cancel">取消</NButton>
      </div>
    </template>
  </yc-form-page>
</template>

<script setup>
import { NTable, NTd, NTh } from 'naive-ui'
import useLoading from '~/packages/hooks/src/use-loading'
import { useRouterPush } from '@/hooks/useRouterPush'
import { reactive, ref } from 'vue'
import YcFormPage from '@/components/common/yc-form-page.vue'

const route = useRoute()
const { routerBack } = useRouterPush()
const baseDataList = ref([
  {
    name: '技术创新指标',
    leftData: [
      { indicator: '研发费用金额（万元）', enterpriseData: 'developFee', score: 'developFeeScore' },
      { indicator: '研发费用占营业收入的比例（%）', enterpriseData: 'developRevenueRate', score: 'developRevenueRateScore' },
      {
        indicator: '与主营业务相关的发明专利申请量（件）',
        enterpriseData: 'inventPatentNum',
        score: 'inventPatentScore'
      },
      { indicator: '企业技术合同成交额（万元）', enterpriseData: 'techContractAmount', score: 'techContractAmountScore' }
    ],
    max: 4,
    rightData: [
      { indicator: '研发费用增速（%）', enterpriseData: 'developFeeIncrease', score: 'developFeeIncreaseScore' },
      {
        indicator: '科技人员占职工总数的比重（%）',
        enterpriseData: 'sciEmpRate',
        score: 'sciEmpRateScore'
      },
      {
        indicator: '与主营业务相关的PCT专利申请量（件）',
        enterpriseData: 'ptcInventPatentNum',
        score: 'ptcInventPatentScore'
      }
    ]
  },
  {
    name: '成长经营指标',
    leftData: [
      { indicator: '高新技术产品收入(万元)', enterpriseData: 'highTechRevenue', score: 'highTechRevenueScore' },
      { indicator: '营业收入增长率(%)', enterpriseData: 'revenueIncreaseRate', score: 'revenueIncreaseRateScore' },
      { indicator: '研发费用加计扣除所得税减免额(万元)', enterpriseData: 'developDecreaseTax', score: 'developDecreaseTaxScore' }
    ],
    max: 3,
    rightData: [
      { indicator: '营业收入(万元)', enterpriseData: 'revenue', score: 'revenueScore' },
      { indicator: '研究生以上人员占比(%)', enterpriseData: 'graduateRate', score: 'graduateRateScore' },
      { indicator: '净资产利润率(%)', enterpriseData: 'netAssetProfitRate', score: 'netAssetProfitRateScore' }
    ]
  },
  {
    name: '辅助指标',
    leftData: [
      { indicator: '吸纳高校应届毕业生人数(人)', enterpriseData: 'currentGraduateNum', score: 'currentGraduateScore' },
      { indicator: '获得省级及以上科技奖励数量(项)', enterpriseData: 'privincialAwardNum', score: 'privincialAwardScore' },
      { indicator: '获得风险投资金额(万元)', enterpriseData: 'riskInvestFee', score: 'riskInvestFeeScore' }
    ],
    max: 3,
    rightData: [
      { indicator: '承担建设省级及以上研发或创新平台数量(项)', enterpriseData: 'upPrivincialDevNum', score: 'upPrivincialDevScore' },
      { indicator: '承担省级以上科技计划项目数量（项）', enterpriseData: 'privincialSciProNum', score: 'privincialSciProScore' }
    ]
  }
])
const cardList = [
  { title: '评级', content: 'evaluateLevel', class: 'text-[#2f6cf4]' },
  { title: '分值', content: 'evaluateScore' },
  { title: '技术创新', content: 'techInnovate' },
  { title: '成长经营', content: 'growManage' },
  { title: '辅助指标', content: 'assignKpi' }
]
const options = [
  // 电子信息、高技术服务、航空航天、生物与新医药、先进制造与自动化、新材料、新能源与节能、资源与环境、其他
  { label: '电子信息', value: '10' },
  { label: '高技术服务', value: '20' },
  { label: '航空航天', value: '30' },
  { label: '生物与新医药', value: '40' },
  { label: '先进制造与自动化', value: '50' },
  { label: '新材料', value: '60' },
  { label: '新能源与节能', value: '70' },
  { label: '资源与环境', value: '80' },
  { label: '其他', value: '90' }
] // 需补充选项
// 定义表格数据类型
const labelData = reactive({
  uscCode: '统一社会信用代码',
  legalRepName: '法定代表人',
  indCode: '所属国民经济行业代码',
  techField: '所属科技创新领域',
  regCapital: '注册资金(万元)',
  regDate: '注册日期',
  regAddr: '注册地址',
  opAddr: '经营地址'
})
const cancel = () => {
  routerBack()
}
const entScaleoptions = [
  //微型企业、小型企业、中型企业、大型企业
  { label: '微型企业', value: '10' },
  { label: '小型企业', value: '20' },
  { label: '中型企业', value: '30' },
  { label: '大型企业', value: '40' }
]
const valueData = reactive({})
const years = ref(null)
const currentYearData = ref({})
const handleChange = (value) => {
  valueData.cardList.map((item) => {
    if (item.label == value) {
      currentYearData.value = item.value
    }
  })
}
// 计算最大行数
const { loading: spinLoading, startLoading: startSpinLoading, endLoading: endSpinLoading } = useLoading()
const id = computed(() => {
  return route.query.id
})
const formData = ref({})

// 初始化
const init = async () => {
  try {
    startSpinLoading()
    const { data = {} } = await fetchManageApiSciEntQueryDetail({ id: id.value })
    data.entType = data.entType && data.entType.split(',')
    data.entScale =
      data.entScale &&
      data.entScale.split(',').map((item) => {
        return entScaleoptions.find((option) => option.value === item)?.label
      })
    data.techField =
      data.techField &&
      data.techField
        .split(',')
        .map((item) => {
          return options.find((option) => option.value === item)?.label
        })
        .join(',')
    data.regCapital = +data.regCapital
    years.value = data.years[0]
    let array = []
    for (const key in data.transMap) {
      let onj = {
        label: key,
        value: data.transMap[key]
      }
      array.push(onj)
    }
    data['cardList'] = array
    data.score = data.score ? +data.score : 0
    Object.assign(formData.value, {
      ...data
    })
    Object.assign(valueData, {
      ...data
    })
    handleChange(years.value)
  } finally {
    endSpinLoading()
  }
}

init()
</script>

<style scoped>
.merged-table {
  background: white;
  border-radius: 4px;
  overflow: hidden;

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
  }

  .header-section {
    background: #f5f7fa;
    text-align: center;
  }

  :deep(tr:nth-child(even)) {
    background-color: #f8f9fa;
  }

  :deep(th, td) {
    border-right: 1px solid #e0e0e0;
    padding: 8px 12px !important;
  }

  :deep(tr:last-child td) {
    border-bottom: none;
  }
}

.n-card {
  margin: 0px auto;
}
.bfbox {
  background: linear-gradient(180deg, #e7f2ff 0%, #e7f2ff33 100%);
}
.form-container {
  padding-bottom: 70px;
}
.btnBox {
  height: 56px;
  background: #fff;
  box-shadow: 0 -5px 10px -1px #0000000d;
  position: fixed;
  bottom: 0;
  width: calc(100% - 270px);
}
</style>
