export const formList: any = [
  {
    title: '基础信息',
    items: [
      {
        type: 'input',
        field: 'entName',
        label: '企业名称',
        span: 7,
        required: true,
        props: {
          placeholder: '请输入企业名称'
        }
      },
      {
        type: 'input',
        field: 'uscCode',
        label: '统一社会信用代码',
        span: 7,
        required: true,
        props: {
          placeholder: '请输入'
        }
      },
      {
        type: 'input',
        field: 'legalRepName',
        label: '法定代表人',
        span: 7,
        props: {
          placeholder: '请输入'
        }
      },
      {
        type: 'select',
        field: 'entType',
        label: '企业类型',
        span: 7,
        required: true,
        RuleType: 'array',
        options: [
          //高新技术企业,科技型中小企业,科创板后备企业,科创板上市企业,科技创新专板,其他,
          { label: '国家高新技术企业', value: '10' },
          { label: '科技型中小企业', value: '20' },
          { label: '科创板后备企业', value: '30' },
          { label: '科创板上市企业', value: '40' },
          { label: '科技创新专板', value: '50' },
          { label: '扩容科企', value: '70' },
          { label: '其他', value: '60' }
        ], // 需补充选项
        props: {
          multiple: true,
          'max-tag-count': 'responsive',
          placeholder: '请选择'
        }
      },
      {
        type: 'select',
        field: 'entScale',
        label: '企业规模',
        span: 7,
        RuleType: 'array',
        props: {
          multiple: true,
          'max-tag-count': 'responsive',
          placeholder: '请选择'
        },
        options: [
          //微型企业、小型企业、中型企业、大型企业
          { label: '微型企业', value: '10' },
          { label: '小型企业', value: '20' },
          { label: '中型企业', value: '30' },
          { label: '大型企业', value: '40' }
        ]
      },
      {
        type: 'select',
        field: 'techField',
        label: '所属科技创新领域',
        span: 7,
        RuleType: 'array',
        props: {
          multiple: true,
          'max-tag-count': 'responsive',
          placeholder: '请选择'
        },
        options: [
          // 电子信息、高技术服务、航空航天、生物与新医药、先进制造与自动化、新材料、新能源与节能、资源与环境、其他
          { label: '电子信息', value: '10' },
          { label: '高技术服务', value: '20' },
          { label: '航空航天', value: '30' },
          { label: '生物与新医药', value: '40' },
          { label: '先进制造与自动化', value: '50' },
          { label: '新材料', value: '60' },
          { label: '新能源与节能', value: '70' },
          { label: '资源与环境', value: '80' },
          { label: '其他', value: '90' }
        ] // 需补充选项
      },
      {
        type: 'input',
        field: 'indCode',
        label: '国民经济行业代码',
        span: 7,
        props: {
          placeholder: '请输入'
        }
      },
      {
        type: 'inputgroupNumber',
        RuleType: 'number',
        field: 'regCapital',
        label: '注册资本',
        span: 7,
        props: {
          placeholder: '请输入',
          triggerText: '万元',
          precision: 2,
          showButton: false
        }
      },
      {
        type: 'date',
        field: 'regDate',
        required: true,
        label: '注册日期',
        span: 7,
        RuleType: 'number',
        props: {
          placeholder: '请输入'
        }
      },
      {
        type: 'input',
        field: 'regAddr',
        label: '注册地址',
        span: 7,
        props: {
          placeholder: '请输入'
        }
      },
      {
        type: 'RegionSelect',
        field: 'regCity',
        fieldLabel: 'regCityName',
        label: '注册地市',
        span: 7,
        required: true,
        props: {
          placeholder: '请选择'
        }
      },
      {
        type: 'input',
        field: 'opAddr',
        label: '实际经营地址',
        span: 7,
        props: {
          placeholder: '请输入'
        }
      },
      {
        type: 'input',
        field: 'remark',
        label: '备注',
        span: 7,
        props: {
          type: 'textarea',
          placeholder: '请输入备注',
          rows: 3
        }
      },
      {
        type: 'uploadFile',
        field: 'fileList',
        label: '附件',
        span: 7,
        props: {
          actionurl: '/sci-ent/upload',
          accept: 'rar, zip, doc, docx, pdf, jpg',
          class: '!w-full'
        }
      }
    ]
  },
  {
    title: '知识价值信用评分评级',
    items: [
      {
        type: 'date',
        field: 'dataYear',
        label: '评分年份',
        span: 7,
        props: {
          type: 'year',
          placeholder: '请选择'
        }
      },
      {
        type: 'inputnumber',
        field: 'score',
        label: '评分',
        span: 7,
        props: {
          placeholder: '请输入',
          min: 0,
          max: 100,
          showButton: false
        },
        RuleType: 'number',
        required: true
      },
      {
        type: 'input',
        field: 'rating',
        label: '评级',
        span: 7,
        disabled: true,
        props: {
          placeholder: '请选择'
        }
      }
    ]
  }
]
