<template>
  <yc-form-page :loading="spinLoading">
    <DynamicForm
      :config="formConfig"
      :initial-data="formData"
      v-model:modelValue="newformData"
      @event="handleFormEvent"
      class="mb-10px"
      ref="DynamicFormRef"
    >
      <!-- 自定义插槽示例 -->
    </DynamicForm>
    <template #footer>
      <div class="w-full flex items-center justify-end gap-12px">
        <NButton @click="cancel">取消</NButton>
        <NButton
          type="primary"
          :loading="submitLoading"
          @click="saveDraft"
          >提交</NButton
        >
      </div>
    </template>
  </yc-form-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DynamicForm from '@/components/business/dynamicForm.vue'
import type { FormConfig } from '@/typings/dynamic-form'
import { formList } from './form'
import useLoading from '~/packages/hooks/src/use-loading'
import { useRouterPush } from '@/hooks/useRouterPush'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'
import YcFormPage from '@/components/common/yc-form-page.vue'

const { routerBack, route } = useRouterPush()
const formData = ref({})
const newformData: any = ref({})
const cancel = () => {
  routerBack()
}
const formConfig: any = ref<FormConfig>(formList)
const pareInt = (val: any) => {
  if (isNaN(val) || val < 0 || val > 100) return null
  if (val >= 95) return 'A'
  if (val >= 90) return 'B'
  if (val >= 80) return 'C'
  if (val >= 70) return 'D'
  if (val >= 60) return 'E'
  return null
}
const handleFormEvent = async (payload: any) => {
  if (payload.field === 'score') {
    newformData.value.rating = pareInt(payload.value)
  }
}

// 是否为编辑
const isEdit = computed(() => {
  return route.value.query.id
})
const DynamicFormRef: any = ref(null)
const { loading: submitLoading, startLoading: startSubmitLoading, endLoading: endSubmitLoading } = useLoading()
const saveDraft = async () => {
  await DynamicFormRef.value.validate()
  const dynamicFormData = DynamicFormRef.value.getFormData()
  const paramData = cloneDeep(dynamicFormData)
  paramData.dataYear = dayjs(paramData.dataYear).year()
  paramData.regDate = dayjs(paramData.regDate).format('YYYY-MM-DD')
  paramData.techField = paramData.techField?.join(',') ?? ''
  paramData.entScale = paramData.entScale?.join(',') ?? ''
  paramData.entType = paramData.entType?.join(',') ?? ''
  let fileList: any = []
  if (paramData.fileList && paramData.fileList.length) {
    paramData.fileList.map((item: any) => {
      const files = {
        attachmentUrl: item.url,
        attachmentName: item.name,
        attachmentType: 'OTHER',
        attachmentSize: item.size ? item.size : item.file.size
      }
      fileList.push(files)
    })
  }
  const params = Object.assign({}, cloneDeep(paramData), { fileList: fileList })
  try {
    startSubmitLoading()
    const fetchApi = isEdit.value ? fetchManageApiSciEntUpdate : fetchManageApiSciEntAdd
    const res = await fetchApi(params)
    if (!res.error) {
      window?.$message?.success(`${isEdit.value ? '编辑成功' : '新增成功'} `)
      routerBack()
    }
  } finally {
    endSubmitLoading()
  }
}

// 初始化
const { loading: spinLoading, startLoading: startSpinLoading, endLoading: endSpinLoading } = useLoading()
const init = async () => {
  try {
    if (isEdit.value) {
      startSpinLoading()
      const { data = {} } = await fetchManageApiSciEntQueryDetail({ id: route.value.query.id })
      console.log('--data', data)
      const firstMapKey = Object.keys(data.transMap)[0]
      const firstMapValue = data.transMap[firstMapKey] || {}
      data.entType = data.entType && data.entType.split(',')
      data.entScale = data.entScale && data.entScale.split(',')
      data.techField = data.techField && data.techField.split(',')
      data.regCapital = +data.regCapital
      data.dataYear = firstMapKey ? dayjs(firstMapKey).valueOf() : null
      data.score = firstMapValue.evaluateScore ? Number(firstMapValue.evaluateScore) : 0
      data.rating = firstMapValue.evaluateLevel ? firstMapValue.evaluateLevel : ''
      data.regDate = new Date(data.regDate).getTime()
      if (data.fileList) {
        data.fileList = data.fileList.map((file: any) => ({
          ...file,
          name: file.attachmentName,
          size: file.attachmentSize,
          url: file.attachmentUrl,
          status: 'finished' // 设置状态为 'done' 表示文件已上传
        }))
      }
      Object.assign(formData.value, {
        ...data
      })
      Object.assign(newformData.value, {
        ...data
      })
    }
  } finally {
    endSpinLoading()
  }
}
onMounted(() => init())
</script>

<style scoped></style>
