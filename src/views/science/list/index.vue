<script setup lang="ts">
import { NButton, NFlex } from 'naive-ui'
import { h, ref } from 'vue'
import { Expose } from '@/typings/expose'
import { useYcTable } from '@/hooks/useYcTable'
import { createConfirmTextButton, createTextButton } from '@/utils/common'
import dayjs from 'dayjs'
import { useRouterPush } from '@/hooks/useRouterPush'
import { fetchManageApiSciEntBatchUpdate } from '@/service/api'

const { tableRef, refreshTable } = useYcTable()
const { routerPushByKey } = useRouterPush()
const entTypeList = [
  { label: '国家高新技术企业', value: '10' },
  { label: '科技型中小企业', value: '20' },
  { label: '科创板后备企业', value: '30' },
  { label: '科创板上市企业', value: '40' },
  { label: '科技创新专板', value: '50' },
  { label: '其他', value: '60' },
  { label: '扩容科企', value: '70' }
]
const entScaleLsit = [
  { label: '微型企业', value: '10' },
  { label: '小型企业', value: '20' },
  { label: '中型企业', value: '30' },
  { label: '大型企业', value: '40' }
]
const tableConfig: any = ref({
  apiFn: fetchManageApiSciEntQueryPage,
  apiParams: {
    entName: '',
    uscCode: '',
    regCity: null
  },
  columns: () => [
    {
      type: 'selection',
      fixed: 'left',
      width: 50,
      sorter: true
    },
    {
      title: '资质到期时间',
      key: 'qualificationExpireTime',
      width: 140,
      ellipsis: {
        tooltip: true
      },
      resizable: true
    },
    {
      title: '企业名称',
      key: 'entName',
      width: 200,
      ellipsis: {
        tooltip: true
      },
      resizable: true
    },
    {
      title: '统一社会信用代码',
      key: 'uscCode',
      width: 200,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '注册地市',
      key: 'regCity',
      width: 140,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业类型',
      width: 140,
      key: 'entType',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业规模类型',
      key: 'entScale',
      width: 120,
      ellipsis: {
        tooltip: true
      },
      render: (rowData: any) => {
        return h(
          'span',
          {},
          {
            default: () =>
              rowData.entScale
                ? rowData.entScale
                    .split(',')
                    .map((item: any) => {
                      return entScaleLsit.find((ent) => ent.value === item)?.label
                    })
                    .join(', ')
                : '--'
          }
        )
      }
    },
    {
      title: '分值',
      width: 120,
      key: 'score',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '评级',
      width: 120,
      key: 'rating'
    },
    {
      title: '累计已备案金额（万元）',
      width: 180,
      key: 'cumulativeRecordedAmount'
    },
    {
      title: '累计信用贷款（万元）',
      width: 170,
      key: 'cumulativeCreditLoan'
    },
    {
      title: '累计担保贷款（万元）',
      width: 170,
      key: 'cumulativeGuaranteeLoan'
    },
    {
      title: '备注',
      width: 120,
      key: 'remark'
    },
    {
      title: '创建日期',
      width: 120,
      key: 'createTime',
      render: (rowData: any) => {
        return h(
          'span',
          {},
          {
            default: () => dayjs(rowData.createTime).format('YYYY-MM-DD')
          }
        )
      }
    },
    {
      title: '操作',
      key: 'operator',
      fixed: 'right',
      align: 'center',
      width: 150,
      render: (rowData: any) => {
        const viewBtn = createTextButton({
          text: '详情',
          onClick: async () => {
            routerPushByKey('science_info', {
              query: { id: rowData.id, type: 'view' }
            })
          }
        })
        const editBtn = createTextButton({
          text: '编辑',
          permissionCode: 'science-edit',
          onClick: async () => {
            routerPushByKey('science_add', {
              query: { type: 'edit', id: rowData.id }
            })
          }
        })
        const delBtn = createConfirmTextButton({
          text: '删除',
          confirmText: '确定删除该数据？',
          onClick: async () => {
            const res = await fetchManageApiSciEntRemove({ id: rowData.id as number })
            if (res.error) return
            window?.$message?.success('删除成功')
            refreshTable(true)
          }
        })
        return h(NFlex, {}, { default: () => [viewBtn, editBtn, delBtn] })
      }
    }
  ]
})

const idList: any = ref([])
const checkedRowKey: any = ref([])
const checkedRowKeys = (keys: any, rows: any, meta: any) => {
  idList.value = keys
  checkedRowKey.value = []
  tableRef.value?.currentPageData.map((item: any) => {
    if (keys.includes(item.id)) {
      checkedRowKey.value.push(item)
    }
  })
}
const Addbak = (type: number) => {
  if (idList.value.length === 0) {
    window?.$message?.info(`请选择数据！`)
    return
  }
  if (checkedRowKey.value.length > 1) {
    window?.$message?.info(`只能选择一条数据！`)
    return
  }
  if (type == 1) {
    routerPushByKey('creditrecord_bank_add', {
      query: { type: 'add', scienceid: checkedRowKey.value[0].entName, uscCode: checkedRowKey.value[0].uscCode }
    })
  } else {
    routerPushByKey('creditrecord_guarantee_add', {
      query: { type: 'add', scienceid: checkedRowKey.value[0].entName, uscCode: checkedRowKey.value[0].uscCode }
    })
  }
}
// 查询
const handleSearch = () => {
  refreshTable(true)
}

// 重置
const regCity = ref()
const handleReset = () => {
  tableConfig.value.apiParams = {
    entName: '',
    uscCode: '',
    regCity: null
  }
  regCity.value = null
  refreshTable(true)
}
//新增
const addent = () => {
  routerPushByKey('science_add', {
    query: { type: 'add' }
  })
}
//导入成功
const dishonestyImportRef = ref<Expose.YCModal>()
const handleImport = () => {
  dishonestyImportRef.value?.open({
    templateApi: fetchManageApiSciEntDownloadTemplate,
    templateParams: { template: 'TECH_ENTERPRISE' },
    uploadApi: fetchManageApiSciEntImportData
  })
}

//计算分值
const modalRef = ref<Expose.YCModal>()
const form: any = reactive({
  year: null
})
const loading = ref(false)
const addcalc = () => {
  modalRef.value?.open()
}
const close = () => {
  form.year = null
  modalRef.value?.close()
}
const handleConfirm = async () => {
  if (!form.year) {
    return window?.$message?.warning('请选择年份')
  }
  try {
    loading.value = true
    form.year = dayjs(form.year).year()
    const res = await fetchManageApiSciEntCalcByYear({ year: form.year })
    if (!res.error) {
      window?.$message?.success('计算成功')
      refreshTable()
      close()
    }
  } finally {
    loading.value = false
  }
}
const formRef = ref()

const formDataTime = ref({
  ids: [],
  qualificationStartTime: null,
  qualificationExpireTime: null
})
const renewalDate: any = ref(null)
const loadingTime = ref(false)
const modalReftime: any = ref(null)
const ModalcloseTime = () => {
  modalReftime.value.close()
  formDataTime.value = { ids: [], qualificationStartTime: null, qualificationExpireTime: null }
  renewalDate.value = null
  loanTerm.value = 0
}
const loanTerm = ref(0)
const changeTime = (val: any) => {
  if (val) {
    formDataTime.value.qualificationStartTime = val[0]
    formDataTime.value.qualificationExpireTime = val[1]
    const start = dayjs(val[0])
    const end = dayjs(val[1])
    const diff = end.diff(start, 'day')
    loanTerm.value = diff
  } else {
    renewalDate.value = null
  }
}
const submitTime = () => {
  if (idList.value.length === 0) {
    window?.$message?.info(`请选择数据！`)
    return
  }
  // if (checkedRowKey.value.length > 1) {
  //   window?.$message?.info(`只能选择一条数据！`)
  //   return
  // }
  formDataTime.value.ids = idList.value
  modalReftime.value.open()
}
const handleSubmitTime = async () => {
  try {
    loadingTime.value = true
    if (!renewalDate.value[0] || !renewalDate.value[1]) {
      return window?.$message?.warning('请选择日期')
    }
    const res = await fetchManageApiSciEntBatchUpdate(formDataTime.value)
    if (res.error) return
    window?.$message?.success(`操作成功！`)
    idList.value = []
    checkedRowKey.value = []
    loadingTime.value = false
    refreshTable()
    ModalcloseTime()
  } catch (error) {
    console.error(error)
  } finally {
    loadingTime.value = false
  }
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="科技型企业库" />
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
      :scrollX="2000"
      :striped="true"
      :checkedRowKeys="idList"
      @update:checkedRowKeys="checkedRowKeys"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
          ><YcSearchItem label="企业名称">
            <NInput
              v-model:value="tableConfig.apiParams.entName"
              placeholder="请输入企业名称"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="企业信用代码">
            <NInput
              v-model:value="tableConfig.apiParams.uscCode"
              placeholder="请输入企业信用代码"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="注册地市">
            <RegionSelect
              v-model="regCity"
              v-model:item="tableConfig.apiParams.regCity"
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <div class="flex items-center gap-8px">
          <NButton
            type="primary"
            @click="addent"
            v-permission="'science-add'"
            >新增</NButton
          >
          <NButton
            @click="Addbak(1)"
            v-permission="'science-bank'"
            >贷款项目录入</NButton
          >
          <NButton
            @click="Addbak(2)"
            v-permission="'science-guarantee'"
            >贷款项目录入</NButton
          >
          <NButton
            @click="addcalc"
            v-permission="'science-add'"
            >分值计算</NButton
          >
          <NButton
            @click="handleImport"
            v-permission="'science-add'"
            >导入</NButton
          >
          <NButton
            @click="submitTime"
            v-permission="'creditrecord-apply'"
            >设置有效期</NButton
          >
        </div>
      </template>
    </YcTable>
    <yc-import
      ref="dishonestyImportRef"
      @success="refreshTable"
    ></yc-import>
    <YcModal
      ref="modalRef"
      title="计算分值"
      width="500px"
    >
      <!-- 表单计算分值 -->
      <NForm
        ref="formRef"
        :model="form"
        :label-width="60"
        label-placement="left"
      >
        <NFormItem
          label="年份"
          prop="year"
        >
          <n-date-picker
            type="year"
            v-model:value="form.year"
            placeholder="请输入"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NButton @click="close">返回</NButton>
        <NButton
          type="primary"
          :loading="loading"
          @click="handleConfirm"
          >确定</NButton
        >
      </template>
    </YcModal>
    <YcModal
      ref="modalReftime"
      title="设置有效期"
      width="550px"
      content-css="!pb-0"
    >
      <NForm
        ref="formRef"
        :model="formDataTime"
        label-placement="left"
        label-width="80"
        label-align="left"
        require-mark-placement="left"
      >
        <NFormItem
          part="renewalDate"
          required
          show-message
          label="日期"
          path="renewalDate"
        >
          <NDatePicker
            v-model:formatted-value="renewalDate"
            placeholder="请选择日期"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            @update:formatted-value="changeTime"
          ></NDatePicker>
        </NFormItem>
      </NForm>
      <div class="pl-[80px] mb-10px">
        有效时间为<span class="text-[red]">{{ loanTerm }}</span
        >天（含节假日）。
      </div>
      <template #footer>
        <NButton @click="ModalcloseTime">取消</NButton>
        <NButton
          type="primary"
          :loading="loadingTime"
          @click="handleSubmitTime"
          >确认</NButton
        >
      </template>
    </YcModal>
  </YcContent>
</template>

<style lang="scss">
.aline:hover {
  text-decoration: underline;
}
</style>
