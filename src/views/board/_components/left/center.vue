<script setup lang="ts">
import WrapBox from '@/views/board/_components/wrap-box.vue'
import { useStore } from '@/views/board/_context/boardContext'
import { useEcharts } from '@/hooks/useEcharts'
import DataModal from '@/views/board/_components/data-modal.vue'
import { ref } from 'vue'
import { TableUtil } from '@/typings/table'

const { boardData } = useStore()

const renderData = computed(() => {
  return boardData.value.top5RegionLoan
})

const { domRef: chartRef, updateOptions } = useEcharts(() => ({
  grid: {
    top: '16%',
    left: '0%',
    right: '5%',
    bottom: '0%',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    data: [
      { name: '信用贷款', itemStyle: { color: '#0077FF' } },
      { name: '担保贷款', itemStyle: { color: '#2BDCFF' } }
    ],
    top: '2%',
    right: '0%',
    icon: 'circle',
    itemWidth: 8,
    itemHeight: 8,
    itemGap: 16,
    textStyle: {
      color: '#FFFFFFD9',
      fontSize: 14,
      fontWeight: 500
    }
  },
  xAxis: {
    data: [],
    axisLine: {
      show: true,
      lineStyle: {
        color: '#BAE7FFFF',
        width: 2
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      show: true,
      interval: 0,
      rotate: -45,
      margin: 14,
      textStyle: {
        color: '#FFFFFFD9',
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 22
      }
    }
  },
  yAxis: {
    type: 'value',
    name: '单位（万元）',
    nameTextStyle: {
      color: '#FFFFFFD9',
      fontSize: 14,
      fontWeight: 400
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        cap: 'round',
        color: '#FFFFFF38'
      }
    },
    axisTick: {
      show: false
    },
    axisLine: {
      show: false
    },
    axisLabel: {
      show: true,
      textStyle: {
        color: '#FFFFFFD9',
        fontSize: 14,
        fontWeight: 500
      }
    }
  },
  series: [
    {
      name: '信用贷款',
      type: 'bar',
      barWidth: 20, // 固定宽度值（像素）
      stack: 'total',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: '#0077FF' },
            { offset: 1, color: '#0077FF' }
          ]
        }
      },
      data: []
    },
    {
      name: '担保贷款',
      type: 'bar',
      stack: 'total',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: '#5FD5EC' },
            { offset: 1, color: '#5FD5EC' }
          ]
        }
      },
      data: []
    }
  ]
}))

const updateChart = () => {
  updateOptions((opts) => {
    opts.xAxis.data = renderData.value.map((item: any) => item.regionName).reverse()
    opts.series[0].data = renderData.value.map((item: any) => item.creditTotalAmount).reverse()
    opts.series[1].data = renderData.value.map((item: any) => item.guaranteeTotalAmount).reverse()
    return opts
  })
}

const tableConfig = ref<TableUtil.TableConfig>({
  apiFn: fetchManageApiLoanStatisticQueryPageByRegion,
  apiParams: {
    area: null,
    recordDateStart: null,
    recordDateEnd: null
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60
    },
    {
      title: '区域',
      key: 'name',
      width: 120,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '户数',
      key: 'enterpriseNum',
      width: 100,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款笔数',
      key: 'loanNum',
      width: 120,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计授信金额（万元）',
      key: 'sumCreditAmount',
      width: 160,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款备案金额（万元）',
      key: 'sumRecordAmount',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '剩余备案余额（万元）',
      key: 'surplusRecordAmount',
      width: 160,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '信用贷款',
      key: 'count7',
      align: 'center',
      children: [
        {
          title: '贷款笔数',
          key: 'loanNumCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款金额（万元）',
          key: 'sumLoanAmountCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款备案金额（万元）',
          key: 'sumRecordAmountCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷笔数',
          key: 'loanNumFirstCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷金额（万元）',
          key: 'sumLoanAmountFirstCredit',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷户数',
          key: 'enterpriseNumFirstCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    },
    {
      title: '担保贷款',
      key: 'count8',
      align: 'center',
      children: [
        {
          title: '贷款笔数',
          key: 'loanNumGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款金额（万元）',
          key: 'sumLoanAmountGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款备案金额（万元）',
          key: 'sumRecordAmountGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷笔数',
          key: 'loanNumFirstGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷金额（万元）',
          key: 'sumLoanAmountFirstGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷户数',
          key: 'enterpriseNumFirstGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    }
  ]
})

const dataModalRef = ref(null)
const handleOpenModal = () => {
  dataModalRef.value?.open()
}

watch(
  renderData,
  () => {
    updateChart()
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <div class="w-full h-full">
    <wrap-box title="各区域知识价值贷款概览（近一年）">
      <template #right>
        <div
          class="text-16px font-400 text-#76FFFFFF flex items-center justify-end gap-4px cursor-pointer"
          @click="handleOpenModal"
        >
          <span>更多</span>
          <IconIconamoonArrowRight2Bold class="text-16px" />
        </div>
      </template>
      <div
        class="w-full h-full flex-1 flex-shrink-0"
        ref="chartRef"
      ></div>
    </wrap-box>

    <data-modal
      ref="dataModalRef"
      :title="`各区域贷款数据统计`"
      width="80%"
    >
      <div class="w-full h-600px p-30px">
        <YcTable
          :table-config="tableConfig"
          ref="tableRef"
          :scrollX="3200"
          :show-sub-header="false"
          :show-sub-header-border="false"
          :table-theme-overrides="{
            thTextColor: '#FFFFFF',
            tdTextColor: '#FFFFFF',
            borderColor: '#06A6E680',
            tdColorModal: '#082234FF',
            tdColorHoverModal: 'rgb(18,94,136)',
            tdColorStripedModal: '#13435e',
            borderColorModal: '#06A6E680',
            color: 'transparent',
            colorDisabled: 'transparent'
          }"
        />
      </div>
    </data-modal>
  </div>
</template>

<style scoped></style>
