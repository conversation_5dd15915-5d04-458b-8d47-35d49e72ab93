<script setup lang="ts">
import WrapBox from '@/views/board/_components/wrap-box.vue'
import { useStore } from '@/views/board/_context/boardContext'
import ScrollTable from '@/views/board/_components/scroll-table.vue'
import DataModal from '@/views/board/_components/data-modal.vue'
import { fetchManageApiLoanStatisticQueryPageByRegion } from '@/service/api'
import { TableUtil } from '@/typings/table'

const { boardData } = useStore()

const renderData = computed(() => {
  return boardData.value.regionRecord
})

const tableColumns = [
  {
    title: '区域名称',
    key: 'regionName',
    width: '50%'
  },
  {
    title: '达成率',
    key: 'achieveRate',
    unit: '%',
    width: '30%'
  },
  {
    title: '不良率',
    key: 'badLoanRatio',
    unit: '%',
    width: '30%'
  },
  {
    title: '不良笔数',
    key: 'badCount',
    width: '30%',
    unit: '笔'
  },
  {
    title: '不良金额',
    key: 'riskAmount',
    unit: '万元',
    width: '30%'
  }
]

const tableConfig = ref<TableUtil.TableConfig>({
  apiFn: fetchManageApiLoanStatisticQueryPageByRegion,
  apiParams: {
    org: null
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60
    },
    {
      title: '区域',
      key: 'name',
      width: 120,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '户数',
      key: 'enterpriseNum',
      width: 100,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款笔数',
      key: 'loanNum',
      width: 120,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计授信金额（万元）',
      key: 'sumCreditAmount',
      width: 160,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款备案金额（万元）',
      key: 'sumRecordAmount',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '剩余备案余额（万元）',
      key: 'surplusRecordAmount',
      width: 160,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '信用贷款',
      key: 'count7',
      align: 'center',
      children: [
        {
          title: '贷款笔数',
          key: 'loanNumCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款金额（万元）',
          key: 'sumLoanAmountCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款备案金额（万元）',
          key: 'sumRecordAmountCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷笔数',
          key: 'loanNumFirstCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷金额（万元）',
          key: 'sumLoanAmountFirstCredit',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷户数',
          key: 'enterpriseNumFirstCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    },
    {
      title: '担保贷款',
      key: 'count8',
      align: 'center',
      children: [
        {
          title: '贷款笔数',
          key: 'loanNumGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款金额（万元）',
          key: 'sumLoanAmountGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款备案金额（万元）',
          key: 'sumRecordAmountGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷笔数',
          key: 'loanNumFirstGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷金额（万元）',
          key: 'sumLoanAmountFirstGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷户数',
          key: 'enterpriseNumFirstGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    }
  ]
})

const dataModalRef = ref(null)
const curBankName = ref('')
const handleOpenModal = (row) => {
  curBankName.value = row.loanBankName
  tableConfig.value.apiParams.org = row.loanBank
  dataModalRef.value?.open()
}
</script>

<template>
  <div class="w-full h-380px flex-shrink-0">
    <wrap-box title="各区域贷款任务完成情况（本年度）">
      <ScrollTable
        :data="renderData"
        :columns="tableColumns"
        :visible-rows="5"
        :interval="2000"
        :auto-scroll="true"
      />
    </wrap-box>

    <data-modal
      ref="dataModalRef"
      :title="`${curBankName}银行企业贷款数据统计`"
      width="80%"
    >
      <div class="w-full h-600px p-30px">
        <YcTable
          :table-config="tableConfig"
          ref="tableRef"
          :scrollX="3200"
          :show-sub-header="false"
          :show-sub-header-border="false"
          :table-theme-overrides="{
            thTextColor: '#FFFFFF',
            tdTextColor: '#FFFFFF',
            borderColor: '#06A6E680',
            tdColorModal: '#082234FF',
            tdColorHoverModal: 'rgb(18,94,136)',
            tdColorStripedModal: '#13435e',
            borderColorModal: '#06A6E680',
            color: 'transparent',
            colorDisabled: 'transparent'
          }"
        />
      </div>
    </data-modal>
  </div>
</template>

<style scoped></style>
