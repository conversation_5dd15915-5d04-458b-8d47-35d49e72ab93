<script setup lang="ts">
import WrapBox from '@/views/board/_components/wrap-box.vue'
import { useStore } from '@/views/board/_context/boardContext'
import { useEcharts } from '@/hooks/useEcharts'

const { boardData } = useStore()

const renderData = computed(() => {
  return boardData.value.yearLoanRecordAmount
})

const { domRef: chartRef, updateOptions } = useEcharts(() => ({
  grid: {
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: [
      { name: '年度备案金额', itemStyle: { color: '#5FD5ECFF' } },
      { name: '年度备案企业家数', itemStyle: { color: '#3684FFFF' } }
    ],
    right: 'center',
    icon: 'circle',
    itemWidth: 8,
    itemHeight: 8,
    itemGap: 16,
    textStyle: {
      color: '#FFFFFFD9',
      fontSize: 14,
      fontWeight: 500
    }
  },
  xAxis: {
    data: [],
    type: 'category',
    axisLine: {
      show: true,
      lineStyle: {
        color: '#BAE7FFFF',
        width: 2
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      show: true,
      textStyle: {
        color: '#FFFFFFD9',
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 22
      }
    }
  },
  yAxis: [
    {
      type: 'value',
      name: '（亿元）',
      position: 'left',
      nameTextStyle: {
        color: '#FFFFFFD9',
        fontSize: 14,
        fontWeight: 400
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          cap: 'round',
          color: '#FFFFFF38'
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      // min: 0,
      // max: 0,
      // interval: 10,
      axisLabel: {
        show: true,
        formatter: '{value}',
        textStyle: {
          color: '#FFFFFFD9',
          fontSize: 14,
          fontWeight: 500
        }
      }
    },
    {
      type: 'value',
      name: '（家）',
      position: 'right',
      nameTextStyle: {
        color: '#FFFFFFD9',
        fontSize: 14,
        fontWeight: 400
      },
      offset: -15,
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          cap: 'round',
          color: '#FFFFFF38'
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      // min: 0,
      // max: 0,
      // interval: 10,
      axisLabel: {
        show: true,
        formatter: '{value}',
        textStyle: {
          color: '#FFFFFFD9',
          fontSize: 14,
          fontWeight: 500
        }
      }
    }
  ],
  series: [
    {
      name: '年度备案金额',
      type: 'line',
      smooth: false, //平滑曲线显示
      showAllSymbol: true, //显示所有图形。
      symbol: 'circle', //标记的图形为实心圆
      symbolSize: 8, //标记的大小
      itemStyle: {
        //折线拐点标志的样式
        color: '#fff',
        borderColor: '#5FD5ECFF',
        borderWidth: 1
      },
      lineStyle: {
        color: '#5FD5ECFF'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#5FD5EC33' },
            { offset: 1, color: '#5FD5EC00' }
          ]
        }
      },
      data: []
    },
    {
      name: '年度备案企业家数',
      type: 'line',
      smooth: false, //平滑曲线显示
      showAllSymbol: true, //显示所有图形。
      symbol: 'circle', //标记的图形为实心圆
      symbolSize: 8, //标记的大小
      yAxisIndex: 1,
      itemStyle: {
        //折线拐点标志的样式
        color: '#fff',
        borderColor: '#3684FFFF',
        borderWidth: 1
      },
      lineStyle: {
        color: '#3684FFFF'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#2276FC33' },
            { offset: 1, color: '#2276FC00' }
          ]
        }
      },
      data: []
    }
  ]
}))

// 计算双轴刻度对齐参数
function calcAxisParams(data, segments = 5) {
  const max = Math.max(...data)
  const min = Math.min(...data)
  const range = max - min

  // 计算理想间隔（向上取整到合适单位）
  let interval = Math.ceil(range / (segments - 1))
  const magnitude = Math.pow(10, Math.floor(Math.log10(interval)))
  interval = Math.ceil(interval / magnitude) * magnitude

  // 调整最大最小值
  const adjustedMax = Math.ceil(max / interval) * interval
  const adjustedMin = Math.floor(min / interval) * interval

  return { min: adjustedMin, max: adjustedMax, interval }
}

const updateChart = () => {
  updateOptions((opts) => {
    const sortedData = [...renderData.value].sort((a, b) => a.year - b.year)
    const leftData = sortedData.map((item: any) => item.recordTotal)
    const rightData = sortedData.map((item: any) => item.entNum)
    // 获取双轴配置
    const leftAxis = calcAxisParams(leftData)
    const rightAxis = calcAxisParams(rightData)

    opts.xAxis.data = sortedData.map((item: any) => item.year)

    // opts.yAxis[0].min = leftAxis.min
    // opts.yAxis[0].max = leftAxis.max
    // opts.yAxis[0].interval = leftAxis.interval
    //
    // opts.yAxis[1].min = rightAxis.min
    // opts.yAxis[1].max = rightAxis.max
    // opts.yAxis[1].interval = rightAxis.interval

    opts.series[0].data = leftData
    opts.series[1].data = rightData
    return opts
  })
}

watch(
  renderData,
  () => {
    updateChart()
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <div class="w-full h-full">
    <wrap-box title="备案贷款金额趋势">
      <div
        class="w-full h-full"
        ref="chartRef"
      ></div>
    </wrap-box>
  </div>
</template>

<style scoped></style>
