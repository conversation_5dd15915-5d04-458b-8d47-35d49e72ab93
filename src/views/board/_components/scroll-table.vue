<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { useDocumentVisibility, useElementSize, useIntervalFn } from '@vueuse/core'

defineOptions({
  name: 'ScrollTable'
})

const props = withDefaults(
  defineProps<{
    /** 表格数据 */
    data: any[]
    /** 表格列配置 */
    columns: { key: string; title: string; width?: number | string; unit?: string }[]
    /** 行高 */
    rowHeight?: number
    /** 显示的行数 */
    visibleRows?: number
    /** 是否需要滚动（当数据量小于可见行数时） */
    needScroll?: boolean
    /** 滚动间隔(毫秒) */
    interval?: number
    /** 是否自动滚动 */
    autoScroll?: boolean
  }>(),
  {
    rowHeight: 48,
    visibleRows: 10,
    interval: 1000,
    autoScroll: true
  }
)

// 表格容器引用
const tableBodyRef = ref<HTMLElement | null>(null)
const { height: tableHeight } = useElementSize(tableBodyRef)

// 滚动位置（使用像素值而不是索引）
const scrollPosition = ref(0)

// 判断是否需要滚动
const needScroll = computed(() => {
  return props.data.length > props.visibleRows
})

// 处理后的数据（只在需要滚动时复制多份以实现无缝滚动）
const processedData = computed(() => {
  if (!props.data.length) return []

  // 如果数据量小于可见行数，则不复制数据
  if (!needScroll.value) return props.data

  // 复制两份数据以确保滚动时有足够的数据
  return [...props.data, ...props.data]
})

// 计算原始数据的总高度
const originalDataHeight = computed(() => {
  return props.data.length * props.rowHeight
})

// 表格样式
const tableBodyStyle = computed(() => ({
  height: `${props.rowHeight * props.visibleRows}px`,
  overflow: 'hidden'
}))

// 表格内容的样式，控制滚动位置
const tableContentStyle = computed(() => ({
  transform: `translateY(-${scrollPosition.value}px)`,
  transition: scrollTransition.value
}))

// 控制是否应用过渡效果
const scrollTransition = ref('transform 0.5s ease-in-out')

// 自动滚动函数
const { pause, resume } = useIntervalFn(
  () => {
    if (!processedData.value.length) return

    // 增加滚动位置
    scrollPosition.value += props.rowHeight

    // 当滚动到第二份数据的末尾时，无缝重置到第一份数据的相同位置
    if (scrollPosition.value >= originalDataHeight.value + props.rowHeight) {
      // 关闭过渡动画
      pause()
      scrollTransition.value = 'none'
      scrollPosition.value = 0

      // // 在下一帧恢复过渡动画
      requestAnimationFrame(() => {
        resume()
        setTimeout(() => {
          scrollTransition.value = 'transform 0.5s linear'
        }, 0)
      })
    }
  },
  props.interval,
  { immediate: false }
)

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    // 重置位置
    scrollTransition.value = 'none'
    scrollPosition.value = 0

    // 在下一帧恢复过渡动画
    requestAnimationFrame(() => {
      scrollTransition.value = 'transform 0.5s linear'

      if (props.autoScroll && needScroll.value && newData.length) {
        resume()
      } else {
        pause()
      }
    })
  },
  { deep: true }
)

// 监听自动滚动属性
watch(
  () => props.autoScroll,
  (newValue) => {
    if (newValue && props.data.length && needScroll.value) {
      resume()
    } else {
      pause()
    }
  }
)

onMounted(() => {
  if (props.autoScroll && props.data.length && needScroll.value) {
    resume()
  }
})

onBeforeUnmount(() => {
  pause()
})

// 暂停滚动
const stopScroll = () => {
  pause()
}

// 恢复滚动
const startScroll = () => {
  if (props.autoScroll && props.data.length && needScroll.value) {
    resume()
  }
}

defineExpose({
  stopScroll,
  startScroll
})

// 添加文档可见性监听
const documentVisibility = useDocumentVisibility()

// 监听文档可见性变化
watch(documentVisibility, (visibility) => {
  if (visibility === 'visible' && props.autoScroll && props.data.length && needScroll.value) {
    resume()
  } else {
    pause()
  }
})
</script>

<template>
  <div class="scroll-table">
    <!-- 表头 -->
    <div class="scroll-table-header">
      <table class="w-full h-full">
        <colgroup>
          <col
            v-for="col in columns"
            :key="col.key"
            :width="col.width"
          />
        </colgroup>
        <thead class="h-full">
          <tr class="h-full">
            <th
              v-for="col in columns"
              :key="col.key"
              :style="{ width: col.width ? `${col.width}` : 'auto' }"
              class="text-#FFFFFFD9 font-500 pl-16px text-18px text-left"
            >
              {{ col.title }}
            </th>
          </tr>
        </thead>
      </table>
    </div>

    <!-- 表体 -->
    <div
      ref="tableBodyRef"
      class="scroll-table-body h-full"
      :style="tableBodyStyle"
      @mouseenter="stopScroll"
      @mouseleave="startScroll"
    >
      <table class="w-full">
        <colgroup>
          <col
            v-for="col in columns"
            :key="col.key"
            :width="col.width"
          />
        </colgroup>
        <tbody :style="tableContentStyle">
          <tr
            v-for="(item, index) in processedData"
            :key="`${index}`"
            :style="{ height: `${rowHeight}px` }"
            v-memo="[item, rowHeight]"
          >
            <td
              v-for="col in columns"
              :key="col.key"
              :style="{ width: col.width ? `${col.width}` : 'auto' }"
              :class="{ 'cursor-pointer' : col.isClick }"
              @click="() => col.onClick && col.onClick(item)"
              class="text-#FFFFFFD9 font-400 pl-16px text-18px text-left"
            >
              {{ `${item[col.key]}${col.unit || ''}` }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
.scroll-table {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.scroll-table-header {
  height: 62px;
  border-bottom: 1px solid #067ae5;
  background: linear-gradient(180deg, #3e78ce21 0%, #548ec200 100%);
  backdrop-filter: blur(5px);
}

.scroll-table-body {
  position: relative;
}

.scroll-table-body tr:nth-child(odd) {
  background-color: #023059ff;
}

.scroll-table-body tr:last-child td {
  border-bottom: none;
}

table {
  border-collapse: collapse;
  table-layout: fixed;
}
</style>
