<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    className?: string
    title: string
    matchTitle?: string
    num: number | string
    unit?: string
    precision?: number
  }>(),
  {
    title: '标题栏',
    num: 0,
    unit: '',
    precision: 0
  }
)

const renderTitles = computed(() => {
  if (props.matchTitle) {
    const titles = props.title.split(props.matchTitle)
    return [
      {
        title: titles[0],
        match: false
      },
      {
        title: props.matchTitle,
        match: true
      },
      {
        title: titles[1],
        match: false
      }
    ]
  } else {
    return [
      {
        title: props.title,
        match: false
      }
    ]
  }
})
</script>

<template>
  <div
    class="ring-item flex flex-col items-center"
    :class="props.className"
  >
    <div class="flex items-center YouSheBiaoTiHei lh-40px">
      <div class="text-30px font-400 text-#00E4FF">
        <!--        <NNumberAnimation-->
        <!--          :from="0"-->
        <!--          :to="props.num"-->
        <!--          :precision="props.precision"-->
        <!--        />-->
        {{ props.num }}
      </div>
      <div class="text-#12A1B2 font-400 text-16px ml-2px">{{ props.unit }}</div>
    </div>
    <div class="font-400 text-22px font-400 YouSheBiaoTiHei text-white lh-22px">
      <span
        v-for="item in renderTitles"
        :key="item.title"
        :class="{ 'text-#FF7640': item.match }"
        >{{ item.title }}</span
      >
    </div>
    <div class="base" />
  </div>
</template>

<style scoped lang="scss">
.YouSheBiaoTiHei {
  font-family: 'YouSheBiaoTiHei';
}

.base {
  width: 140px;
  height: 68px;
  background: url('@/assets/images/board-base.png') no-repeat center center;
  background-size: 100% 100%;
}
</style>
