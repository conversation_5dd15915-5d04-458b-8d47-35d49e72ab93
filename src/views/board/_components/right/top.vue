<script setup lang="ts">
import WrapBox from '@/views/board/_components/wrap-box.vue'
import { useStore } from '@/views/board/_context/boardContext'
import ScrollTable from '@/views/board/_components/scroll-table.vue'
import DataModal from '@/views/board/_components/data-modal.vue'
import { ref } from 'vue'
import { TableUtil } from '@/typings/table'
import { fetchManageApiDashboardQueryReportList } from '@/service/api'

const { boardData } = useStore()

const renderData = computed(() => {
  return boardData.value.entReport || []
})

const tableColumns = [
  {
    title: '排名',
    key: 'index',
    width: '10%'
  },
  {
    title: '机构名称',
    key: 'organizationName',
    width: '30%'
  },
  {
    title: '企业投放户数',
    key: 'yearNewCount',
    width: '20%'
  },
  {
    title: '本季贷款投放额',
    key: 'quarterLoanAmount',
    width: '20%'
  },
  {
    title: '信用贷款投放额占比',
    key: 'creditLoanAmountPercentage',
    unit: '%',
    width: '20%'
  }
]

const tableConfig = ref<TableUtil.TableConfig>({
  apiFn: fetchManageApiDashboardQueryReportList,
  apiParams: {},
  transformer: (res: any) => {
    const { data = [], rows = [], pageNum = 1, pageNo = 1, pageSize = 10, total = 0 } = res || {}
    const bindPageSize = pageSize <= 0 ? 10 : pageSize
    const recordsWithIndex = [...(data ? data : []), ...(rows ? rows : [])].map((item: any, index: number) => {
      return {
        ...item,
        index: (pageNum - 1) * pageSize + index + 1
      }
    })
    return {
      data: recordsWithIndex,
      pageNum,
      pageNo,
      pageSize: bindPageSize,
      total
    }
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60
    },
    {
      title: '银行机构',
      key: 'organizationName',
      width: 120,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款投放额',
      key: 'count7',
      align: 'center',
      children: [
        {
          title: '当年',
          key: 'yearLoanAmount',
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '当季',
          key: 'quarterLoanAmount',
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '上季',
          key: 'lastQuarterLoanAmount',
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '去年当季',
          key: 'lastYearQuarterLoanAmount',
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '同比',
          key: 'loanAmountYoY',
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '环比',
          key: 'loanAmountMoM',
          ellipsis: {
            tooltip: true
          }
        }
      ]
    },
    {
      title: '现有余额',
      key: 'currentBalance',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '高企贷款',
      key: 'count8',
      align: 'center',
      children: [
        {
          title: '户数',
          key: 'hiTechLoanCount',
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '金额',
          key: 'hiTechLoanAmount',
          ellipsis: {
            tooltip: true
          }
        }
      ]
    },
    {
      title: '科技型中小企业贷款占比',
      key: 'count8',
      align: 'center',
      children: [
        {
          title: '户数',
          key: 'smallTechLoanCount',
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '金额',
          key: 'smallTechLoanAmount',
          ellipsis: {
            tooltip: true
          }
        }
      ]
    },
    {
      title: '信用贷款投放额',
      key: 'firstLoanCount'
    },
    {
      title: '信用贷款投放额占比',
      key: 'creditLoanAmountPercentage'
    },
    {
      title: '季度投放额排名',
      key: 'creditLoanAmountPercentage'
    }
  ]
})

const quarter = computed(() => {
  if (renderData.value && renderData.value.length) {
    return `（${renderData.value[0].year}年第${renderData.value[0].quarter}季度）`
  }
  return ''
})

// 查看更多
const dataModalRef = ref(null)
const handleOpenModal = () => {
  dataModalRef.value?.open()
}
</script>

<template>
  <div class="w-full h-380px flex-shrink-0">
    <wrap-box :title="`全口径科技贷款-投放额排名${quarter}`">
      <template #right>
        <div
          class="text-16px font-400 text-#76FFFFFF w-full flex items-center justify-center gap-4px cursor-pointer"
          @click="handleOpenModal"
        >
          <span>更多</span>
          <IconIconamoonArrowRight2Bold class="text-16px" />
        </div>
      </template>
      <div class="w-full h-full flex flex-col gap-14px">
        <div class="flex-1 flex-shrink-0">
          <ScrollTable
            :data="renderData"
            :columns="tableColumns"
            :visible-rows="5"
            :interval="2000"
            :auto-scroll="true"
          />
        </div>
      </div>
    </wrap-box>

    <data-modal
      ref="dataModalRef"
      :title="`科技贷款情况统计汇总表`"
      width="90%"
    >
      <div class="w-full h-600px p-30px">
        <YcTable
          :table-config="tableConfig"
          ref="tableRef"
          :show-paginate="false"
          :show-sub-header="false"
          :show-sub-header-border="false"
          :table-theme-overrides="{
            thTextColor: '#FFFFFF',
            tdTextColor: '#FFFFFF',
            borderColor: '#06A6E680',
            tdColorModal: '#082234FF',
            tdColorHoverModal: 'rgb(18,94,136)',
            tdColorStripedModal: '#13435e',
            borderColorModal: '#06A6E680',
            color: 'transparent',
            colorDisabled: 'transparent'
          }"
        />
      </div>
    </data-modal>
  </div>
</template>

<style scoped></style>
