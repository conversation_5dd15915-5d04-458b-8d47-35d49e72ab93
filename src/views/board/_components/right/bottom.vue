<script setup lang="ts">
import WrapBox from '@/views/board/_components/wrap-box.vue'
import { useStore } from '@/views/board/_context/boardContext'
import ScrollTable from '@/views/board/_components/scroll-table.vue'

const { boardData } = useStore()

const renderData = computed(() => {
  return boardData.value.orgBadRate
})

const tableColumns = [
  {
    title: '机构名称',
    key: 'loanBankName',
    width: '50%'
  },
  {
    title: '不良率',
    key: 'badLoanRatio',
    unit: '%',
    width: '30%'
  },
  {
    title: '不良笔数',
    key: 'badCount',
    width: '30%',
    unit: '笔'
  },
  {
    title: '不良金额',
    key: 'riskAmount',
    unit: '万元',
    width: '30%'
  }
]
</script>

<template>
  <div class="w-full h-380px flex-shrink-0">
    <wrap-box title="知识价值贷款-各机构不良情况（本年度）">
      <ScrollTable
        :data="renderData"
        :columns="tableColumns"
        :visible-rows="5"
        :interval="2000"
        :auto-scroll="true"
      />
    </wrap-box>
  </div>
</template>

<style scoped></style>
