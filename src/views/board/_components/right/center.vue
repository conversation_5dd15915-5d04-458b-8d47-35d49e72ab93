<script setup lang="ts">
import WrapBox from '@/views/board/_components/wrap-box.vue'
import { useStore } from '@/views/board/_context/boardContext'
import { useEcharts } from '@/hooks/useEcharts'

const { boardData } = useStore()

const renderData = computed(() => {
  return (boardData.value.top10BankCredit || []).slice(0, 5)
})

const { domRef: chartRef, updateOptions } = useEcharts(() => ({
  grid: {
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: [
      { name: '总贷款金额', itemStyle: { color: '#5FD5ECFF' } },
      { name: '贷款家数', itemStyle: { color: '#3684FFFF' } }
    ],
    right: 'center',
    icon: 'circle',
    itemWidth: 8,
    itemHeight: 8,
    itemGap: 16,
    textStyle: {
      color: '#FFFFFFD9',
      fontSize: 14,
      fontWeight: 500
    }
  },
  xAxis: {
    data: [],
    type: 'category',
    axisLine: {
      show: true,
      lineStyle: {
        color: '#BAE7FFFF',
        width: 2
      }
    },
    axisTick: {
      show: false
    },
    // axisLabel: {
    //   show: true,
    //   interval: 0,
    //   rotate: -45,
    //   margin: 14,
    //   textStyle: {
    //     color: '#FFFFFFD9',
    //     fontSize: 14,
    //     fontWeight: 400,
    //     lineHeight: 22
    //   }
    // }
    axisLabel: {
      show: true,
      textStyle: {
        color: '#FFFFFFD9',
        fontSize: 14,
        fontWeight: 400,
        lineHeight: 22
      }
    }
  },
  yAxis: [
    {
      type: 'value',
      name: '（亿元）',
      position: 'left',
      nameTextStyle: {
        color: '#FFFFFFD9',
        fontSize: 14,
        fontWeight: 400
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          cap: 'round',
          color: '#FFFFFF38'
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      // min: 0,
      // max: 0,
      // interval: 10,
      axisLabel: {
        show: true,
        formatter: '{value}',
        textStyle: {
          color: '#FFFFFFD9',
          fontSize: 14,
          fontWeight: 500
        }
      }
    },
    {
      type: 'value',
      name: '（家）',
      position: 'right',
      nameTextStyle: {
        color: '#FFFFFFD9',
        fontSize: 14,
        fontWeight: 400
      },
      offset: -15,
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          cap: 'round',
          color: '#FFFFFF38'
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      // min: 0,
      // max: 0,
      // interval: 10,
      axisLabel: {
        show: true,
        formatter: '{value}',
        textStyle: {
          color: '#FFFFFFD9',
          fontSize: 14,
          fontWeight: 500
        }
      }
    }
  ],
  series: [
    {
      name: '总贷款金额',
      type: 'line',
      smooth: false, //平滑曲线显示
      showAllSymbol: true, //显示所有图形。
      symbol: 'circle', //标记的图形为实心圆
      symbolSize: 8, //标记的大小
      itemStyle: {
        //折线拐点标志的样式
        color: '#fff',
        borderColor: '#5FD5ECFF',
        borderWidth: 1
      },
      lineStyle: {
        color: '#5FD5ECFF'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#5FD5EC33' },
            { offset: 1, color: '#5FD5EC00' }
          ]
        }
      },
      data: []
    },
    {
      name: '贷款家数',
      type: 'line',
      smooth: false, //平滑曲线显示
      showAllSymbol: true, //显示所有图形。
      symbol: 'circle', //标记的图形为实心圆
      symbolSize: 8, //标记的大小
      yAxisIndex: 1,
      itemStyle: {
        //折线拐点标志的样式
        color: '#fff',
        borderColor: '#3684FFFF',
        borderWidth: 1
      },
      lineStyle: {
        color: '#3684FFFF'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#2276FC33' },
            { offset: 1, color: '#2276FC00' }
          ]
        }
      },
      data: []
    }
  ]
}))

const updateChart = () => {
  updateOptions((opts) => {
    const leftData = renderData.value.map((item: any) => item.recordAmount)
    const rightData = renderData.value.map((item: any) => item.entNum)

    opts.xAxis.data = renderData.value.map((item: any) => item.loanBankName)

    opts.series[0].data = leftData
    opts.series[1].data = rightData
    return opts
  })
}

watch(
  renderData,
  () => {
    updateChart()
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <div class="w-full h-full">
    <wrap-box title="知识价值贷款-机构投放情况（TOP5）">
      <div
        class="w-full h-full"
        ref="chartRef"
      ></div>
    </wrap-box>
  </div>
</template>

<style scoped></style>
