<script setup lang="ts">
import WrapBox from '@/views/board/_components/wrap-box.vue'
import { useStore } from '@/views/board/_context/boardContext'
import { useEcharts } from '@/hooks/useEcharts'

const { boardData } = useStore()

const renderData = computed(() => {
  return boardData.value.sciEntTypeMaps
})

const { domRef: chartRef, updateOptions } = useEcharts(() => ({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    data: ['高新技术企业', '科技型中小企业'],
    orient: 'vertical',
    right: '10%',
    top: 'center',
    icon: 'rect',
    itemWidth: 10,
    itemHeight: 10,
    itemGap: 16,
    textStyle: {
      color: '#FFFFFFD9',
      fontSize: 14,
      fontWeight: 500
    }
  },
  series: [
    {
      name: 'bg',
      type: 'custom',
      z: -1,
      coordinateSystem: 'none',
      tooltip: {
        show: false
      },
      renderItem: (params, api) => {
        const center = api.getZr().getWidth() * 0.4
        const height = api.getZr().getHeight()

        // 环形图的外半径是70%，增加20%就是84%
        const radius = height * 0.42 // 70% * 1.2 / 2 = 42%

        return {
          type: 'circle',
          shape: {
            cx: center,
            cy: height * 0.5,
            r: radius
          },
          style: {
            fill: '#1A5180'
          }
        }
      },
      data: [0]
    },
    {
      name: '企业类型分布',
      type: 'pie',
      center: ['40%', '50%'],
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ]
}))

const updateChart = () => {
  updateOptions((opts) => {
    opts.series[1].data = [
      {
        name: '高新技术企业',
        value: renderData.value?.hiTechEntNewFirstLoansCy ?? 0,
        itemStyle: {
          color: '#3DBCBEFF'
        }
      },
      {
        name: '科技型中小企业',
        value: renderData.value?.techSmeNewFirstLoansCy ?? 0,
        itemStyle: {
          color: '#3B5DE8FF'
        }
      }
    ]
    return opts
  })
}

watch(
  renderData,
  () => {
    updateChart()
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <div class="w-full h-full">
    <wrap-box
      title="全口径科技贷款-企业类型分布"
      size="small"
    >
      <div
        class="w-full h-full"
        ref="chartRef"
      ></div>
    </wrap-box>
  </div>
</template>

<style scoped></style>
