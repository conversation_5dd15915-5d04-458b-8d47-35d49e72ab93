<script setup lang="ts">
import { computed, ref, useSlots } from 'vue'
import { Expose } from '@/typings/expose'

interface Emits {
  (e: 'close'): void
}

const props = withDefaults(
  defineProps<{
    title: string
    width?: number | string
    contentCss?: string
  }>(),
  {
    width: '1200px',
    title: 'YC Modal Title'
  }
)

const slots = useSlots()

const emits = defineEmits<Emits>()
const showModal = ref(false)
const modalWidth = computed(() => {
  return typeof props.width === 'number' ? `${props.width}px` : props.width
})

// 打开弹窗
const open = () => {
  showModal.value = true
}

// 关闭弹窗
const close = () => {
  showModal.value = false
  emits('close')
}

defineExpose({
  open,
  close
} as Expose.YCModal)
</script>

<template>
  <NModal
    v-model:show="showModal"
    :auto-focus="false"
    :mask-closable="false"
  >
    <div
      class="bg-transparent"
      :style="{ width: modalWidth }"
    >
      <div class="w-full h-48px flex items-center justify-between">
        <div class="header-title_bg relative">
          <div class="absolute left-8% h-48px w-full text-20px font-500 text-white lh-48px">
            {{ title }}
          </div>
        </div>
        <div
          class="header-title_close cursor-pointer"
          @click="close"
        ></div>
      </div>
      <div class="mt-6px w-full min-h-200px max-h-600px content">
        <NScrollbar class="h-full">
          <slot></slot>
        </NScrollbar>
      </div>
    </div>
  </NModal>
</template>

<style scoped lang="scss">
.header-title_bg {
  width: 60%;
  height: 48px;
  background: url('@/assets/images/board-modal-title.png') no-repeat center center;
  background-size: 100% 100%;
}
.header-title_close {
  width: 58px;
  height: 36px;
  background: url('@/assets/images/board-modal-close.png') no-repeat center center;
  background-size: cover;
}

.content {
  border: 3px solid #3389b4;
  background: #052032;
  box-shadow:
    0 8.09px 11.3px 0 #45aad659 inset,
    8.09px 0 6.47px 0 #053851ad inset,
    -8.09px 0 6.47px 0 #053051ad inset;
}

:deep(.n-data-table) {
  .n-data-table-th {
    background: linear-gradient(180deg, #0f5178 0%, #042d46 100%);
    backdrop-filter: blur(5px);
  }
}

:deep(.n-base-selection) {
  .n-base-selection-label {
    background: transparent !important;
    border: 1px solid #ffffff38;

    .n-base-selection-input {
      color: #ffffff8c;
    }
  }

  .n-base-selection-input {
  }

  .n-base-selection__border {
    border: none !important;
  }

  .n-base-selection__state-border {
    border: 1px solid #ffffff38 !important;
  }
}

:deep(.n-pagination) {
  .n-pagination-item {
    background: transparent !important;
    border: 1px solid #ffffff38 !important;
  }
}

:deep(.n-scrollbar-rail__scrollbar) {
  background: #ffffff38 !important;
}
</style>
