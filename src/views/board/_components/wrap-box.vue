<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    title: string
    titleClass?: string
    size?: 'small' | 'default'
  }>(),
  {
    title: '标题栏',
    size: 'default'
  }
)
</script>

<template>
  <div class="wrap-box w-full h-full relative">
    <div
      class="wrap-box-title"
      :class="`wrap-box-title_${props.size}`"
    >
      <div class="w-full h-46px flex items-center justify-between pl-18px">
        <n-gradient-text
          class="text-20px font-600"
          :gradient="{
            deg: 90,
            from: '#76FFFF',
            to: '#52ACFF'
          }"
        >
          {{ props.title }}
        </n-gradient-text>
        <div class="pr-24%">
          <slot name="right"></slot>
        </div>
      </div>
    </div>
    <div class="wrap-box-content">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.wrap-box {
  flex-shrink: 0;
  border: 1px solid #276ab1;
  background: linear-gradient(180deg, #022f58cc 0%, #104c7fcc 58%);
  backdrop-filter: blur(5px);

  &-title {
    width: 100%;
    height: 76px;
    position: absolute;
    top: -4px;
    left: 0;

    &_default {
      background: url('@/assets/images/board-container-title.png') no-repeat 100% top;
      background-size: 100% 76px;
    }

    &_small {
      background: url('@/assets/images/board-container-title_small.png') no-repeat 100% top;
      background-size: 100% 76px;
    }
  }

  &-content {
    position: relative;
    width: 100%;
    top: 44px;
    height: calc(100% - 44px);
    padding: 14px 18px;
  }
}
</style>
