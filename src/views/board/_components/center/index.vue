<script setup lang="ts">
import Map from './map.vue'
import Bottom from './bottom.vue'
</script>

<template>
  <div class="w-928px h-full flex-shrink-0 flex flex-col justify-between gap-10px">
    <div class="w-full h-full flex-1">
      <Map />
    </div>
    <div class="w-full h-290px flex-shrink-0 flex items-center gap-20px">
      <Bottom />
    </div>
  </div>
</template>

<style scoped></style>
