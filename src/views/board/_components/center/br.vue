<script setup lang="ts">
import WrapBox from '@/views/board/_components/wrap-box.vue'
import { useStore } from '@/views/board/_context/boardContext'
import { useEcharts } from '@/hooks/useEcharts'

const { boardData } = useStore()

const renderData = computed(() => {
  return boardData.value.creditProportionAnalyse
})

const { domRef: chartRef, updateOptions } = useEcharts(() => ({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    data: ['100万以下', '100万-500万', '500万-1000万', '1000万以上'],
    orient: 'vertical',
    right: '0%',
    top: 'center',
    icon: 'rect',
    itemWidth: 10,
    itemHeight: 10,
    itemGap: 16,
    textStyle: {
      color: '#FFFFFFD9',
      fontSize: 14,
      fontWeight: 500
    }
  },
  series: [
    {
      name: 'bg',
      type: 'custom',
      z: -1,
      coordinateSystem: 'none',
      tooltip: {
        show: false
      },
      renderItem: (params, api) => {
        const center = api.getZr().getWidth() * 0.4
        const height = api.getZr().getHeight()

        // 环形图的外半径是70%，增加20%就是84%
        const radius = height * 0.42 // 70% * 1.2 / 2 = 42%

        return {
          type: 'circle',
          shape: {
            cx: center,
            cy: height * 0.5,
            r: radius
          },
          style: {
            fill: '#1A5180'
          }
        }
      },
      data: [0]
    },
    {
      name: '单笔授信比重分布',
      type: 'pie',
      center: ['40%', '50%'],
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ]
}))

const updateChart = () => {
  updateOptions((opts) => {
    const items = [
      { name: '100万以下', key: 'lt100', color: '#BEE5FBFF' },
      { name: '100万-500万', key: 'bt100And500', color: '#30B4FFFF' },
      { name: '500万-1000万', key: 'bt500And1000', color: '#52EB28FF' },
      { name: '1000万以上', key: 'gt1000', color: '#FFE24FFF' }
    ]
    opts.series[1].data = items.map((item: any) => ({
      name: item.name,
      value: renderData.value[item.key],
      itemStyle: {
        color: item.color
      }
    }))
    return opts
  })
}

watch(
  renderData,
  () => {
    updateChart()
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <div class="w-full h-full">
    <wrap-box
      title="单笔授信比重分布"
      size="small"
    >
      <div
        class="w-full h-full"
        ref="chartRef"
      ></div>
    </wrap-box>
  </div>
</template>

<style scoped></style>
