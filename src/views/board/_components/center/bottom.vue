<script setup lang="ts">
import { useStore } from '@/views/board/_context/boardContext'
import dayjs from 'dayjs'

const { boardData } = useStore()

const renderData = computed(() => {
  return boardData.value.loanRecordGeneral
})

const icons = {
  ic1: new URL('@/assets/images/ic-1.png', import.meta.url).href,
  ic2: new URL('@/assets/images/ic-2.png', import.meta.url).href,
  ic3: new URL('@/assets/images/ic-3.png', import.meta.url).href,
  ic4: new URL('@/assets/images/ic-4.png', import.meta.url).href,
  ic5: new URL('@/assets/images/ic-5.png', import.meta.url).href,
  ic6: new URL('@/assets/images/ic-6.png', import.meta.url).href
}
</script>

<template>
  <div class="w-full h-full flex flex-col gap-24px">
    <div class="flex items-center gap-28px">
      <div class="box-item">
        <div class="flex items-center gap-12px">
          <div class="box-item-icon">
            <img
              :src="icons.ic1"
              class="w-38px"
            />
          </div>
          <div class="text-16px font-400 text-#FFFFFF">历年累计<br />知识价值贷款金额</div>
        </div>
        <div class="flex items-center gap-4px YouSheBiaoTiHei">
          <div class="text-26px font-700 text-#fff">{{ renderData.loanTotalAmount || 0 }}</div>
          <div class="text-14px font-400 text-#FFFFFF8C mt-8px">亿元</div>
        </div>
      </div>
      <div class="box-item">
        <div class="flex items-center gap-12px">
          <div class="box-item-icon">
            <img
              :src="icons.ic2"
              class="w-38px"
            />
          </div>
          <div class="text-16px font-400 text-#FFFFFF">历年累计<br />知识价值贷款企业家数</div>
        </div>
        <div class="flex items-center gap-4px YouSheBiaoTiHei">
          <div class="text-26px font-700 text-#fff">{{ renderData.entNum || 0 }}</div>
          <div class="text-14px font-400 text-#FFFFFF8C mt-8px">家</div>
        </div>
      </div>
    </div>
    <div class="flex items-center gap-28px">
      <div class="box-item">
        <div class="flex items-center gap-12px">
          <div class="box-item-icon">
            <img
              :src="icons.ic3"
              class="w-38px"
            />
          </div>
          <div class="text-16px font-400 text-#FFFFFF">{{ dayjs().year() }}年累计<br />知识价值贷款金额</div>
        </div>
        <div class="flex items-center gap-4px YouSheBiaoTiHei">
          <div class="text-26px font-700 text-#fff">{{ renderData.creditAmountCurrentYear || 0 }}</div>
          <div class="text-14px font-400 text-#FFFFFF8C mt-8px">亿元</div>
        </div>
      </div>
      <div class="box-item">
        <div class="flex items-center gap-12px">
          <div class="box-item-icon">
            <img
              :src="icons.ic4"
              class="w-38px"
            />
          </div>
          <div class="text-16px font-400 text-#FFFFFF">{{ dayjs().year() }}年累计<br />知识价值贷款企业家数</div>
        </div>
        <div class="flex items-center gap-4px YouSheBiaoTiHei">
          <div class="text-26px font-700 text-#fff">{{ renderData.creditEntCountCurrentYear || 0 }}</div>
          <div class="text-14px font-400 text-#FFFFFF8C mt-8px">家</div>
        </div>
      </div>
    </div>
    <div class="flex items-center gap-28px">
      <div class="box-item">
        <div class="flex items-center gap-12px">
          <div class="box-item-icon">
            <img
              :src="icons.ic5"
              class="w-38px"
            />
          </div>
          <div class="text-16px font-400 text-#FFFFFF">2025年<br />担保贷款金额</div>
        </div>
        <div class="flex items-center gap-4px YouSheBiaoTiHei">
          <div class="text-26px font-700 text-#fff">{{ renderData.guaranteeAmountCurrentYear || 0 }}</div>
          <div class="text-14px font-400 text-#FFFFFF8C mt-8px">亿元</div>
        </div>
      </div>
      <div class="box-item">
        <div class="flex items-center gap-12px">
          <div class="box-item-icon">
            <img
              :src="icons.ic6"
              class="w-38px"
            />
          </div>
          <div class="text-16px font-400 text-#FFFFFF">2025年<br />担保贷款企业家数</div>
        </div>
        <div class="flex items-center gap-4px YouSheBiaoTiHei">
          <div class="text-26px font-700 text-#fff">{{ renderData.guaranteeEntCountCurrentYear || 0 }}</div>
          <div class="text-14px font-400 text-#FFFFFF8C mt-8px">家</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.box-item {
  width: 100%;
  height: 78px;
  background: url('@/assets/images/num-box.png') no-repeat center center;
  background-size: 100% 100%;
  padding: 0 18px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &-icon {
    padding-right: 12px;
    height: 38px;
    border-right: 1px solid #10557c;
  }
}
</style>
