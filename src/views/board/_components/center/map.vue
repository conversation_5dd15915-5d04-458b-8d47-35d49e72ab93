<script setup lang="ts">
import { useEcharts } from '@/hooks/useEcharts'
import HunanGeo<PERSON>son from '@/assets/geoJson/hunan.json'
import { useStore } from '@/views/board/_context/boardContext'
import DataModal from '@/views/board/_components/data-modal.vue'
import { ref } from 'vue'
import { TableUtil } from '@/typings/table'
import { fetchManageApiLoanStatisticQueryPageByRegionOrg } from '@/service/api'
import dayjs from 'dayjs'

const { boardData } = useStore()
const currentDate = ref(dayjs().format('YYYY年MM月DD日'))

const mapData = computed(() => {
  return boardData.value.regionRecord
})

const loanRecordGeneralData = computed(() => {
  return boardData.value.loanRecordGeneral
})

const dataModalRef = ref(null)
const dataModalTitle = ref('')

const tableConfig = ref<TableUtil.TableConfig>({
  apiFn: fetchManageApiLoanStatisticQueryPageByRegionOrg,
  apiParams: {
    region: null,
    recordDateStart: null,
    recordDateEnd: null
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60
    },
    {
      title: '机构名称',
      key: 'name',
      width: 120,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '户数',
      key: 'enterpriseNum',
      width: 100,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款笔数',
      key: 'loanNum',
      width: 120,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计授信金额（万元）',
      key: 'sumCreditAmount',
      width: 160,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款备案金额（万元）',
      key: 'sumRecordAmount',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '信用贷款',
      key: 'count7',
      align: 'center',
      children: [
        {
          title: '贷款笔数',
          key: 'loanNumCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款金额（万元）',
          key: 'sumLoanAmountCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款备案金额（万元）',
          key: 'sumRecordAmountCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷笔数',
          key: 'loanNumFirstCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷金额（万元）',
          key: 'sumLoanAmountFirstCredit',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷户数',
          key: 'enterpriseNumFirstCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    },
    {
      title: '担保贷款',
      key: 'count8',
      align: 'center',
      children: [
        {
          title: '贷款笔数',
          key: 'loanNumGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款金额（万元）',
          key: 'sumLoanAmountGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款备案金额（万元）',
          key: 'sumRecordAmountGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷笔数',
          key: 'loanNumFirstGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷金额（万元）',
          key: 'sumLoanAmountFirstGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷户数',
          key: 'enterpriseNumFirstGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    }
  ]
})

const { domRef: mapRef, updateOptions } = useEcharts(
  () => ({
    tooltip: {
      trigger: 'item',
      padding: 0,
      formatter: (params) => {
        const dataItem = mapData.value.find((item: any) => item.regionName === params.name)
        return `
          <div class="min-w-216px relative py-8px rounded-2px" style="background: #031D37B3; backdrop-filter: blur(4px);">
            <div class="h-34px w-full px-16px flex items-center gap-6px text-18px font-600 text-#fff" style="background: linear-gradient(90deg, #14fbff99 0%, #35fbfe00 100%);">
              <sapn>●</sapn>
              <span>${params.name}</span>
            </div>
            <div class="px-16px pt-10px py-4px flex flex-col gap-6px">
             <div class="w-full flex items-center justify-between">
                <div class="text-#FFFFFFCC font-400 text-16px">达成率</div>
                <div class="text-#fff font-600 text-16px">${dataItem?.achieveRate ?? 0}%</div>
              </div>
              <div class="w-full flex items-center justify-between">
                <div class="text-#FFFFFFCC font-400 text-16px">贷款企业</div>
                <div class="text-#fff font-600 text-16px">${dataItem?.entNum ?? 0}家</div>
              </div>
              <div class="w-full flex items-center justify-between">
                <div class="text-#FFFFFFCC font-400 text-16px">贷款笔数</div>
                <div class="text-#fff font-600 text-16px">${dataItem?.recordNum ?? 0}笔</div>
              </div>
              <div class="w-full flex items-center justify-between">
                <div class="text-#FFFFFFCC font-400 text-16px">贷款金额</div>
                <div class="text-#fff font-600 text-16px">${dataItem?.loanTotal ?? 0}万元</div>
              </div>
            </div>
            <img src="${new URL('@/assets/images/box-tl.png', import.meta.url).href}" alt="" class="absolute left-[-4px] top-[-4px] w-36px h-36px" />
            <img src="${new URL('@/assets/images/box-tr.png', import.meta.url).href}" alt="" class="absolute right-[-4px] top-[-4px] w-36px h-36px" />
            <img src="${new URL('@/assets/images/box-bl.png', import.meta.url).href}" alt="" class="absolute left-[-4px] bottom-[-4px] w-36px h-36px" />
            <img src="${new URL('@/assets/images/box-br.png', import.meta.url).href}" alt="" class="absolute right-[-4px] bottom-[-4px] w-36px h-36px" />
         `
      }
    },
    geo: {
      map: 'hunan',
      show: true,
      roam: false,
      zoom: 1.2,
      aspectScale: 0.9,
      label: {
        show: false
      },
      layoutSize: '100%',
      itemStyle: {
        borderColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0.44, color: '#52ACFF' },
            { offset: 1, color: '#76FFFF' }
          ]
        },
        borderWidth: 3,
        shadowColor: 'rgba(10,76,139,1)',
        shadowOffsetY: 0,
        shadowBlur: 60
      }
    },
    visualMap: {
      type: 'continuous',
      min: 0,
      max: 100,
      calculable: false,
      inRange: { color: ['#205D96', '#14395B'] }, // 非前三名的渐变蓝
      textStyle: { color: '#fff' },
      bottom: 30,
      show: false
    },
    series: [
      {
        name: '湖南省',
        type: 'map',
        map: 'hunan',
        zoom: 1.2,
        aspectScale: 0.9,
        itemStyle: {
          areaColor: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#205D96' },
              { offset: 1, color: '#14395B' }
            ]
          },
          borderColor: '#2472BC',
          borderWidth: 1
        },
        label: {
          show: true,
          formatter(params) {
            const name = params.name
            return `{circle|${'●' + '\n'}} {name|${name.length > 5 ? name.slice(0, 5) + '\n' + name.slice(5) : name}}`
          },
          rich: {
            circle: {
              color: 'rgba(67,255,210,0.9)',
              fontSize: 20,
              lineHeight: 22
            },
            name: {
              color: 'rgba(255,255,255,0.9)',
              fontSize: 16,
              fontWeight: 400,
              lineHeight: 22
            }
          }
        },
        emphasis: {
          itemStyle: {
            areaColor: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#205D96' },
                { offset: 1, color: '#14395B' }
              ]
            },
            borderWidth: 3,
            borderColor: '#35D0E8FF'
          }
        },
        select: {
          disabled: true
        },
        data: []
      }
    ]
  }),
  {
    onInitBefore: (echarts) => {
      echarts.registerMap('hunan', HunanGeoJson)
    },
    onClick: (params) => {
      const { seriesType, data } = params
      if (seriesType !== 'map') return
      if (!data) return window?.$message?.warning('该区域暂无数据')
      dataModalTitle.value = `${params.name}企业贷款备案数据统计`
      tableConfig.value.apiParams.region = data.value
      dataModalRef.value?.open()
    }
  }
)

// 3. 颜色生成函数
function getRankColor(rank) {
  return (
    {
      1: [
        { offset: 0, color: '#fdd752' },
        { offset: 1, color: '#fea722' }
      ], // 第一名红色
      2: [
        { offset: 0, color: '#02c6ff' },
        { offset: 1, color: '#3779ff' }
      ], // 第二名金色
      3: [
        { offset: 0, color: '#90f7ec' },
        { offset: 1, color: '#32ccbc' }
      ] // 第三名绿色
    }[rank] || [
      { offset: 0, color: '#205D96' },
      { offset: 1, color: '#14395B' }
    ]
  ) // 非前三名返回null保持visualMap渐变
}

const updateMap = () => {
  updateOptions((opts) => {
    // 1. 数据预处理（找出前三名）
    const rawData = mapData.value.map((item: any) => ({
      name: item.regionName,
      value: item.achieveRate
    }))
    console.log(rawData)
    // 按值降序排序并标记前三名
    const sortedData = [...rawData].sort((a, b) => b.value - a.value)
    const top3Names = sortedData.slice(0, 3).map((item) => item.name)
    // 2. 生成带样式的数据
    const styledData = rawData.map((item) => ({
      ...item,
      // 根据排名设置专属颜色
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: getRankColor(top3Names.indexOf(item.name) + 1)
        }
      }
    }))

    opts.series[0].data = styledData
    return opts
  })
}

watch(
  mapData,
  () => {
    updateMap()
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <div class="w-full h-full pt-40px pb-10px relative flex flex-col items-center relative">
    <div class="text-20px font-500 text-#BED9FFFF pb-4%">*统计数据：截止{{ currentDate }}</div>
    <div
      class="map w-600px h-680px"
      ref="mapRef"
    ></div>
    <div class="map-ring">
      <div class="map-ring-inner"></div>
    </div>

    <div
      class="absolute left-0 bottom-18px w-116px h-138px bg-#031D3799 rounded-8px px-12px py-16px border-1px border-solid border-#5EE7FF flex flex-col gap-8px"
      style="backdrop-filter: blur(4.7px)"
    >
      <div class="text-16px font-500 text-white">达成率</div>
      <div class="flex items-center gap-12px">
        <div
          class="w-30px h-14px"
          style="background: linear-gradient(94deg, #fdd752 1.29%, #fea722 98.31%)"
        ></div>
        <div class="text-14px font-400 text-#fff">第一名</div>
      </div>
      <div class="flex items-center gap-12px">
        <div
          class="w-30px h-14px"
          style="background: linear-gradient(94deg, #02c6ff 1.29%, #3779ff 98.31%)"
        ></div>
        <div class="text-14px font-400 text-#fff">第二名</div>
      </div>
      <div class="flex items-center gap-12px">
        <div
          class="w-30px h-14px"
          style="background: linear-gradient(135deg, #90f7ec 0%, #32ccbc 100%)"
        ></div>
        <div class="text-14px font-400 text-#fff">第三名</div>
      </div>
    </div>
    <div
      class="absolute right-0 bottom-18px bg-#031D3799 rounded-8px px-12px py-16px border-1px border-solid border-#5EE7FF flex flex-col gap-8px"
      style="backdrop-filter: blur(4.7px)"
    >
      <div class="text-16px font-500 text-white">{{ dayjs().year() }}年平均贷款利率：{{ loanRecordGeneralData.avgLoanRate || 0 }}%</div>
    </div>
  </div>

  <data-modal
    ref="dataModalRef"
    :title="dataModalTitle"
    width="80%"
  >
    <div class="w-full h-600px p-30px">
      <YcTable
        :table-config="tableConfig"
        ref="tableRef"
        :scrollX="2800"
        :show-sub-header="false"
        :show-sub-header-border="false"
        :table-theme-overrides="{
          thTextColor: '#FFFFFF',
          tdTextColor: '#FFFFFF',
          borderColor: '#06A6E680',
          tdColorModal: '#082234FF',
          tdColorHoverModal: 'rgb(18,94,136)',
          tdColorStripedModal: '#13435e',
          borderColorModal: '#06A6E680',
          color: 'transparent',
          colorDisabled: 'transparent'
        }"
      />
    </div>
  </data-modal>
</template>

<style scoped lang="scss">
.map {
}

.map-ring {
  position: absolute;
  left: 0;
  bottom: 60px;
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 100px;

  &-inner {
    width: 100%;
    height: 100%;
    background: url('@/assets/images/board-map-ring.png') no-repeat center center;
    background-size: 100% 100%;
  }
}
</style>
