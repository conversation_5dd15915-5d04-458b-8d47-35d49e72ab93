<script setup lang="ts">
import WrapBox from '@/views/board/_components/wrap-box.vue'
import { useStore } from '@/views/board/_context/boardContext'
import { useEcharts } from '@/hooks/useEcharts'

const { boardData } = useStore()

const renderData = computed(() => {
  return boardData.value.loanTypeAnalyse
})

const { domRef: chartRef, updateOptions } = useEcharts(() => ({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    data: ['信用贷款', '担保贷款'],
    orient: 'vertical',
    right: '0%',
    top: 'center',
    icon: 'rect',
    itemWidth: 10,
    itemHeight: 10,
    itemGap: 16,
    textStyle: {
      color: '#FFFFFFD9',
      fontSize: 14,
      fontWeight: 500
    }
  },
  series: [
    {
      name: 'bg',
      type: 'custom',
      z: -1,
      coordinateSystem: 'none',
      tooltip: {
        show: false
      },
      renderItem: (params, api) => {
        const center = api.getZr().getWidth() * 0.4
        const height = api.getZr().getHeight()

        // 环形图的外半径是70%，增加20%就是84%
        const radius = height * 0.42 // 70% * 1.2 / 2 = 42%

        return {
          type: 'circle',
          shape: {
            cx: center,
            cy: height * 0.5,
            r: radius
          },
          style: {
            fill: '#1A5180'
          }
        }
      },
      data: [0]
    },
    {
      name: '贷款担保方式类型分析',
      type: 'pie',
      center: ['40%', '50%'],
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ]
}))

const updateChart = () => {
  updateOptions((opts) => {
    opts.series[1].data = renderData.value.map((item: any) => ({
      name: item.loanType === 'CREDIT' ? '信用贷款' : '担保贷款',
      value: item.recordNum,
      itemStyle: {
        color: item.loanType === 'CREDIT' ? '#3DBCBEFF' : '#3B5DE8FF'
      }
    }))
    return opts
  })
}

watch(
  renderData,
  () => {
    updateChart()
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <div class="w-full h-full">
    <wrap-box
      title="贷款担保方式类型分析"
      size="small"
    >
      <div
        class="w-full h-full"
        ref="chartRef"
      ></div>
    </wrap-box>
  </div>
</template>

<style scoped></style>
