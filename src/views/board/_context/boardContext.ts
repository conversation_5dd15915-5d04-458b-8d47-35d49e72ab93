import { useContext } from '~/packages/hooks'
import { fetchManageApiDashboardQueryDashboard } from '@/service/api'
import { ref } from 'vue'

interface BoardData {
  top10BankCredit: any[]
  regionRecord: any[]
  lastHalfYearCredit: any[]
  lastHalfYearLoan: any[]
  top5RegionLoan: any[]
  loanRecordGeneral: any[]
  loanTypeAnalyse: any[]
  creditProportionAnalyse: any[]
  entAreaCount: any[]
  entReport: any[]
  yearLoanRecordAmount: any[]
  sciEntLoanTypeMap: any
  sciEntTypeMaps: any
  orgBadRate: any
}

export const { setupStore, useStore } = useContext('boardStore', () => {
  const boardData = ref<BoardData>({
    top10BankCredit: [],
    regionRecord: [],
    lastHalfYearCredit: [],
    lastHalfYearLoan: [],
    top5RegionLoan: [],
    loanRecordGeneral: [],
    loanTypeAnalyse: [],
    creditProportionAnalyse: [],
    entAreaCount: [],
    entReport: [],
    yearLoanRecordAmount: [],
    sciEntLoanTypeMap: {},
    sciEntTypeMaps: {},
    orgBadRate: []
  })
  fetchManageApiDashboardQueryDashboard({}).then((res) => {
    const { data, error } = res
    if (!error) {
      boardData.value = Object.assign(boardData.value, data)
    }
  })
  return {
    boardData: boardData
  }
})
