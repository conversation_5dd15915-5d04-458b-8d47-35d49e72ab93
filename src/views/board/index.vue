<script setup lang="ts">
import autofit from 'autofit.js'
import Left from './_components/left/index.vue'
import Center from './_components/center/index.vue'
import Right from './_components/right/index.vue'
import { setupStore } from './_context/boardContext'
import { useRouterPush } from '@/hooks/useRouterPush'

setupStore()

const { routerBack } = useRouterPush()

onMounted(() => {
  autofit.init({
    el: '#board-fit',
    dw: 1920,
    dh: 1210
  })
})

onUnmounted(() => {
  autofit.off()
})
</script>

<template>
  <div
    class="board"
    id="board-fit"
  >
    <div class="board-title">
      <div
        class="board-title-back font-500 text-18px text-white lh-26px cursor-pointer"
        @click="routerBack"
      >
        返回管理后台
      </div>
    </div>
    <div class="board-content">
      <Left />
      <Center />
      <Right />
    </div>
  </div>
</template>

<style scoped lang="scss">
.board {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #0c243a;
  background: url('@/assets/images/board-bg.png') no-repeat center center;
  background-size: 100% 100%;

  &-title {
    width: 100%;
    height: 164px;
    position: absolute;
    top: 0;
    left: 0;
    background: url('@/assets/images/board-title.png') no-repeat center top;
    background-size: 100% auto;

    &-back {
      position: absolute;
      left: 54px;
      top: 12px;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url('@/assets/images/board-back.png') no-repeat center center;
        background-size: cover;
        margin-right: 10px;
      }
    }
  }

  &-content {
    width: 100%;
    height: calc(100% - 90px);
    position: absolute;
    top: 90px;
    left: 0;
    padding: 0 20px 32px 20px;
    display: flex;
    gap: 20px;
  }
}
</style>
