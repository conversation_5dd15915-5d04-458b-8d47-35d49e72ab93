import { loanPurposeModeOptions } from '@/constants/app'
import type { FormConfig } from '@/typings/dynamic-form'

interface IParams {
  rowData: any
  query: Record<string, any>
}
export const getFormList = (params: IParams): FormConfig => {
  const isView = unref(params?.query)?.type === 'view'
  return [
    {
      title: '基础信息',
      items: [
        {
          type: 'input',
          field: 'socialCreditCode',
          label: '统一社会信用代码',
          span: 7,
          disabled: true
        },
        {
          type: 'input',
          field: 'entName',
          label: '企业名称',
          span: 7,
          disabled: true
        },
        {
          type: 'input',
          field: 'enterprisePrincipal',
          label: '企业负责人',
          span: 7,
          disabled: true
        },
        {
          type: 'input',
          field: 'enterpriseContact',
          label: '企业联系人',
          span: 7,
          disabled: true
        },
        {
          type: 'input',
          field: 'mobilePhoneNumber',
          label: '手机号',
          span: 7,
          disabled: true
        },
        {
          type: 'input',
          field: 'landlinePhoneNumber',
          label: '座机号',
          span: 7,
          disabled: true
        }
      ]
    },

    {
      title: '贷款信息',
      items: [
        {
          type: 'input',
          field: 'serialNo',
          label: '流水号',
          span: 7,
          disabled: true
        },
        {
          type: 'select',
          field: 'loanType',
          label: '贷款方式',
          span: 7,
          disabled: true,
          options: [
            { label: '信用贷款', value: 'CREDIT' },
            { label: '担保贷款', value: 'GUARANTEE' }
          ]
        },
        {
          type: 'input',
          field: 'loanBankName',
          label: '贷款发放银行',
          span: 7,
          disabled: true
        },
        {
          type: 'input',
          field: 'guaranteeOrgName',
          label: '担保机构',
          span: 7,
          disabled: true
        },
        {
          type: 'select',
          field: 'loanPurpose',
          label: '贷款用途',
          disabled: true,
          span: 7,
          options: loanPurposeModeOptions,
          props: {
            placeholder: '请选择'
          }
        },
        {
          type: 'input',
          field: 'regionName',
          label: '区域',
          span: 7,
          disabled: true
        },
        {
          type: 'inputgroupNumber',
          field: 'loanAmount',
          label: '贷款金额',
          span: 7,
          disabled: true,
          props: {
            triggerText: '万元',
            showButton: false
          }
        },
        {
          type: 'inputgroupNumber',
          field: 'recordAmount',
          label: '申请备案金额',
          span: 7,
          disabled: true,
          props: {
            triggerText: '万元',
            showButton: false
          }
        },
        {
          type: 'inputgroupNumber',
          field: 'loanRate',
          label: '贷款利率',
          span: 7,
          disabled: true,
          props: {
            triggerText: '%',
            showButton: false
          }
        },
        {
          type: 'inputgroupNumber',
          field: 'guaranteeFeeRate',
          label: '担保费率',
          span: 7,
          disabled: true,
          props: {
            triggerText: '%',
            showButton: false
          }
        },
        {
          type: 'inputgroup',
          field: 'loanTerm',
          label: '主债权期限',
          span: 7,
          disabled: true,
          props: {
            triggerText: '%',
            showButton: false
          }
        },
        {
          type: 'input',
          field: 'loanBeginDate',
          label: '贷款开始日期',
          span: 7,
          disabled: true
        },
        {
          type: 'input',
          field: 'loanEndDate',
          label: '贷款结束日期',
          span: 7,
          disabled: true
        },
        {
          type: 'input',
          field: 'loanContractNo',
          label: '贷款合同编号',
          span: 7,
          disabled: true
        },
        {
          type: 'input',
          field: 'bankPermissionContractNo',
          label: '银行保证合同编号',
          span: 7,
          disabled: true
        },
        {
          type: 'input',
          field: 'entrustedGuaranteeLetterNo',
          label: '委托担保协议编号',
          span: 7,
          disabled: true
        },
        {
          type: 'NRadioGroup',
          field: 'firstTimeBorrower',
          label: '首贷户',
          tooltip: '按人民银行首贷户口径',
          span: 7,
          disabled: true,
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 }
          ]
        },
        {
          type: 'NRadioGroup',
          field: 'principalFreeRenewal',
          label: '无还本续贷',
          span: 7,
          disabled: true,
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 }
          ]
        }
      ]
    },

    {
      title: '其他信息',
      items: [
        {
          type: 'uploadFile',
          field: 'LOAN_CONTRACT',
          label: '贷款合同',
          tooltip: '线上贷款可提供相关系统截图',
          span: 7,
          disabled: true,
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'uploadFile',
          field: 'PAYING_RECEIPT_VOUCHER',
          label: '放款凭证',
          tooltip: '线上贷款可提供相关系统截图',
          span: 7,
          disabled: true,
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'uploadFile',
          field: 'BANK_PERMISSION_CONTRACT',
          label: '银行保证合同',
          span: 7,
          disabled: true,
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'uploadFile',
          field: 'OTHER',
          label: '其他',
          span: 7,
          disabled: true,
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'input',
          field: 'remark',
          label: '备注',
          span: 10,
          disabled: true,
          props: {
            type: 'textarea',
            rows: 3,
            class: '!w-full'
          }
        }
      ]
    },

    {
      title: '风险补偿信息',
      items: [
        {
          type: 'input',
          field: 'receiptAccount',
          label: '收款账号',
          span: 7,
          required: true,
          disabled: isView
        },
        {
          type: 'input',
          field: 'receiptName',
          label: '收款户名',
          span: 7,
          required: true,
          disabled: isView
        },
        {
          type: 'inputgroupNumber',
          field: 'compensationRatio',
          label: '风补比例',
          span: 7,
          required: true,
          disabled: isView,
          RuleType: 'number',
          props: {
            showButton: false,
            triggerText: '%'
          }
        },
        {
          type: 'inputgroupNumber',
          field: 'principalLossAmount',
          label: '本金损失金额',
          span: 7,
          required: true,
          disabled: isView,
          RuleType: 'number',
          props: {
            showButton: false,
            triggerText: '万元',
            max: params.rowData.recordAmount || 0
          }
        },
        {
          type: 'inputgroupNumber',
          field: 'appliedCompensationAmount',
          label: '申请风补金额',
          span: 7,
          required: true,
          disabled: isView,
          RuleType: 'number',
          props: {
            showButton: false,
            triggerText: '万元'
          }
        },
        {
          type: 'input',
          field: 'riskRemark',
          label: '备注',
          span: 7,
          disabled: isView
        },
        {
          type: 'uploadFile',
          field: 'COMPENSATION_APPLICATION_FORM',
          label: '补偿申请表',
          span: 7,
          required: true,
          disabled: isView,
          RuleType: 'array',
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'uploadFile',
          field: 'COMPENSATION_NOTICE',
          label: '代偿通知书',
          span: 7,
          required: true,
          disabled: isView,
          RuleType: 'array',
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'uploadFile',
          field: 'COMPENSATION_CERTIFICATE',
          label: '代偿证明',
          span: 7,
          required: true,
          disabled: isView,
          RuleType: 'array',
          props: {
            class: '!w-full'
          }
        },
        {
          type: 'uploadFile',
          field: 'COMPENSATION_TRANSACTION_RECORD',
          label: '代偿流水',
          span: 7,
          required: true,
          disabled: isView,
          RuleType: 'array',
          props: {
            class: '!w-full'
          }
        }
      ]
    }
  ]
}
