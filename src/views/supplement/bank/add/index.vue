<template>
  <yc-form-page :loading="mainLoading">
    <DynamicForm
      :config="formConfig"
      :initial-data="formData"
      @event="handleFormEvent"
      @update:modelValue="handleDataChange"
      ref="DynamicFormRef"
    >
      <!-- 自定义插槽示例 -->
    </DynamicForm>
    <div
      class="bg-white p-10px"
      v-if="isView"
    >
      <BorderTitleBar
        title="审批记录"
        class="mb-10px !w-4/5 !w-3/5"
      />
      <AuditProcess :instance-id="instanceId"></AuditProcess>
    </div>
    <template #footer>
      <div class="w-full flex items-center justify-end gap-12px btnBox">
        <NButton @click="handleCancel">取消</NButton>
        <NButton
          strong
          secondary
          type="info"
          @click="handleSave"
          :loading="saveLoading"
          v-if="isEdit"
          >保存草稿</NButton
        >
        <NButton
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          v-if="isEdit"
          >提交</NButton
        >
      </div>
    </template>
  </yc-form-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DynamicForm from '@/components/business/dynamicForm.vue'
import AuditProcess from '@/components/business/auditProcess.vue'
import type { FormConfig } from '@/typings/dynamic-form'
import { getFormList } from './form'
import { useLoading } from '~/packages/hooks/src'
import YcFormPage from '@/components/common/yc-form-page.vue'
import {
  fetchManageApiAuditAuditCredit,
  fetchManageApiAuditAuditGuarantee,
  fetchManageApiRiskEdit,
  fetchManageApiRiskQueryDetail
} from '@/service/api'
import { cloneDeep, groupBy } from 'lodash-es'
import { useRouterPush } from '@/hooks/useRouterPush'
import { $formatTime } from '@/globals'

const route = useRoute()
const { routerBack } = useRouterPush()
const query = computed(() => {
  return route.query
})

// 表单数据
const formData: any = ref({})

// 是否为编辑
const isEdit = computed(() => query.value.type === 'edit')
// 是否为查看
const isView = computed(() => query.value.type === 'view')
// 工作流id
const instanceId = computed(() =>
  formData.value.loanType === 'CREDIT' ? formData.value.creditAuditInstanceId : formData.value.guaranteeAuditInstanceId
)

const formList: Ref<FormConfig | any> = ref([])
const formConfig = computed(() => formList.value)
const handleFormEvent = (payload: any) => {
  console.log('表单事件:', payload)
}

const handleDataChange = (data: any) => {
  console.log('数据更新:', data)
}

// 取消
const handleCancel = () => {
  routerBack()
}

// 保存为草稿
const riskFileKeys = ['COMPENSATION_APPLICATION_FORM', 'PAYING_RECEIPT_VOUCHER', 'BANK_COLLECTION_EVIDENCE', 'LETTER_OF_COMMITMENT']
const DynamicFormRef: any = ref(null)
const { loading: saveLoading, startLoading: startSaveLoading, endLoading: endSaveLoading } = useLoading()
const handleSave = async (isLoading: boolean = true) => {
  await DynamicFormRef.value.validate()
  const dynamicFormData = DynamicFormRef.value.getFormData()
  const fileList: any[] = []
  riskFileKeys.forEach((key) => {
    if (dynamicFormData[key] && dynamicFormData[key].length) {
      const files = dynamicFormData[key].map((item: any) => {
        return {
          fileUrl: item.url,
          fileName: item.name,
          fileType: 'OTHER',
          fileBusiness: key
        }
      })
      fileList.push(...files)
    }
  })
  const params = Object.assign({}, cloneDeep(dynamicFormData), { fileList: fileList })
  try {
    isLoading && startSaveLoading()
    const { error } = await fetchManageApiRiskEdit(params)
    if (!error && isLoading) {
      window?.$message?.success('保存成功')
      handleCancel()
    }
    if (error) {
      throw new Error('保存失败')
    }
  } finally {
    isLoading && endSaveLoading()
  }
}

// 提交
const { loading: submitLoading, startLoading: startSubmitLoading, endLoading: endSubmitLoading } = useLoading()
const handleSubmit = async () => {
  try {
    startSubmitLoading()
    await handleSave(false)
    // serialNo
    const dynamicFormData = DynamicFormRef.value.getFormData()
    const fetchApi = dynamicFormData.loanType === 'CREDIT' ? fetchManageApiAuditAuditCredit : fetchManageApiAuditAuditGuarantee
    const { error } = await fetchApi({
      instanceId: dynamicFormData.loanType === 'CREDIT' ? dynamicFormData.creditAuditInstanceId : dynamicFormData.guaranteeAuditInstanceId,
      action: 'PASS'
    })
    if (!error) {
      window?.$message?.success('提交成功')
      handleCancel()
    }
  } finally {
    endSubmitLoading()
  }
}

// 页面初始化
const { loading: mainLoading, startLoading: startMainLoading, endLoading: endMainLoading } = useLoading()
const init = async () => {
  try {
    startMainLoading()
    const { data } = await fetchManageApiRiskQueryDetail({ id: query.value.id })
    const files = groupBy(data.fileList, 'fileBusiness')
    Object.keys(files).forEach((key) => {
      files[key] = files[key].map((item: any) => {
        return {
          ...item,
          url: item.fileUrl,
          name: item.fileName,
          status: 'finished'
        }
      })
    })
    data.loanBeginDate = $formatTime(data.loanBeginDate, 'YYYY-MM-DD')
    data.loanEndDate = $formatTime(data.loanEndDate, 'YYYY-MM-DD')
    if (isEdit) {
      data.appliedCompensationAmount = !$isEmpty(data.appliedCompensationAmount) ? data.appliedCompensationAmount : data.recordAmount
    }
    formData.value = Object.assign({}, data, files)
    formList.value = getFormList({ query: query.value, rowData: data }) as FormConfig
  } finally {
    endMainLoading()
  }
}

onMounted(() => {
  init()
})
</script>

<style scoped>
.form-container {
  padding-bottom: 70px;
}
.btnBox {
  height: 56px;
  background: #fff;
  box-shadow: 0 -5px 10px -1px #0000000d;
  position: fixed;
  bottom: 0;
  width: calc(100% - 270px);
}
</style>
