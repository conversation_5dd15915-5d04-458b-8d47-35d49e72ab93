<script setup lang="ts">
import { NButton } from 'naive-ui'
import type { Ref } from 'vue'
import { h, ref } from 'vue'
import { useYcTable } from '@/hooks/useYcTable'
import type { TableUtil } from '@/typings/table'
import { CreditAuditStatusModeRecord } from '@/constants/app'
import { fetchManageApiAuditRecall, fetchManageApiRiskQueryPage, fetchManageApiRiskRemoveRiskAudit } from '@/service/api'
import { useRouterPush } from '@/hooks/useRouterPush'
import { $formatTime } from '@/globals'

const { tableRef } = useYcTable()

const { routerPushByKey } = useRouterPush()

const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchManageApiRiskQueryPage,
  apiParams: {
    loanType: 'CREDIT',
    loanBank: null,
    socialCreditCode: null,
    entName: null
  },
  columns: () => [
    {
      type: 'selection',
      width: 50,
      fixed: 'left'
    },
    {
      title: '风险补偿状态',
      key: 'creditAuditStatus',
      width: 140,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => CreditAuditStatusModeRecord[rowData.creditAuditStatus as UnionKey.CreditAuditStatus]
          }
        )
      }
    },
    {
      title: '流水号',
      key: 'serialNo',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业名称',
      width: 200,
      key: 'entName',
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          NButton,
          {
            type: 'primary',
            text: true,
            onClick: () => {
              routerPushByKey('supplement_bank_add', { query: { type: 'view', id: rowData.id as string } })
            }
          },
          {
            default: () => rowData.entName
          }
        )
      }
    },
    {
      title: '企业信用代码',
      key: 'socialCreditCode',
      width: 200,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款金额（万元）',
      key: 'loanAmount',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '申请金额（万元）',
      key: 'recordAmount',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款开始日期',
      key: 'loanBeginDate',
      width: 140,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => $formatTime(rowData.loanBeginDate as string, 'YYYY-MM-DD')
          }
        )
      }
    },
    {
      title: '贷款结束日期',
      key: 'loanEndDate',
      width: 140,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => $formatTime(rowData.loanEndDate as string, 'YYYY-MM-DD')
          }
        )
      }
    },
    {
      title: '贷款期限（月）',
      key: 'loanTerm',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '利率（%）',
      key: 'loanRate',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return $isEmpty(rowData.loanRate) ? '--' : `${rowData.loanRate}%`
      }
    },
    {
      title: '贷款发放银行',
      key: 'loanBankName',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业负责人',
      key: 'enterprisePrincipal',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业联系人',
      key: 'enterpriseContact',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '手机号',
      key: 'mobilePhoneNumber',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '座机号',
      key: 'landlinePhoneNumber',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '区域',
      key: 'regionName',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '首贷户',
      key: 'firstTimeBorrower',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => ($isEmpty(rowData.firstTimeBorrower) ? '--' : rowData.firstTimeBorrower ? '是' : '否')
          }
        )
      }
    },
    {
      title: '备注',
      key: 'remark',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    }
  ]
})

// 查询
const handleSearch = () => {
  tableRef.value?.refreshData(true)
}

// 重置
const handleReset = () => {
  checkedRowKeys.value = []
  tableConfig.value.apiParams = {
    loanType: 'CREDIT',
    loanBank: null,
    socialCreditCode: null,
    entName: null
  }
  tableRef.value?.refreshData(true)
}

// 选中行
const checkedRowKeys = ref([])
// 编辑
const handleEdit = () => {
  if (!checkedRowKeys.value.length) {
    return window?.$message?.warning('请选择需要编辑的数据')
  }
  if (checkedRowKeys.value.length > 1) {
    return window?.$message?.warning('只能选择一条数据进行编辑')
  }
  const rowData = tableRef.value?.currentPageData.find((item: any) => item.id === checkedRowKeys.value[0])
  if (!rowData) {
    return window?.$message?.warning('未找到需要撤回的数据')
  }
  if (!['TO_BE_SUBMITTED', 'FOO'].includes(rowData.creditAuditStatus)) {
    return window?.$message?.warning('当前状态无法编辑')
  }
  routerPushByKey('supplement_bank_add', {
    query: {
      type: 'edit',
      id: checkedRowKeys.value[0]
    }
  })
}

// 删除
const handleDelete = async () => {
  if (!checkedRowKeys.value.length) {
    return window?.$message?.warning('请选择需要删除的数据')
  }
  if (checkedRowKeys.value.length > 1) {
    return window?.$message?.warning('只能选择一条数据进行删除')
  }
  const rowData = tableRef.value?.currentPageData.find((item: any) => item.id === checkedRowKeys.value[0])
  if (!rowData) {
    return window?.$message?.warning('未找到需要撤回的数据')
  }
  if (!['TO_BE_SUBMITTED', 'FOO'].includes(rowData.creditAuditStatus)) {
    return window?.$message?.warning('当前状态无法删除')
  }
  const res = await fetchManageApiRiskRemoveRiskAudit({ id: checkedRowKeys.value[0] })
  if (!res.error) {
    window?.$message?.success('删除成功')
    checkedRowKeys.value = []
    handleSearch()
  }
}

// 撤回
const handleWithdraw = async () => {
  if (!checkedRowKeys.value.length) {
    return window?.$message?.warning('请选择需要要撤回的数据')
  }
  if (checkedRowKeys.value.length > 1) {
    return window?.$message?.warning('只能选择一条数据进行撤回')
  }
  const rowData = tableRef.value?.currentPageData.find((item: any) => item.id === checkedRowKeys.value[0])
  if (!rowData) {
    return window?.$message?.warning('未找到需要撤回的数据')
  }
  if (!['REGIONAL_AUDITING'].includes(rowData.creditAuditStatus)) {
    return window?.$message?.warning('当前状态无法撤回')
  }
  const res = await fetchManageApiAuditRecall({ id: rowData.id, loanType: rowData.loanType })
  if (!res.error) {
    window?.$message?.success('撤回成功')
    handleSearch()
  }
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="银行风险补偿" />
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
      v-model:checkedRowKeys="checkedRowKeys"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
          ><YcSearchItem label="企业名称">
            <NInput
              v-model:value="tableConfig.apiParams.entName"
              placeholder="请输入企业名称"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="贷款发放银行">
            <NInput
              v-model:value="tableConfig.apiParams.loanBank"
              placeholder="请输入贷款发放银行"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="企业信用代码">
            <NInput
              v-model:value="tableConfig.apiParams.socialCreditCode"
              placeholder="请输入企业信用代码"
              clearable
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <div class="flex items-center gap-8px">
          <NButton @click="handleEdit">编辑</NButton>
          <NButton @click="handleWithdraw">撤回</NButton>
          <NButton @click="handleDelete">删除</NButton>
        </div>
      </template>
    </YcTable>
  </YcContent>
</template>

<style lang="scss">
.aline:hover {
  text-decoration: underline;
}
/* 在全局样式添加 */
.n-collapse-transition {
  transition:
    height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: height;
}

/* 防止flex布局导致的过渡问题 */
.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  overflow: hidden; /* 防止内容溢出影响动画 */
}
.search-container {
  display: block;
  min-width: 100%;
  contain: content; /* 限制浏览器重绘范围 */
}
</style>
