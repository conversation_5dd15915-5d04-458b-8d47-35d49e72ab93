<script setup lang="ts">
import { NButton } from 'naive-ui'
import type { Ref } from 'vue'
import { h, ref } from 'vue'
import { useYcTable } from '@/hooks/useYcTable'
import type { TableUtil } from '@/typings/table'
import { CreditAuditStatusModeRecord, GuaranteeAuditStatusModeRecord } from '@/constants/app'
import { fetchManageApiAuditKill, fetchManageApiRiskPaid, fetchManageApiRiskQueryTodoList } from '@/service/api'
import { useRouterPush } from '@/hooks/useRouterPush'
import { $formatTime } from '@/globals'
import { hasRole } from '@/utils/role'

const { tableRef } = useYcTable()

const { routerPushByKey } = useRouterPush()

const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchManageApiRiskQueryTodoList,
  apiParams: {
    loanBank: null,
    socialCreditCode: null,
    entName: null
  },
  columns: () => [
    {
      type: 'selection',
      width: 50,
      fixed: 'left'
    },
    {
      title: '风险补偿状态',
      key: 'creditAuditStatus',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () =>
              rowData.loanType === 'CREDIT'
                ? CreditAuditStatusModeRecord[rowData.creditAuditStatus as UnionKey.CreditAuditStatus]
                : GuaranteeAuditStatusModeRecord[
                    (hasRole(['REGION_ADMIN']) ? rowData.regionAuditStatus : rowData.guaranteeAuditStatus) as UnionKey.GuaranteeAuditStatus
                  ]
          }
        )
      }
    },
    {
      title: '接收时间',
      key: 'createdTime',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '流水号',
      key: 'serialNo',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款方式',
      key: 'loanType',
      width: 140,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => ($isEmpty(rowData.loanType) ? '--' : rowData.loanType === 'CREDIT' ? '信用贷款' : '担保贷款')
          }
        )
      }
    },
    {
      title: '企业名称',
      width: 200,
      key: 'entName',
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          NButton,
          {
            type: 'primary',
            text: true,
            onClick: () => {
              routerPushByKey('supplement_todo_info', {
                query: {
                  type: 'audit',
                  id: rowData.id as string
                }
              })
            }
          },
          {
            default: () => rowData.entName
          }
        )
      }
    },
    {
      title: '企业信用代码',
      key: 'socialCreditCode',
      width: 200,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款金额（万元）',
      key: 'loanAmount',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '申请金额（万元）',
      key: 'recordAmount',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款开始日期',
      key: 'loanBeginDate',
      width: 140,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => $formatTime(rowData.loanBeginDate as string, 'YYYY-MM-DD')
          }
        )
      }
    },
    {
      title: '贷款结束日期',
      key: 'loanEndDate',
      width: 180,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => $formatTime(rowData.loanEndDate as string, 'YYYY-MM-DD')
          }
        )
      }
    },
    {
      title: '贷款期限（月）',
      key: 'loanTerm',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '利率（%）',
      key: 'loanRate',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return $isEmpty(rowData.loanRate) ? '--' : `${rowData.loanRate}%`
      }
    },
    {
      title: '贷款发放银行',
      key: 'loanBankName',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业负责人',
      key: 'enterprisePrincipal',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业联系人',
      key: 'enterpriseContact',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '手机号',
      key: 'mobilePhoneNumber',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '座机号',
      key: 'landlinePhoneNumber',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '区域',
      key: 'regionName',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '首贷户',
      key: 'firstTimeBorrower',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => ($isEmpty(rowData.firstTimeBorrower) ? '--' : rowData.firstTimeBorrower ? '是' : '否')
          }
        )
      }
    },
    {
      title: '备注',
      key: 'remark',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    }
  ]
})

// 选中行
const checkedRowKeys = ref([])

// 查询
const handleSearch = () => {
  checkedRowKeys.value = []
  tableRef.value?.refreshData(true)
}

// 重置
const handleReset = () => {
  tableConfig.value.apiParams = {
    loanBank: null,
    socialCreditCode: null,
    entName: null
  }
  handleSearch()
}

// 审批
const handleAudit = async (action: 'PASS' | 'REJECT') => {
  if (!checkedRowKeys.value.length) {
    return window?.$message?.warning('请选择需要要审批的数据')
  }
  if (checkedRowKeys.value.length > 1) {
    return window?.$message?.warning('只能选择一条数据进行审批')
  }
  const rowData = tableRef.value?.currentPageData.find((item: any) => item.id === checkedRowKeys.value[0])
  const auditStatus =
    rowData.loanType === 'CREDIT'
      ? rowData.creditAuditStatus
      : hasRole(['REGION_ADMIN'])
        ? rowData.regionAuditStatus
        : rowData.guaranteeAuditStatus
  if (!rowData) {
    return window?.$message?.warning('未找到需要审批的数据')
  }
  if (['AWAITING_VOUCHER_SUPPLEMENT', 'PENDING_PAYMENT', 'PAID', 'TERMINATED'].includes(auditStatus)) {
    return window?.$message?.warning('当前状态无法审批')
  }
  const fetchApi = rowData.loanType === 'CREDIT' ? fetchManageApiAuditAuditCredit : fetchManageApiAuditAuditGuarantee
  const res = await fetchApi({
    instanceId:
      rowData.loanType === 'CREDIT'
        ? rowData.creditAuditInstanceId
        : hasRole(['REGION_ADMIN'])
          ? rowData.regionAuditInstanceId
          : rowData.guaranteeAuditInstanceId,
    action,
    auditType: hasRole(['REGION_ADMIN']) ? 'REGION_AUDIT' : 'PROVINCE_AUDIT'
  })
  if (!res.error) {
    window?.$message?.success('审批成功')
    handleSearch()
  }
}

// 上传资金划拨凭证
const handleUpload = () => {
  if (!checkedRowKeys.value.length) {
    return window?.$message?.warning('请选择需要上传资金划拨凭证的数据')
  }
  if (checkedRowKeys.value.length > 1) {
    return window?.$message?.warning('只能选择一条数据进行上传资金划拨凭证')
  }
  const rowData = tableRef.value?.currentPageData.find((item: any) => item.id === checkedRowKeys.value[0])
  const auditStatus =
    rowData.loanType === 'CREDIT'
      ? rowData.creditAuditStatus
      : hasRole(['REGION_ADMIN'])
        ? rowData.regionAuditStatus
        : rowData.guaranteeAuditStatus
  if (!rowData) {
    return window?.$message?.warning('未找到需要上传资金划拨凭证的数据')
  }
  if (['PAID', 'TERMINATED'].includes(auditStatus)) {
    return window?.$message?.warning('当前状态无法上传资金划拨凭证')
  }
  routerPushByKey('supplement_todo_info', {
    query: {
      type: 'upload',
      id: checkedRowKeys.value[0]
    }
  })
}

// 已拨付
const handlePaid = async () => {
  if (!checkedRowKeys.value.length) {
    return window?.$message?.warning('请选择操作已拨付的数据')
  }
  if (checkedRowKeys.value.length > 1) {
    return window?.$message?.warning('只能选择一条数据进行操作')
  }
  const rowData = tableRef.value?.currentPageData.find((item: any) => item.id === checkedRowKeys.value[0])
  const auditStatus =
    rowData.loanType === 'CREDIT'
      ? rowData.creditAuditStatus
      : hasRole(['REGION_ADMIN'])
        ? rowData.regionAuditStatus
        : rowData.guaranteeAuditStatus
  if (!rowData) {
    return window?.$message?.warning('未找到需要操作的数据')
  }
  if (['PAID', 'TERMINATED'].includes(auditStatus)) {
    return window?.$message?.warning('当前状态无法进行已拨付操作')
  }
  const res = await fetchManageApiRiskPaid({ id: rowData.id, loanType: rowData.loanType })
  if (!res.error) {
    window?.$message?.success('已拨付操作成功')
    handleSearch()
  }
}

// 终止
const handleKill = async () => {
  if (!checkedRowKeys.value.length) {
    return window?.$message?.warning('请选择需要终止的数据')
  }
  if (checkedRowKeys.value.length > 1) {
    return window?.$message?.warning('只能选择一条数据进行终止')
  }
  const rowData = tableRef.value?.currentPageData.find((item: any) => item.id === checkedRowKeys.value[0])
  const auditStatus =
    rowData.loanType === 'CREDIT'
      ? rowData.creditAuditStatus
      : hasRole(['REGION_ADMIN'])
        ? rowData.regionAuditStatus
        : rowData.guaranteeAuditStatus
  if (!rowData) {
    return window?.$message?.warning('未找到需要终止的数据')
  }
  if (['PAID', 'TERMINATED'].includes(auditStatus)) {
    return window?.$message?.warning('当前状态无法终止')
  }
  const dialogIns = window?.$dialog?.warning({
    title: '提示',
    content: () => {
      return h('div', { class: 'text-14px font-500' }, '终止后将会释放企业贷款额度，禁止再次提交风险补偿申请，是否确认中止该数据？')
    },
    action: () =>
      h(
        NButton,
        {
          type: 'primary',
          onClick: async () => {
            unref(dialogIns)?.destroy()
            const instanceId = rowData.loanType === 'CREDIT' ? rowData.creditAuditInstanceId : rowData.guaranteeAuditInstanceId
            const res = await fetchManageApiAuditKill({ instanceId })
            if (!res.error) {
              window?.$message?.success('终止成功')
              handleSearch()
            }
          }
        },
        { default: () => '确定' }
      )
  })
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="待办事项" />
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
      v-model:checkedRowKeys="checkedRowKeys"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
          ><YcSearchItem label="企业名称">
            <NInput
              v-model:value="tableConfig.apiParams.entName"
              placeholder="请输入企业名称"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="贷款发放银行">
            <NInput
              v-model:value="tableConfig.apiParams.loanBank"
              placeholder="请输入贷款发放银行"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="企业信用代码">
            <NInput
              v-model:value="tableConfig.apiParams.socialCreditCode"
              placeholder="请输入企业信用代码"
              clearable
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <div class="flex items-center gap-8px">
          <NButton
            @click="handleAudit('PASS')"
            v-permission="'supplement_todo:pass'"
          >
            同意
          </NButton>
          <NButton
            @click="handleAudit('REJECT')"
            v-permission="'supplement_todo:reject'"
          >
            退回
          </NButton>
          <NButton
            @click="handleKill"
            v-permission="'supplement_todo:kill'"
          >
            终止
          </NButton>
          <NButton
            @click="handleUpload"
            v-permission="'supplement_todo:upload'"
          >
            上传资金划拨凭证
          </NButton>
          <NButton
            @click="handlePaid"
            v-permission="'supplement_todo:paid'"
          >
            已拨付
          </NButton>
        </div>
      </template>
    </YcTable>
  </YcContent>
</template>
