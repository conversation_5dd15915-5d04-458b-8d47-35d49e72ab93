<script setup lang="ts">
import type { Ref } from 'vue'
import { h, ref } from 'vue'
import { useYcTable } from '@/hooks/useYcTable'
import type { TableUtil } from '@/typings/table'
import { CreditAuditStatusModeRecord, GuaranteeAuditStatusModeRecord } from '@/constants/app'
import { fetchManageApiRiskQueryDoneList } from '@/service/api'
import { useRouterPush } from '@/hooks/useRouterPush'
import { createTextButton } from '@/utils/common'
import { $formatTime } from '@/globals'
import { hasRole } from '@/utils/role'

const { tableRef } = useYcTable()

const { routerPushByKey } = useRouterPush()

const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchManageApiRiskQueryDoneList,
  apiParams: {
    loanBank: null,
    socialCreditCode: null,
    entName: null
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 80
    },
    {
      title: '风险补偿状态',
      key: 'creditAuditStatus',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () =>
              rowData.loanType === 'CREDIT'
                ? CreditAuditStatusModeRecord[rowData.creditAuditStatus as UnionKey.CreditAuditStatus]
                : GuaranteeAuditStatusModeRecord[
                    (hasRole(['REGION_ADMIN']) ? rowData.regionAuditStatus : rowData.guaranteeAuditStatus) as UnionKey.GuaranteeAuditStatus
                  ]
          }
        )
      }
    },
    {
      title: '流水号',
      key: 'serialNo',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业名称',
      width: 200,
      key: 'entName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业信用代码',
      key: 'socialCreditCode',
      width: 200,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款金额（万元）',
      key: 'loanAmount',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '申请金额（万元）',
      key: 'recordAmount',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款开始日期',
      key: 'loanBeginDate',
      width: 140,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => $formatTime(rowData.loanBeginDate as string, 'YYYY-MM-DD')
          }
        )
      }
    },
    {
      title: '贷款结束日期',
      key: 'loanEndDate',
      width: 140,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => $formatTime(rowData.loanEndDate as string, 'YYYY-MM-DD')
          }
        )
      }
    },
    {
      title: '贷款期限（月）',
      key: 'loanTerm',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '利率（%）',
      key: 'loanRate',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return $isEmpty(rowData.loanRate) ? '--' : `${rowData.loanRate}%`
      }
    },
    {
      title: '贷款发放银行',
      key: 'loanBankName',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业负责人',
      key: 'enterprisePrincipal',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业联系人',
      key: 'enterpriseContact',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '手机号',
      key: 'mobilePhoneNumber',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '座机号',
      key: 'landlinePhoneNumber',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '区域',
      key: 'regionName',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '首贷户',
      key: 'firstTimeBorrower',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => ($isEmpty(rowData.firstTimeBorrower) ? '--' : rowData.firstTimeBorrower ? '是' : '否')
          }
        )
      }
    },
    {
      title: '备注',
      key: 'remark',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '操作',
      key: '__operate',
      width: 80,
      fixed: 'right',
      render: (rowData) => {
        const viewBtn = createTextButton({
          text: '查看',
          onClick: () => {
            routerPushByKey('supplement_done_info', {
              query: { id: rowData.id as string, type: 'view' }
            })
          }
        })
        return h('div', {}, [viewBtn])
      }
    }
  ]
})

// 选中行
const checkedRowKeys = ref([])

// 查询
const handleSearch = () => {
  checkedRowKeys.value = []
  tableRef.value?.refreshData(true)
}

// 重置
const handleReset = () => {
  checkedRowKeys.value = []
  tableConfig.value.apiParams = {
    loanBank: null,
    socialCreditCode: null,
    entName: null
  }
  tableRef.value?.refreshData(true)
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="已办事项" />
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
      v-model:checkedRowKeys="checkedRowKeys"
      scroll-x="3000"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
          ><YcSearchItem label="企业名称">
            <NInput
              v-model:value="tableConfig.apiParams.entName"
              placeholder="请输入企业名称"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="贷款发放银行">
            <NInput
              v-model:value="tableConfig.apiParams.loanBank"
              placeholder="请输入贷款发放银行"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="企业信用代码">
            <NInput
              v-model:value="tableConfig.apiParams.socialCreditCode"
              placeholder="请输入企业信用代码"
              clearable
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
    </YcTable>
  </YcContent>
</template>
