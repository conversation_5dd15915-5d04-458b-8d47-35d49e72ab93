<template>
  <yc-form-page :loading="mainLoading">
    <DynamicForm
      :config="formConfig"
      :initial-data="formData"
      @event="handleFormEvent"
      @update:modelValue="handleDataChange"
      ref="DynamicFormRef"
    >
      <!-- 自定义插槽示例 -->
    </DynamicForm>
    <div class="bg-white p-10px">
      <BorderTitleBar
        title="审批记录"
        class="!w-4/5 !w-3/5"
      />
      <template v-if="formData.loanType === 'CREDIT'">
        <AuditProcess
          :instanceId="formData.creditAuditInstanceId"
          class="pt-10px"
        ></AuditProcess>
      </template>
      <template v-else>
        <div class="flex flex-col gap-12px">
          <n-tabs
            type="line"
            v-model:value="guaranteeAuditInstanceId"
          >
            <n-tab :name="formData.regionAuditInstanceId">实施区域审批</n-tab>
            <n-tab :name="formData.guaranteeAuditInstanceId">省级机构审批</n-tab>
          </n-tabs>
          <AuditProcess :instanceId="guaranteeAuditInstanceId"></AuditProcess>
        </div>
      </template>
    </div>
    <template #footer>
      <div class="w-full flex items-center justify-end gap-12px btnBox">
        <NButton @click="handleCancel">取消</NButton>
      </div>
    </template>
  </yc-form-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DynamicForm from '@/components/business/dynamicForm.vue'
import AuditProcess from '@/components/business/auditProcess.vue'
import { getFormList } from './form'
import { useLoading } from '~/packages/hooks/src'
import YcFormPage from '@/components/common/yc-form-page.vue'
import { fetchManageApiRiskQueryDetail } from '@/service/api'
import { groupBy } from 'lodash-es'
import { useRouterPush } from '@/hooks/useRouterPush'
import { $formatTime } from '@/globals'

const route = useRoute()
const router = useRouter()
const { routerPushByKey, routerBack } = useRouterPush()
const query = computed(() => {
  return route.query
})

// 是否为编辑
const isEdit = computed(() => query.value.type === 'edit')
// 是否为查看
const isView = computed(() => query.value.type === 'view')
// 是否为审批
const isAudit = computed(() => query.value.type === 'audit')
// 是否为上传资金划拨凭证
const isUpload = computed(() => query.value.type === 'upload')

const formData: any = ref({})

const guaranteeAuditInstanceId = ref(null)

const formList = ref([])
const formConfig = computed(() => formList.value)
const handleFormEvent = (payload: any) => {
  console.log('表单事件:', payload)
  // formConfig.value[1].items[1].label = '111111111'
}

const handleDataChange = (data: any) => {
  console.log('数据更新:', data)
}

// 取消
const handleCancel = () => {
  routerBack()
}

// 保存为草稿
const DynamicFormRef: any = ref(null)

// 页面初始化
const { loading: mainLoading, startLoading: startMainLoading, endLoading: endMainLoading } = useLoading()
const init = async () => {
  try {
    startMainLoading()
    const { data } = await fetchManageApiRiskQueryDetail({ id: query.value.id })
    formData.value = Object.assign({}, data)
    const files = groupBy(data.fileList, 'fileBusiness')
    Object.keys(files).forEach((key) => {
      files[key] = files[key].map((item: any) => {
        return {
          ...item,
          url: item.fileUrl,
          name: item.fileName,
          status: 'finished'
        }
      })
    })
    data.loanBeginDate = $formatTime(data.loanBeginDate, 'YYYY-MM-DD')
    data.loanEndDate = $formatTime(data.loanEndDate, 'YYYY-MM-DD')
    formData.value = Object.assign({}, data, files)
    formList.value = getFormList({ query, loanType: data.loanType })
    guaranteeAuditInstanceId.value = formData.value.regionAuditInstanceId || null
  } finally {
    endMainLoading()
  }
}

onMounted(() => {
  init()
})
</script>
