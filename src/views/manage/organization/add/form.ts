import { FormConfig } from '@/typings/dynamic-form'

export const formList: FormConfig = [
  {
    title: '合作担保机构信息',
    items: [
      {
        label: '担保机构名称',
        type: 'input',
        field: 'orgName',
        required: true
      },
      {
        label: '区域',
        type: 'RegionSelect',
        field: 'region',
        fieldLabel: 'regionName',
        required: true
      },
      {
        label: '风补比例',
        type: 'inputgroupNumber',
        field: 'compensationRatio',
        RuleType: 'number',
        props: {
          showButton: false,
          triggerText: '%',
          min: 0,
          max: 100
        }
      },
      {
        label: '收款账号',
        type: 'input',
        field: 'receiptAccount'
      },
      {
        label: '收款户名',
        type: 'input',
        field: 'receiptName'
      }
    ]
  }
]
