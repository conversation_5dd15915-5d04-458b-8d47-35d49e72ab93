<script setup lang="ts">
import YcFormPage from '@/components/common/yc-form-page.vue'

import { formList } from './form'
import DynamicForm from '@/components/business/dynamicForm.vue'
import type { FormConfig } from '@/typings/dynamic-form'
import { ref } from 'vue'
import useLoading from '~/packages/hooks/src/use-loading'
import { useRouterPush } from '@/hooks/useRouterPush'

const { routerBack, route } = useRouterPush()

// 是否为编辑
const isEdit = computed(() => {
  return route.value.query.id
})

// 表单配置
const formConfig: any = ref<FormConfig>(formList)
// 表单数据
const formData = ref({
  orgType: 'GUARANTEE',
  orgName: null,
  region: null,
  compensationRatio: null,
  receiptAccount: null,
  receiptName: null
})

// 表单事件
const curRegionName = ref(null)
const handleFormEvent = (payload: any) => {
  const { field, item } = payload
  if (field === 'region') {
    curRegionName.value = item.regionSelectLabel
  }
}

// 取消
const handleCancel = () => {
  routerBack()
}

// 提交
const DynamicFormRef: any = ref(null)
const { loading: submitLoading, startLoading: startSubmitLoading, endLoading: endSubmitLoading } = useLoading()
const handleSubmit = async () => {
  await DynamicFormRef.value.validate()
  const dynamicFormData = DynamicFormRef.value.getFormData()
  dynamicFormData.regionName = curRegionName.value ? curRegionName.value : dynamicFormData.regionName
  const params = Object.assign(formData.value, dynamicFormData)
  try {
    startSubmitLoading()
    const fetchApi = isEdit.value ? fetchManageApiOrgUpdate : fetchManageApiOrgSave
    await fetchApi(params)
    window?.$message?.success(isEdit.value ? '编辑成功' : '新增成功')
    routerBack()
  } catch (error) {
    endSubmitLoading()
  }
}

// 初始化
const init = async () => {
  if (isEdit.value) {
    const res = await fetchManageApiOrgQueryPage({ id: route.value.query.id, pageNo: 1, pageSize: 1 })
    formData.value = res.data?.list?.[0] || {}
  }
}
init()
</script>

<template>
  <YcFormPage>
    <DynamicForm
      :config="formConfig"
      :initial-data="formData"
      @event="handleFormEvent"
      class="mb-10px"
      ref="DynamicFormRef"
    />
    <template #footer>
      <NButton @click="handleCancel"> 取消 </NButton>
      <NButton
        type="primary"
        @click="handleSubmit"
        :loading="submitLoading"
        >提交</NButton
      >
    </template>
  </YcFormPage>
</template>
