<template>
  <YcContent>
    <BorderTitleBar title="担保机构管理" />
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
        >
          <YcSearchItem label="担保机构">
            <NInput
              v-model:value="tableConfig.apiParams.orgName"
              placeholder="请输入担保机构名称"
            />
          </YcSearchItem>
          <YcSearchItem label="区域">
            <RegionSelect v-model="tableConfig.apiParams.region" />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <div class="flex items-center gap-8px">
          <NButton
            type="primary"
            @click="handleAdd"
            >新增</NButton
          >
          <NButton @click="handleImport">导入</NButton>
        </div>
      </template>
    </YcTable>

    <YcImport
      ref="ycImportRef"
      @success="handleSearch"
    />
  </YcContent>
</template>

<script setup lang="ts">
import { useRouterPush } from '@/hooks/useRouterPush'
import { Expose } from '@/typings/expose'
import { TableUtil } from '@/typings/table'
import { createConfirmTextButton, createTextButton } from '@/utils/common'
import RegionSelect from '@/components/business/regionSelect.vue'

const { routerPush } = useRouterPush()

const tableRef = ref<Expose.YCTable>()
const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchManageApiOrgQueryPage,
  apiParams: {
    orgType: 'GUARANTEE',
    orgName: null,
    region: null
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 80
    },
    {
      title: '担保机构名称',
      key: 'orgName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '区域',
      key: 'regionName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '收款账号',
      key: 'receiptAccount',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '收款户名',
      key: 'receiptName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '风补比例',
      key: 'compensationRatio',
      ellipsis: {
        tooltip: true
      },
      render: (row) => {
        return `${$isEmpty(row.compensationRatio) ? '--' : `${row.compensationRatio}%`}`
      }
    },
    {
      title: '操作',
      key: '__operate',
      width: 120,
      resizable: false,
      render: (row) => {
        const editBtn = createTextButton({
          text: '编辑',
          onClick: () => {
            routerPush({
              name: 'manage_organization_add',
              query: { id: row.id as string }
            })
          }
        })
        const deleteBtn = createConfirmTextButton({
          text: '删除',
          confirmText: '确定删除该机构吗？',
          onClick: async () => {
            const { error } = await fetchManageApiOrgRemove({ id: row.id })
            if (!error) {
              window?.$message?.success('删除成功')
              tableRef.value?.refreshData(true)
            }
          }
        })
        return h('div', { class: 'flex items-center gap-12px' }, [editBtn, deleteBtn])
      }
    }
  ]
})

// 查询
const handleSearch = () => {
  tableRef.value?.refreshData(true)
}

// 重置
const handleReset = () => {
  tableConfig.value.apiParams = {
    orgType: 'GUARANTEE',
    orgName: null,
    region: null
  }
  tableRef.value?.refreshData(true)
}

// 新增
const handleAdd = () => {
  routerPush({
    name: 'manage_organization_add'
  })
}

// 导入
const ycImportRef = ref<Expose.YCModal>()
const handleImport = () => {
  ycImportRef.value?.open({
    templateApi: fetchManageApiSciEntDownloadTemplate,
    templateParams: {
      template: 'PARTNER_ORG_GUARANTEE'
    },
    uploadApi: fetchManageApiOrgImport,
    uploadParams: {
      type: 'GUARANTEE'
    }
  })
}
</script>
