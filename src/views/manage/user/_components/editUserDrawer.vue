<script setup lang="ts">
import type { Ref } from 'vue'
import { computed, ref } from 'vue'
import { useLoading } from '@yc/hooks'
import { cloneDeep } from 'lodash-es'
import { useFormRules, useNaiveForm } from '@/hooks/useForm'
import { SexTypeModeOptions, UserTypeModeOptions } from '@/constants/app'
import { fetchOrgQueryList, fetchQueryRoleList, fetchUserAdd, fetchUserDetailById, fetchUserUpdate } from '@/service/api'
import { formatParams } from '@/utils/common'

interface IOpenParams {
  type: Common.EditOrAdd | 'view'
  id?: number
}

const emit = defineEmits(['refresh'])

const { loading, startLoading, endLoading } = useLoading()
const { loading: spinLoading, startLoading: startSpinLoading, endLoading: endSpinLoading } = useLoading()

const ycDrawerRef: Ref<Expose.YCDrawer | null> = ref(null)
const drawerType = ref<IOpenParams['type']>('add')

const isAdd = computed(() => drawerType.value === 'add')
const isView = computed(() => drawerType.value === 'view')

const { formData, formRef, resetFormData, validate, setFormData } = useNaiveForm({
  name: null,
  loginNumber: null,
  userType: null,
  sex: null,
  phone: null,
  divide: null,
  workUnit: null,
  userRoleList: null,
  orgId: null,
  scopeList: [],
  fileList: [],
  userStatus: 'USE',
  loginStatus: 'ONLINE'
})
const { createRequiredRule, formRules } = useFormRules()

const rules = {
  name: [{ ...createRequiredRule('请输入姓名'), trigger: 'blur' }],
  loginNumber: [{ ...createRequiredRule('请输入用户名'), trigger: 'blur' }],
  userType: [{ ...createRequiredRule('请选择用户类型'), trigger: 'change' }],
  sex: [{ ...createRequiredRule('请选择性别'), trigger: 'change' }],
  phone: formRules.phone,
  orgId: [
    {
      ...createRequiredRule('请选择所在支部'),
      trigger: 'change',
      type: 'number'
    }
  ],
  userRoleList: [{ ...createRequiredRule('请选择角色'), trigger: 'change', type: 'number' }]
}

// 获取下拉框数据
const orgTreeList = ref([])
const roleList = ref([])
const getSelectOptions = async () => {
  const fetchOrgTreeList = fetchOrgQueryList({})
  const fetchRoleList = fetchQueryRoleList()
  const [orgTreeListData, roleListData] = await Promise.all([fetchOrgTreeList, fetchRoleList])
  orgTreeList.value = orgTreeListData.data || []
  roleList.value = roleListData.data || []
}

// 打开抽屉
const open = async (params: IOpenParams) => {
  const { type, id = 0, orgId } = params
  drawerType.value = type
  resetFormData()
  ycDrawerRef.value?.open()
  try {
    startSpinLoading()
    await getSelectOptions()
    if (type !== 'add') {
      const { data = {} } = await fetchUserDetailById(id)
      data.userRoleList = data.userRoleList[0]
      if (data.attachment) {
        let arr = [data.attachment]
        formData.value.fileList = arr.map((file: any) => ({
          id: file.id,
          name: decodeURIComponent(file.fileName),
          size: file.fileSize,
          type: file.fileType,
          url: file.fileUrl,
          status: 'finished' // 设置状态为 'done' 表示文件已上传
        }))
      }
      Object.assign(formData.value, {
        ...data
      })
    } else {
      setFormData({
        orgId: orgId || null
      })
    }
  } finally {
    endSpinLoading()
  }
}

// 关闭抽屉
const close = () => {
  ycDrawerRef.value?.close()
}
const ycUploadRef: any = ref(null)
const Upload = () => {
  ycUploadRef.value?.openFileDialog()
}
// 确认提交
const handleSubmit = async () => {
  await validate()
  try {
    startLoading()
    let res = null
    formData.value.userRoleList = [formData.value.userRoleList]
    formData.value.fileList.forEach((item: any) => {
      item.fileUrl = item.url
      item.fileSize = item.file ? item.file.size : item.size
      delete item.id
    })
    formData.value.attachment = formData.value.fileList[0]
    const sendParams = cloneDeep(formData.value)
    if (drawerType.value === 'add') {
      res = await fetchUserAdd(formatParams(sendParams))
    } else {
      res = await fetchUserUpdate(formatParams(sendParams))
    }
    if (res.error) return
    window?.$message?.success(`${isAdd.value ? '新增用户成功' : '编辑用户成功'} `)
    emit('refresh')
    close()
  } finally {
    endLoading()
  }
}

defineExpose({
  open
})
</script>

<template>
  <YcDrawer
    ref="ycDrawerRef"
    :title="`${isAdd ? '新增' : isView ? '查看' : '编辑'}用户`"
  >
    <NSpin :show="spinLoading">
      <NForm
        ref="formRef"
        :model="formData"
        label-align="left"
        label-placement="left"
        :rules="rules"
        require-mark-placement="left"
        label-width="100"
        :disabled="isView"
      >
        <NFormItem
          label="姓名"
          path="name"
        >
          <NInput
            v-model:value="formData.name"
            placeholder="请输入姓名"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="用户名"
          path="loginNumber"
        >
          <NInput
            v-model:value="formData.loginNumber"
            :disabled="!isAdd"
            placeholder="请输入用户名"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="用户类型"
          path="userType"
        >
          <NSelect
            v-model:value="formData.userType"
            :options="UserTypeModeOptions"
            placeholder="请选择用户类型"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="性别"
          path="sex"
        >
          <NSelect
            v-model:value="formData.sex"
            :options="SexTypeModeOptions"
            placeholder="请选择性别"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="联系电话"
          path="phone"
        >
          <div class="w-full flex flex-col">
            <NInput
              v-model:value="formData.phone"
              class="w-full"
              placeholder="请输入联系电话"
              clearable
              :minlength="11"
              :maxlength="11"
              show-count
            />
          </div>
        </NFormItem>
        <NFormItem
          label="所在部门"
          path="orgId"
        >
          <NTreeSelect
            v-model:value="formData.orgId"
            :options="orgTreeList"
            label-field="name"
            key-field="id"
            children-field="children"
            placeholder="请选择所在部门"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="角色"
          path="userRoleList"
        >
          <NSelect
            v-model:value="formData.userRoleList"
            :options="roleList"
            placeholder="请选择角色"
            clearable
            label-field="name"
            value-field="id"
          />
        </NFormItem>
        <NFormItem label="职责分工">
          <NInput
            v-model:value="formData.divide"
            placeholder="请输入职责分工"
            clearable
          />
        </NFormItem>
        <NFormItem label="工作单位">
          <NInput
            v-model:value="formData.workUnit"
            placeholder="请输入工作单位"
            clearable
          />
        </NFormItem>
        <NFormItem label="签名">
          <div class="w-full">
            <NButton @click="Upload">上传文件</NButton>
            <yc-upload
              :listType="`image`"
              :max="1"
              ref="ycUploadRef"
              :show-trigger="false"
              v-model:file-list="formData.fileList"
            />
            <!-- <NUpload :max="5" multiple>
              <NButton>上传文件</NButton>
            </NUpload> -->
          </div>
        </NFormItem>
      </NForm>
    </NSpin>
    <template #footer>
      <NButton
        secondary
        strong
        @click="close"
        >返回</NButton
      >
      <NButton
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSubmit"
        >确定</NButton
      >
    </template>
  </YcDrawer>
</template>

<style scoped></style>
