<script setup lang="ts">
import type { Ref } from 'vue'
import { computed, ref } from 'vue'
import { useLoading } from '@yc/hooks'
import { useFormRules, useNaiveForm } from '@/hooks/useForm'
import { fetchOrgAdd, fetchOrgDetailById, fetchOrgQueryList, fetchOrgUpdate } from '@/service/api'

interface IEditDeptDrawerProps {
  type: Common.EditOrAdd | 'view'
  id?: number
  path?: string
}

interface Emits {
  (e: 'refresh'): void
}

const emit = defineEmits<Emits>()

const { loading, startLoading, endLoading } = useLoading()
const { loading: spinLoading, startLoading: startSpinLoading, endLoading: endSpinLoading } = useLoading()

const ycDrawerRef: Ref<Expose.YCDrawer | null> = ref(null)
const drawerType = ref<IEditDeptDrawerProps['type']>('add')

const drawerTitle = computed(() => (drawerType.value === 'add' ? '新增部门' : drawerType.value === 'edit' ? '编辑部门' : '查看部门'))
const isView = computed(() => drawerType.value === 'view')
const isEdit = computed(() => drawerType.value === 'edit')
const isAdd = computed(() => drawerType.value === 'add')

const { formData, formRef, resetFormData, validate } = useNaiveForm({
  parentId: 0,
  name: null,
  code: null,
  orderNum: null,
  labelList: [],
  intro: null
})
const { createRequiredRule } = useFormRules()

const rules = {
  name: [{ ...createRequiredRule('请输入部门名称'), trigger: 'blur' }],
  code: [{ ...createRequiredRule('请输入部门标识'), trigger: 'blur' }]
}

// 获取部门列表
const orgTreeList = ref<any[]>([])
const getOrgTreeList = async () => {
  const { data = [] } = await fetchOrgQueryList({})
  orgTreeList.value = data
}

// 打开抽屉
const parentDeptId = ref(0)
const open = async (params: IEditDeptDrawerProps) => {
  const { type, id = 0, path = null } = params
  parentDeptId.value = id
  drawerType.value = type
  ycDrawerRef.value?.open()
  try {
    startSpinLoading()
    if (type === 'add' && !id) {
      resetFormData()
    } else if (type === 'add') {
      resetFormData()
      formData.value.parentId = id
      formData.value.path = path
    } else {
      const { data = {} } = await fetchOrgDetailById(id)
      Object.assign(formData.value, data)
    }
    getOrgTreeList()
    getOrgLabelList()
  } finally {
    endSpinLoading()
  }
}

// 关闭抽屉
const close = () => {
  ycDrawerRef.value?.close()
}

// 确认提交
const handleSubmit = async () => {
  await validate()
  try {
    startLoading()
    let res = null
    if (drawerType.value === 'add') {
      res = await fetchOrgAdd(formData.value)
    } else {
      res = await fetchOrgUpdate(formData.value)
    }
    if (res.error) return
    window?.$message?.success(`${drawerType.value === 'add' ? '新增成功' : '编辑成功'} `)
    emit('refresh')
    close()
  } finally {
    endLoading()
  }
}

defineExpose({
  open
})
</script>

<template>
  <YcDrawer
    ref="ycDrawerRef"
    :title="drawerTitle"
  >
    <NSpin :show="spinLoading">
      <NForm
        ref="formRef"
        :model="formData"
        label-align="left"
        label-placement="left"
        :rules="rules"
        require-mark-placement="left"
        label-width="100"
      >
        <NFormItem
          label="部门名称"
          path="name"
        >
          <NInput
            v-model:value="formData.name"
            placeholder="请输入部门名称"
            :disabled="isView"
          />
        </NFormItem>
        <NFormItem
          label="部门标识"
          path="code"
        >
          <NInput
            v-model:value="formData.code"
            placeholder="请输入部门标识"
            :disabled="isView || isEdit"
          />
        </NFormItem>
        <NFormItem label="父级部门">
          <NTreeSelect
            v-model:value="formData.parentId"
            :options="orgTreeList"
            label-field="name"
            key-field="id"
            children-field="children"
            :disabled="isEdit || isView || (isAdd && !!parentDeptId)"
            placeholder="请选择父级部门"
          />
        </NFormItem>
        <NFormItem label="部门排序">
          <NInputNumber
            v-model:value="formData.orderNum"
            placeholder="请输入部门排序"
            :min="0"
            class="w-full"
            botton-placement="right"
            :disabled="isView"
          />
        </NFormItem>
        <NFormItem label="部门介绍">
          <NInput
            v-model:value="formData.intro"
            placeholder="请输入部门介绍"
            type="textarea"
            :disabled="isView"
          />
        </NFormItem>
      </NForm>
    </NSpin>
    <template #footer>
      <NButton
        secondary
        strong
        @click="close"
        >返回</NButton
      >
      <NButton
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSubmit"
        >确定</NButton
      >
    </template>
  </YcDrawer>
</template>

<style scoped></style>
