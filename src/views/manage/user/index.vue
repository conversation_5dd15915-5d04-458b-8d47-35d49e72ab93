<script setup lang="ts">
import type { Ref } from 'vue'
import { computed, h, reactive, ref } from 'vue'
import type { TreeOption, TreeProps } from 'naive-ui'
import { NButton, NDropdown, NFlex } from 'naive-ui'
import { useLoading } from '@yc/hooks'
import { useYcTable } from '@/hooks/useYcTable'
import type { TableUtil } from '@/typings/table'
import type { Expose } from '@/typings/expose'

import { useRouterPush } from '@/hooks/useRouterPush'
import { fetchOrgDeleteById, fetchOrgQueryList, fetchQueryRoleList, fetchUserDelete, fetchUserQueryPage } from '@/service/api'
import { createConfirmTextButton, createTextButton } from '@/utils/common'
import { SexTypeModeRecord, UserTypeModeRecord } from '@/constants/app'
import { usePermission } from '@/hooks/usePermission'
import EditDeptDrawer from './_components/editDeptDrawer.vue'
import EditUserDrawer from './_components/editUserDrawer.vue'
import YcSearchContent from '@/components/common/yc-search-content.vue'

type TreeThemeOverrides = NonNullable<TreeProps['themeOverrides']>

const { tableRef, refreshTable } = useYcTable()
const { routerPushByKey } = useRouterPush()
const { hasPermission } = usePermission()

const { loading, startLoading, endLoading } = useLoading()
const editUserDrawerRef = ref<Expose.YCDrawer | null>(null)
const resetPwdRef = ref<Expose.YCModal | null>(null)

const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchUserQueryPage,
  immediate: false,
  apiParams: {
    name: null,
    phone: null
  },
  columns: () => [
    {
      title: '姓名',
      key: 'name',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '用户名',
      key: 'loginNumber',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '用户类型',
      key: 'userType',
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => UserTypeModeRecord[rowData.userType as UnionKey.UserType]
          }
        )
      }
    },
    {
      title: '角色',
      key: 'roleName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '职责分工',
      key: 'divide',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '性别',
      key: 'sex',
      ellipsis: {
        tooltip: true
      },
      width: 80,
      render: (rowData) => {
        return h('span', {}, { default: () => SexTypeModeRecord[rowData.sex as UnionKey.SexType] })
      }
    },
    {
      title: '联系电话',
      key: 'phone',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '所在部门',
      key: 'orgName',
      ellipsis: {
        tooltip: true
      }
    },
    // {
    //   title: "账号状态",
    //   key: "userStatus",
    //   render: (rowData) => {
    //     return h(NSwitch, {
    //       value: (rowData.userStatus as UnionKey.UseStatusType) === "USE",
    //       rubberBand: false,
    //       disabled: !hasPermission("10111"),
    //       async onUpdateValue(value) {
    //         const res = await fetchUserUpdateStatus(rowData.id as number);
    //         if (res.error) return;
    //         window?.$message?.success(`${value ? "启用" : "禁用"}账号成功`);
    //         rowData.userStatus = value ? "USE" : "DISABLE";
    //       },
    //     });
    //   },
    // },
    {
      title: '操作',
      key: 'operator',
      width: 80,
      render: (rowData) => {
        const viewBtn = createTextButton({
          text: '查看',
          permissionCode: '10037',
          onClick: () => {
            editUserDrawerRef.value?.open({
              type: 'view',
              id: rowData.id as number
            })
          }
        })
        const editBtn = createTextButton({
          text: '编辑',
          permissionCode: '10038',
          onClick: () => {
            editUserDrawerRef.value?.open({
              type: 'edit',
              id: rowData.id as number
            })
          }
        })
        const resetPwdBtn = createTextButton({
          text: '重置密码',
          permissionCode: 'N10039',
          onClick: () => {
            resetPwdRef.value?.open(rowData)
          }
        })
        const deleteUserBtn = createConfirmTextButton({
          text: '删除',
          confirmText: '确定删除该用户？',
          permissionCode: 'N10040',
          onClick: async () => {
            const res = await fetchUserDelete(rowData.id as number)
            if (res.error) return
            window?.$message?.success('删除用户成功')
            refreshTable(true)
          }
        })
        return h(
          NFlex,
          {},
          {
            default: () => [viewBtn, editBtn, resetPwdBtn, deleteUserBtn]
          }
        )
      }
    }
  ]
})
const roleListTypeMaps = reactive([])
const getSelectOptions = async () => {
  const fetchRoleList = fetchQueryRoleList()
  const [roleListData]: any = await Promise.all([fetchRoleList])
  const object = roleListData.reduce((acc: any, item: any) => {
    acc[item.id] = item.name
    return acc
  }, {})
  Object.assign(roleListTypeMaps, { ...object })
}
getSelectOptions()
// 机构树点击
const currentOrgItem: Recordable = ref(null)
const currentOrgId: any = ref([])
const currentOrgName = computed(() => currentOrgItem.value?.name ?? '')
const orgTreeNodeProps = ({ option }: { option: TreeOption }) => {
  return {
    onClick() {
      if (option.id !== currentOrgId.value[0]) {
        currentOrgItem.value = option
        currentOrgId.value = [option.id]
        tableConfig.value.apiParams.orgId = option.id
        refreshTable()
      }
    }
  }
}

// 获取机构列表
const searchOrgName = ref('')
const orgTreeList = ref<any[]>([])
const getOrgTreeList = async () => {
  try {
    startLoading()
    const { data = [], error } = await fetchOrgQueryList({
      name: searchOrgName.value
    })
    if (error) return
    orgTreeList.value = data || []
  } finally {
    endLoading()
  }
}

// 新增机构
const editDeptDrawerRef = ref<Expose.YCDrawer | null>(null)
const handleAddDept = () => {
  editDeptDrawerRef.value?.open({
    type: 'add'
  })
}

// 新增用户
const handleAddUser = () => {
  editUserDrawerRef.value?.open({
    type: 'add',
    orgId: currentOrgItem.value?.id ?? null
  })
}

// 初始化数据
const initData = async () => {
  await getOrgTreeList()
  currentOrgItem.value = orgTreeList.value?.[0] ?? null
  currentOrgId.value = [orgTreeList.value?.[0]?.id ?? null]
  tableConfig.value.apiParams.orgId = currentOrgId.value?.[0] ?? null
  refreshTable()
}

// 机构树后缀
const orgTreeSuffix = ({ option }) => {
  const dropDownOptions = [
    { label: '添加', key: 'add', show: hasPermission('11031') },
    { label: '编辑', key: 'edit', show: hasPermission('11032') },
    { label: '查看', key: 'view', show: hasPermission('11033') },
    { label: '删除', key: 'delete', show: hasPermission('11032') }
  ]
  return h(
    NDropdown,
    {
      size: 'small',
      trigger: 'click',
      options: dropDownOptions,
      onSelect: async (type) => {
        if (type === 'delete') {
          const callBack = async () => {
            const res = await fetchOrgDeleteById(option.id)
            if (!res.error) {
              window?.$message?.success('删除成功')
              await initData()
            }
          }
          const d = window.$dialog?.warning({
            title: '提示',
            content: `是否确定删除${option.name}该机构？`,
            positiveText: '确定',
            negativeText: '返回',
            onPositiveClick() {
              d.loading = true
              return callBack()
            }
          })
        } else {
          editDeptDrawerRef.value?.open({
            type,
            id: option.id,
            path: option.path
          })
        }
      }
    },
    {
      default: () => {
        return h(
          'div',
          {
            class: 'icon-mdi:dots-horizontal'
          },
          {}
        )
      }
    }
  )
}

const treeThemeOverrides: TreeThemeOverrides = {
  nodeHeight: '40px',
  nodeColorActive: '#EFF5FD',
  nodeTextColor: '#3A3F50',
  nodeBorderRadius: '4px'
}

// 重置
const handleResetTable = () => {
  Object.assign(tableConfig.value.apiParams, {
    name: null,
    phone: null,
    isJoinScore: null
  })
  refreshTable()
}

initData()
</script>

<template>
  <YcContent class="!flex-row">
    <n-split
      direction="horizontal"
      style="height: 100%"
      default-size="280px"
      max="600px"
      min="280px"
      pane1-class="flex flex-col gap-16px"
    >
      <template #1>
        <div class="w-full border-r border-border_default h-full overflow-y-auto">
          <BorderTitleBar
            title="组织架构"
            class="pr-16px"
          >
            <template #right>
              <NTooltip trigger="hover">
                <template #trigger>
                  <IconMdiPlusThick
                    v-permission="`10031`"
                    class="cursor-pointer text-14px"
                    @click="handleAddDept"
                  />
                </template>
                新增部门
              </NTooltip>
            </template>
          </BorderTitleBar>
          <div class="pr-16px">
            <NInput
              v-model:value="searchOrgName"
              type="text"
              placeholder="请输入部门名称"
              clearable
              @keyup.enter="getOrgTreeList"
            >
              <template #suffix>
                <IconMdiMagnify
                  class="cursor-pointer text-18px text-font_secondary"
                  @click="getOrgTreeList"
                />
              </template>
            </NInput>
          </div>
          <NSpin
            :show="loading"
            class="flex-1 overflow-y-auto"
          >
            <NTree
              v-if="orgTreeList.length"
              class="pr-16px"
              :pattern="searchOrgName"
              :data="orgTreeList"
              key-field="id"
              label-field="name"
              children-field="children"
              :selected-keys="currentOrgId"
              block-line
              selectable
              :render-suffix="orgTreeSuffix"
              :node-props="orgTreeNodeProps"
              :theme-overrides="treeThemeOverrides"
            />
            <YcEmpty
              v-else
              class="py-200px"
              title="暂无数据"
            />
          </NSpin>
        </div>
      </template>
      <template #2>
        <div class="w-full flex flex-1 flex-col gap-16px pl-16px h-full">
          <BorderTitleBar :title="currentOrgName"> </BorderTitleBar>
          <YcTable
            ref="tableRef"
            :table-config="tableConfig"
          >
            <template #header>
              <YcSearchContent
                @search="refreshTable(true)"
                @reset="handleResetTable"
              >
                <YcSearchItem label="姓名">
                  <NInput
                    v-model:value="tableConfig.apiParams.name"
                    placeholder="姓名"
                    clearable
                  />
                </YcSearchItem>
                <YcSearchItem label="联系电话">
                  <NInput
                    v-model:value="tableConfig.apiParams.phone"
                    placeholder="联系电话"
                    clearable
                  ></NInput>
                </YcSearchItem>
              </YcSearchContent>
            </template>
            <template #header-sub>
              <NButton
                v-permission="`10036`"
                type="primary"
                @click="handleAddUser"
                >新增用户</NButton
              >
            </template>
          </YcTable>
        </div>
      </template>
    </n-split>

    <!--  新增机构弹窗  -->
    <EditDeptDrawer
      ref="editDeptDrawerRef"
      @refresh="getOrgTreeList"
    />

    <!-- 新增/编辑用户弹窗   -->
    <EditUserDrawer
      ref="editUserDrawerRef"
      @refresh="refreshTable(true)"
    />
  </YcContent>
</template>

<style scoped lang="scss">
:deep(.n-tree-node--selected .n-tree-node-content__suffix) {
  color: #204d87;
}

:deep(.n-tree-node--selected .n-tree-node-content__text) {
  color: #204d87;
}

:deep(.n-tree-node--selected .n-tree-node-switcher__icon) {
  color: #204d87;
}
</style>
