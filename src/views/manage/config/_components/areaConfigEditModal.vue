<script setup lang="ts">
import { Expose } from '@/typings/expose'
import { useFormRules, useNaiveForm } from '@/hooks/useForm'
import { useLoading } from '~/packages/hooks'
import { fetchManageApiConfigAdd, fetchManageApiConfigEdit } from '@/service/api'

const modalRef = ref<Expose.YCModal | null>(null)

const _emits = defineEmits(['success'])

const { createRequiredRule } = useFormRules()
const { formData, formRef, validate, setFormData, resetFormData } = useNaiveForm({
  configNo: null,
  creditRiseRate: null,
  guaranteeRiseRate: null,
  totalAmount: null
})

const rules = {
  configNo: [{ ...createRequiredRule('请选择区域'), trigger: 'change' }],
  creditRiseRate: [{ ...createRequiredRule('请输入信用贷款上浮基点'), trigger: 'blur', type: 'number' }],
  guaranteeRiseRate: [{ ...createRequiredRule('请输入担保贷款上浮基点'), trigger: 'blur', type: 'number' }],
  totalAmount: [{ ...createRequiredRule('请输入备案额度上限'), trigger: 'blur', type: 'number' }]
}

// 打开
const editType = ref<'add' | 'edit'>('add')
const open = (params: { type: 'add' | 'edit'; editData?: any }) => {
  editType.value = params.type
  resetFormData()
  modalRef.value?.open()
  if (params.type === 'edit') {
    setFormData(params.editData)
  }
}

// 关闭
const close = () => {
  modalRef.value?.close()
}

// 提交
const { loading: submitLoading, startLoading: startSubmitLoading, endLoading: endSubmitLoading } = useLoading()
const handleSubmit = async () => {
  await validate()
  try {
    startSubmitLoading()
    const fetchApi = editType.value === 'add' ? fetchManageApiConfigAdd : fetchManageApiConfigEdit
    const res = await fetchApi(formData.value)
    if (!res.error) {
      window.$message?.success(`${editType.value === 'add' ? '新增' : '编辑'}成功`)
      _emits('success', formData.value)
      close()
    }
  } finally {
    endSubmitLoading()
  }
}

defineExpose({
  open,
  close
} as Expose.YCModal)
</script>

<template>
  <YcModal
    :title="`${editType === 'add' ? '新增' : '编辑'}区域参数配置`"
    ref="modalRef"
  >
    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
    >
      <div class="flex items-center gap-12px w-full">
        <NFormItem
          label="区域"
          path="configNo"
          class="w-full"
        >
          <RegionSelect
            v-model="formData.configNo"
            :disabled="editType === 'edit'"
          />
        </NFormItem>
        <NFormItem
          label="信用贷款上浮基点"
          path="creditRiseRate"
          class="w-full"
        >
          <NInputNumber
            v-model:value="formData.creditRiseRate"
            placeholder="请输入"
            :min="0"
            :max="140"
            :show-button="false"
            class="w-full"
            :precision="0"
          />
        </NFormItem>
      </div>
      <div class="flex items-center gap-12px w-full">
        <NFormItem
          label="担保贷款上浮基点"
          path="guaranteeRiseRate"
          class="w-full"
        >
          <NInputNumber
            v-model:value="formData.guaranteeRiseRate"
            placeholder="请输入"
            :min="0"
            :max="90"
            :show-button="false"
            class="w-full"
            :precision="0"
          />
        </NFormItem>
        <NFormItem
          label="备案额度上限"
          path="totalAmount"
          class="w-full"
        >
          <NInputGroup>
            <NInputNumber
              v-model:value="formData.totalAmount"
              placeholder="请输入"
              :min="0"
              :show-button="false"
              class="w-full"
              :precision="2"
            />
            <NInputGroupLabel>万元</NInputGroupLabel>
          </NInputGroup>
        </NFormItem>
      </div>
    </NForm>
    <template #footer>
      <div class="w-full flex items-center justify-between">
        <div class="text-14px font-500 text-red">*信用贷款加点幅度不能超过140个基点，担保贷款不能超过90个基点</div>
        <NButton
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          >确定</NButton
        >
      </div>
    </template>
  </YcModal>
</template>

<style scoped></style>
