<script setup lang="ts">
import type { Ref } from 'vue'
import { ref } from 'vue'
import type { TableUtil } from '@/typings/table'
import { useYcTable } from '@/hooks/useYcTable'
import { fetchManageApiTrackTrackRecord } from '@/service/api'

const { tableRef, refreshTable } = useYcTable()

const tableConfig: Ref<TableUtil.TableConfig> = ref({
  immediate: false,
  apiFn: fetchManageApiTrackTrackRecord,
  apiParams: {
    id: null
  },
  columns: () => [
    {
      title: '变更时间',
      key: 'createdTime',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '变更描述',
      key: 'trackDesc',
      render: (row) => {
        return h('span', {
          innerHTML: row.trackDesc ? row.trackDesc.replace(/\n/g, '<br/>') : '--'
        })
      }
    }
  ]
})

const ycModalRef = ref()
const open = async (id: number) => {
  ycModalRef.value?.open()
  tableConfig.value.apiParams.dataId = id
  await nextTick()
  refreshTable(true)
}

defineExpose({
  open
})
</script>

<template>
  <YcModal
    title="历史记录"
    width="800px"
    ref="ycModalRef"
  >
    <div class="w-full h-500px">
      <YcTable
        ref="tableRef"
        :table-config="tableConfig"
        :show-sub-header="false"
        :show-sub-header-border="false"
      />
    </div>
  </YcModal>
</template>

<style scoped></style>
