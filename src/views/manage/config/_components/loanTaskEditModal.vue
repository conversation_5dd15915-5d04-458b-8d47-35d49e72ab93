<script setup lang="ts">
import { Expose } from '@/typings/expose'
import { useFormRules, useNaiveForm } from '@/hooks/useForm'
import { useLoading } from '~/packages/hooks'
import { fetchManageApiLoanTaskManageAdd, fetchManageApiLoanTaskManageUpdate } from '@/service/api'

const modalRef = ref<Expose.YCModal | null>(null)

const _emits = defineEmits(['success'])

const { createRequiredRule } = useFormRules()
const { formData, formRef, validate, setFormData, resetFormData } = useNaiveForm({
  year: null,
  region: null,
  targetAmount: null
})

const rules = {
  // year: [{ ...createRequiredRule('请选择年份'), trigger: ['change', 'blur'] }],
  region: [{ ...createRequiredRule('请选择区域'), trigger: 'change' }],
  targetAmount: [{ ...createRequiredRule('请输入目标金额'), trigger: 'blur', type: 'number' }]
}

// 打开
const editType = ref<'add' | 'edit'>('add')
const open = (params: { type: 'add' | 'edit'; editData?: any }) => {
  editType.value = params.type
  resetFormData()
  formData.value.year = new Date().getFullYear().toString()
  modalRef.value?.open()
  if (params.type === 'edit') {
    setFormData(params.editData)
  }
}

// 关闭
const close = () => {
  modalRef.value?.close()
}

// 提交
const { loading: submitLoading, startLoading: startSubmitLoading, endLoading: endSubmitLoading } = useLoading()
const handleSubmit = async () => {
  if (!formData.value.year) {
    return window?.$message?.warning('请选择年份')
  }
  await validate()
  try {
    startSubmitLoading()
    const fetchApi = editType.value === 'add' ? fetchManageApiLoanTaskManageAdd : fetchManageApiLoanTaskManageUpdate
    const res = await fetchApi(formData.value)
    if (!res.error) {
      window.$message?.success(`${editType.value === 'add' ? '新增' : '编辑'}成功`)
      _emits('success', formData.value)
      close()
    }
  } finally {
    endSubmitLoading()
  }
}

defineExpose({
  open,
  close
} as Expose.YCModal)
</script>

<template>
  <YcModal
    :title="`${editType === 'add' ? '新增' : '编辑'}贷款任务管理`"
    ref="modalRef"
  >
    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
    >
      <div class="flex items-center gap-12px w-full">
        <NFormItem
          label="年份"
          path="year"
          class="w-full"
        >
          <n-date-picker
            type="year"
            v-model:formatted-value="formData.year"
            value-format="yyyy"
            placeholder="请输入"
            :disabled="editType === 'edit'"
          />
        </NFormItem>
        <NFormItem
          label="区域"
          path="region"
          class="w-full"
        >
          <RegionSelect
            v-model="formData.region"
            :disabled="editType === 'edit'"
          />
        </NFormItem>
      </div>
      <div class="flex items-center gap-12px w-full">
        <NFormItem
          label="目标贷款金额"
          path="targetAmount"
          class="w-50%"
        >
          <NInputGroup>
            <NInputNumber
              v-model:value="formData.targetAmount"
              placeholder="请输入"
              :min="0"
              :show-button="false"
              class="w-full"
              :precision="2"
            />
            <NInputGroupLabel>亿元</NInputGroupLabel>
          </NInputGroup>
        </NFormItem>
      </div>
    </NForm>
    <template #footer>
      <NButton
        type="primary"
        @click="handleSubmit"
        :loading="submitLoading"
      >确定</NButton
      >
    </template>
  </YcModal>
</template>

<style scoped></style>
