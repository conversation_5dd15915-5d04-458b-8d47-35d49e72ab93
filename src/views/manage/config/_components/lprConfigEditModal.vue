<script setup lang="ts">
import { Expose } from '@/typings/expose'
import { useFormRules, useNaiveForm } from '@/hooks/useForm'
import { useLoading } from '~/packages/hooks'
import { fetchManageApiLoanRateAdd, fetchManageApiLoanRateEdit } from '@/service/api'

const modalRef = ref<Expose.YCModal | null>(null)

const _emits = defineEmits(['success'])

const { createRequiredRule } = useFormRules()
const { formData, formRef, validate, setFormData, resetFormData } = useNaiveForm({
  oneYearRate: null,
  threeYearRate: null
})

const rules = {
  oneYearRate: [{ ...createRequiredRule('请输入一年期LPR'), trigger: 'blur', type: 'number' }],
  threeYearRate: [{ ...createRequiredRule('请输入三年期LPR'), trigger: 'blur', type: 'number' }]
}

// 打开
const editType = ref<'add' | 'edit'>('add')
const open = (params: { type: 'add' | 'edit'; editData?: any }) => {
  editType.value = params.type
  resetFormData()
  modalRef.value?.open()
  if (params.type === 'edit') {
    setFormData(params.editData)
  }
}

// 关闭
const close = () => {
  modalRef.value?.close()
}

// 提交
const { loading: submitLoading, startLoading: startSubmitLoading, endLoading: endSubmitLoading } = useLoading()
const handleSubmit = async () => {
  await validate()
  try {
    startSubmitLoading()
    const fetchApi = editType.value === 'add' ? fetchManageApiLoanRateAdd : fetchManageApiLoanRateEdit
    const res = await fetchApi(formData.value)
    if (!res.error) {
      window.$message?.success(`${editType.value === 'add' ? '新增' : '编辑'}成功`)
      _emits('success', formData.value)
      close()
    }
  } finally {
    endSubmitLoading()
  }
}

defineExpose({
  open,
  close
} as Expose.YCModal)
</script>

<template>
  <YcModal
    :title="`${editType === 'add' ? '新增' : '编辑'}贷款市场报价利率`"
    ref="modalRef"
  >
    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
    >
      <div class="flex items-center gap-12px w-full">
        <NFormItem
          label="一年期LPR"
          path="oneYearRate"
          class="w-full"
        >
          <NInputGroup>
            <NInputNumber
              v-model:value="formData.oneYearRate"
              placeholder="请输入"
              :min="0"
              :max="140"
              :show-button="false"
              class="w-full"
              :precision="2"
            />
            <NInputGroupLabel>%</NInputGroupLabel>
          </NInputGroup>
        </NFormItem>
        <NFormItem
          label="三年期LPR"
          path="threeYearRate"
          class="w-full"
        >
          <NInputGroup>
            <NInputNumber
              v-model:value="formData.threeYearRate"
              placeholder="请输入"
              :min="0"
              :max="140"
              :show-button="false"
              class="w-full"
              :precision="2"
            />
            <NInputGroupLabel>%</NInputGroupLabel>
          </NInputGroup>
        </NFormItem>
      </div>
    </NForm>
    <template #footer>
      <div class="w-full flex items-center justify-between">
        <div class="text-14px font-500 text-red">*贷款市场报价利率（LPR）加点幅度不能超过140个基点</div>
        <NButton
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          >确定</NButton
        >
      </div>
    </template>
  </YcModal>
</template>

<style scoped></style>
