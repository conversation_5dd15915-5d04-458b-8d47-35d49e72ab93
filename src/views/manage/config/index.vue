<template>
  <YcContent>
    <BorderTitleBar title="配置管理" />
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
      :show-sub-header-border="false"
      :key="activeTab"
      :scroll-x="activeTab === 'area' ? 2400 : 0"
      :show-sub-header="activeTab !== 'org'"
    >
      <template #header>
        <NTabs
          type="line"
          :value="activeTab"
          @update:value="handleTabChange"
        >
          <NTab name="lpr">贷款市场报价利率</NTab>
          <NTab name="area">区域参数配置</NTab>
          <NTab name="task">贷款任务管理</NTab>
          <NTab name="org">机构熔断管理</NTab>
        </NTabs>
        <YcSearchContent
          v-if="activeTab === 'org'"
          @search="handleSearch"
          @reset="handleReset"
        >
          <YcSearchItem label="机构名称">
            <NInput
              v-model:value="orgTableConfig.apiParams.name"
              placeholder="请输入机构名称"
              clearable
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template
        #header-sub
        v-if="activeTab !== 'org'"
      >
        <div class="flex items-center gap-8px">
          <NButton
            type="primary"
            @click="handleAdd"
            >新增</NButton
          >
        </div>
      </template>
    </YcTable>

    <!--  区域参数配置编辑弹窗  -->
    <areaConfigEditModal
      ref="areaConfigEditModalRef"
      @success="tableRef?.refreshData(true)"
    />

    <!-- lpr参数配置编辑弹窗   -->
    <lprConfigEditModal
      ref="lprConfigEditModalRef"
      @success="tableRef?.refreshData(true)"
    />

    <!--  贷款任务管理编辑弹窗  -->
    <loanTaskEditModel
      ref="loanTaskEditModelRef"
      @success="tableRef?.refreshData(true)"
    />

    <areaLogModal ref="areaLogModalRef" />
  </YcContent>
</template>

<script setup lang="ts">
import { Expose } from '@/typings/expose'
import { TableUtil } from '@/typings/table'
import areaConfigEditModal from './_components/areaConfigEditModal.vue'
import lprConfigEditModal from './_components/lprConfigEditModal.vue'
import loanTaskEditModel from './_components/loanTaskEditModal.vue'
import {
  fetchManageApiConfigQueryPage,
  fetchManageApiConfigQueryYearCount,
  fetchManageApiLoanRateDelete,
  fetchManageApiLoanRateQueryPage,
  fetchManageApiLoanTaskManageQueryPage,
  fetchManageApiLoanTaskManageDelete
} from '@/service/api'
import { createConfirmTextButton, createTextButton } from '@/utils/common'
import { NSwitch } from 'naive-ui'
import areaLogModal from './_components/areaLogModal.vue'
import YcSearchContent from '@/components/common/yc-search-content.vue'

const tableRef = ref<Expose.YCTable>()

const areaConfigEditModalRef = ref<Expose.YCModal | null>(null)
const lprConfigEditModalRef = ref<Expose.YCModal | null>(null)
const loanTaskEditModelRef = ref<Expose.YCModal | null>(null)
const areaLogModalRef = ref<Expose.YCModal | null>(null)

// 切换tab
const activeTab = ref('lpr')

// 贷款市场报价利率表格配置
const lprTableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchManageApiLoanRateQueryPage,
  apiParams: {},
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60
    },
    {
      title: '一年期LPR（%）',
      key: 'oneYearRate',
      resizable: true,
      ellipsis: {
        tooltip: true
      },
      render: (row) => {
        return `${$isEmpty(row.oneYearRate) ? '--' : `${row.oneYearRate}%`}`
      }
    },
    {
      title: '三年期LPR（%）',
      key: 'threeYearRate',
      resizable: true,
      ellipsis: {
        tooltip: true
      },
      render: (row) => {
        return `${$isEmpty(row.threeYearRate) ? '--' : `${row.threeYearRate}%`}`
      }
    },
    {
      title: '更新人',
      key: 'updatedBy',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '更新时间',
      key: 'updatedTime',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '操作',
      key: '__operate',
      resizable: false,
      width: 120,
      render: (row) => {
        const editBtn = createTextButton({
          text: '编辑',
          onClick: () => {
            lprConfigEditModalRef.value?.open({ type: 'edit', editData: row })
          }
        })
        const deleteBtn = createConfirmTextButton({
          text: '删除',
          confirmText: '确定删除该贷款市场报价利率吗？',
          onClick: async () => {
            const { error } = await fetchManageApiLoanRateDelete({ id: row.id })
            if (!error) {
              window?.$message?.success('删除成功')
              tableRef.value?.refreshData(true)
            }
          }
        })
        return h('div', { class: 'flex items-center gap-12px' }, [editBtn, deleteBtn])
      }
    }
  ]
})
// 区域参数配置表格配置
const areaTableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchManageApiConfigQueryPage,
  apiParams: {},
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 80
    },
    {
      title: '区域',
      key: 'configName',
      width: 140,
      ellipsis: {
        tooltip: true
      },
      fixed: 'left'
    },
    {
      title: '信用贷款上浮基点',
      key: 'creditRiseRate',
      width: 160,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '担保贷款上浮基点',
      key: 'guaranteeRiseRate',
      width: 160,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '信用贷款1年期LPR（%）',
      key: 'creditLprOneYear',
      width: 200,
      ellipsis: {
        tooltip: true
      },
      render: (row) => {
        return `${$isEmpty(row.creditLprOneYear) ? '--' : `${row.creditLprOneYear}%`}`
      }
    },
    {
      title: '信用贷款3年期LPR（%）',
      key: 'creditLprThreeYear',
      width: 200,
      ellipsis: {
        tooltip: true
      },
      render: (row) => {
        return `${$isEmpty(row.creditLprThreeYear) ? '--' : `${row.creditLprThreeYear}%`}`
      }
    },
    {
      title: '担保贷款1年期LPR（%） ',
      key: 'guaranteeLprOneYear',
      width: 200,
      ellipsis: {
        tooltip: true
      },
      render: (row) => {
        return `${$isEmpty(row.guaranteeLprOneYear) ? '--' : `${row.guaranteeLprOneYear}%`}`
      }
    },
    {
      title: '担保贷款3年期LPR（%）',
      key: 'guaranteeLprThreeYear',
      width: 200,
      ellipsis: {
        tooltip: true
      },
      render: (row) => {
        return `${$isEmpty(row.guaranteeLprThreeYear) ? '--' : `${row.guaranteeLprThreeYear}%`}`
      }
    },
    {
      title: '备案额度上限（万元）',
      key: 'totalAmount',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '已备案金额（万元）',
      key: 'recordTotalAmount',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '剩余备案金额（万元）',
      key: 'recordLeftAmount',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '是否熔断',
      key: 'fusing',
      width: 100,
      fixed: 'right',
      render: (rowData) => {
        return h(NSwitch, {
          value: rowData.fusing as boolean,
          rubberBand: false,
          async onUpdateValue(value) {
            try {
              const res = await fetchManageApiConfigFusing({ id: rowData.configNo, fusing: value })
              if (!res.error) {
                window?.$message?.success(`${value ? '启用' : '禁用'}熔断成功`)
                rowData.fusing = value
              }
            } finally {
            }
          }
        })
      }
    },
    {
      title: '更新人',
      key: 'updatedBy',
      width: 120,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '更新时间',
      key: 'updatedTime',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '操作',
      key: '__operate',
      resizable: false,
      width: 120,
      fixed: 'right',
      render: (row) => {
        const editBtn = createTextButton({
          text: '编辑',
          onClick: () => {
            areaConfigEditModalRef.value?.open({ type: 'edit', editData: row })
          }
        })
        const logBtn = createTextButton({
          text: '历史记录',
          onClick: () => {
            areaLogModalRef.value?.open(row.id)
          }
        })
        return h('div', { class: 'flex items-center gap-12px' }, [editBtn, logBtn])
      }
    }
  ]
})

// 贷款任务管理
const taskTableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchManageApiLoanTaskManageQueryPage,
  apiParams: {},
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60
    },
    {
      title: '年份',
      key: 'year',
      resizable: true,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '区域名称',
      key: 'regionName',
      resizable: true,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '目标贷款金额(亿元)',
      key: 'targetAmount',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '操作',
      key: '__operate',
      resizable: false,
      width: 120,
      render: (row) => {
        const editBtn = createTextButton({
          text: '编辑',
          onClick: () => {
            loanTaskEditModelRef.value?.open({ type: 'edit', editData: row })
          }
        })
        const deleteBtn = createConfirmTextButton({
          text: '删除',
          confirmText: '确定删除该贷款任务吗？',
          onClick: async () => {
            const { error } = await fetchManageApiLoanTaskManageDelete({ id: row.id })
            if (!error) {
              window?.$message?.success('删除成功')
              tableRef.value?.refreshData(true)
            }
          }
        })
        return h('div', { class: 'flex items-center gap-12px' }, [editBtn, deleteBtn])
      }
    }
  ]
})

// 机构熔断管理表格配置
const orgTableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchManageApiConfigQueryYearCount,
  apiParams: {
    name: null
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 80
    },
    {
      title: '年份',
      key: 'year',
      width: 120,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '机构名称',
      key: 'name',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '已贷款备案金额(万元)',
      key: 'recordAmount',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '已申请风补金额(万元)',
      key: 'compensateAmount',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '不良率(%)',
      key: 'badLoanRatio',
      ellipsis: {
        tooltip: true
      },
      render: (row) => {
        return `${$isEmpty(row.badLoanRatio) ? '--' : `${row.badLoanRatio * 100}%`}`
      }
    },
    {
      title: '是否熔断',
      key: 'fusing',
      width: 100,
      fixed: 'right',
      render: (rowData) => {
        return h(NSwitch, {
          value: rowData.fusing as boolean,
          rubberBand: false,
          async onUpdateValue(value) {
            try {
              const res = await fetchManageApiConfigFusing({ id: rowData.relateId, fusing: value })
              if (!res.error) {
                window?.$message?.success(`${value ? '启用' : '禁用'}熔断成功`)
                rowData.fusing = value
              }
            } finally {
            }
          }
        })
      }
    }
  ]
})
// 表格配置
const tableConfig = computed(() => {
  if (activeTab.value === 'area') {
    return areaTableConfig.value
  } else if (activeTab.value === 'org') {
    return orgTableConfig.value
  } else if (activeTab.value === 'task') {
    return taskTableConfig.value
  } else {
    return lprTableConfig.value
  }
})

const handleTabChange = async (value: string) => {
  activeTab.value = value
  orgTableConfig.value.apiParams.name = null
}

// 查询
const handleSearch = () => {
  tableRef.value?.refreshData(true)
}

// 重置
const handleReset = () => {
  console.log('重置')
  orgTableConfig.value.apiParams.name = null
  tableRef.value?.refreshData(true)
}

// 新增
const handleAdd = () => {
  if (activeTab.value === 'area') {
    areaConfigEditModalRef.value?.open({ type: 'add' })
  } else if (activeTab.value === 'task') {
    loanTaskEditModelRef.value?.open({ type: 'add' })
  } else {
    lprConfigEditModalRef.value?.open({ type: 'add' })
  }
}
</script>
