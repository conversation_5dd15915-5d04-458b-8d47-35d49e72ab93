<script setup lang="ts">
import { NButton, NFlex, NTag } from 'naive-ui'
import type { Ref } from 'vue'
import { ref, h } from 'vue'
import { useYcTable } from '@/hooks/useYcTable'
import type { TableUtil } from '@/typings/table'
import { fetchQueryOperateLogPage } from '@/service/api'
import { logTypeModeRecord, operTargetTypeModeRecord, operTypeModeRecord } from '@/constants/app'
const { tableRef, refreshTable } = useYcTable()

const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchQueryOperateLogPage,
  apiParams: {},
  columns: () => [
    {
      title: '日志类型',
      key: 'logType',
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h('span', {}, { default: () => logTypeModeRecord[rowData.logType as UnionKey.LogType] })
      }
    },
    {
      title: '功能模块',
      key: 'operTargetType',
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h('span', {}, { default: () => operTargetTypeModeRecord[rowData.operTargetType as UnionKey.OperTargetType] })
      }
    },
    {
      title: '操作类型',
      key: 'operType',
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h('span', {}, { default: () => operTypeModeRecord[rowData.operType as UnionKey.OperType] })
      }
    },
    {
      title: '操作描述',
      key: 'operSimpleDesc',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '访问IP',
      key: 'remoteIp',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '请求地址',
      key: 'requestUrl',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '操作时间',
      key: 'createdTime'
    }
  ]
})

// 重置表格
const handleResetTable = () => {
  tableConfig.value.apiParams.name = null
  refreshTable()
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="日志管理"> </BorderTitleBar>
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
    >
      <template #header>
        <div class="flex items-center gap-12px">
          <YcSearchItem label="操作描述">
            <NInput
              v-model:value="tableConfig.apiParams.name"
              placeholder="请输入关键字"
              clearable
            />
          </YcSearchItem>
          <NButton
            type="primary"
            @click="refreshTable"
            >查询</NButton
          >
          <NButton
            secondary
            strong
            @click="handleResetTable"
            >重置</NButton
          >
        </div>
      </template>
    </YcTable>
  </YcContent>
</template>

<style scoped></style>
