<script setup lang="ts">
import { NButton, NFlex, NTag } from 'naive-ui'
import type { Ref } from 'vue'
import { h, reactive, ref } from 'vue'
import dayjs from 'dayjs'
import { useYcTable } from '@/hooks/useYcTable'
import type { TableUtil } from '@/typings/table'
import { fetchDictDelete, fetchQueryDictPage } from '@/service/api'
import { createConfirmTextButton, createTextButton } from '@/utils/common'
import DictEditDrawer from './_components/dictEditDrawer.vue'

const dictEditDrawerRef: Ref<Expose.YCDrawer | null> = ref(null)

const { tableRef, refreshTable } = useYcTable()

const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchQueryDictPage,
  apiParams: {
    name: null
  },
  columns: () => [
    {
      title: '字典项名称',
      key: 'typeDescription'
    },
    {
      title: '字典值',
      render: (rowData) => {
        return rowData.dictList.map((t) => t.dictDescription).join(';')
      }
    },
    {
      title: '更新时间',
      key: 'releaseDate',
      render: (rowData) => {
        return dayjs(rowData.releaseDate).format('YYYY-MM-DD')
      }
    },
    {
      title: '操作',
      key: 'operator',
      width: 220,
      render: (rowData) => {
        const viewBtn = createTextButton({
          text: '查看',
          permissionCode: '10028',
          onClick: () => {
            dictEditDrawerRef.value?.open({
              type: 'view',
              name: rowData.typeDescription,
              dictType: rowData.dictType
            })
          }
        })
        const editBtn = createTextButton({
          text: '编辑',
          permissionCode: '10026',
          onClick: () => {
            dictEditDrawerRef.value?.open({
              type: 'edit',
              name: rowData.typeDescription,
              dictType: rowData.dictType
            })
          }
        })
        return h(
          NFlex,
          { align: 'center' },
          {
            default: () => [viewBtn, editBtn]
          }
        )
      }
    }
  ]
})

// 重置表格
const handleResetTable = () => {
  tableConfig.value.apiParams.name = null
  refreshTable()
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="字典管理"> </BorderTitleBar>
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
    >
      <template #header>
        <div class="flex items-center gap-12px">
          <YcSearchItem label="字典项名称">
            <NInput
              v-model:value="tableConfig.apiParams.name"
              placeholder="请输入字典项名称"
              clearable
            />
          </YcSearchItem>
          <NButton
            type="primary"
            @click="refreshTable"
            >查询</NButton
          >
          <NButton
            secondary
            strong
            @click="handleResetTable"
            >重置</NButton
          >
        </div>
      </template>
    </YcTable>

    <DictEditDrawer
      ref="dictEditDrawerRef"
      @refresh="refreshTable"
    />
  </YcContent>
</template>

<style scoped></style>
