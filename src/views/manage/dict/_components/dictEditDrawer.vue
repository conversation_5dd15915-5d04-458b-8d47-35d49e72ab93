<script setup lang="ts">
import type { Ref } from 'vue'
import { computed, ref } from 'vue'
import { useNaiveForm } from '@/hooks/useForm'
import { fetchDictDetailByType, fetchDictUpdate } from '@/service/api'
import { useLoading } from '~/packages/hooks'

interface IOpenParams {
  type: Common.EditOrAdd | 'view'
  name: string
  dictType: string
  id?: number
}

const emit = defineEmits(['refresh'])

const ycDrawerRef: Ref<Expose.YCDrawer | null> = ref(null)

const { formData, formRef, resetFormData, validate, setFormData } = useNaiveForm({
  dictType: null,
  typeDescription: null,
  list: []
})
const { loading, startLoading, endLoading } = useLoading()
const { loading: confirmLoading, startLoading: startConfirmLoading, endLoading: endConfirmLoading } = useLoading()

const drawerType = ref<Common.EditOrAdd | 'view'>('add')
const isAdd = computed(() => drawerType.value === 'add')
const isEdit = computed(() => drawerType.value === 'edit')
const isView = computed(() => drawerType.value === 'view')
const drawerTitle = computed(() => `${isAdd.value ? '新增' : isEdit.value ? '编辑' : '查看'}字典项`)
const dicName = ref('')
const dicType = ref('')

// 打开抽屉
const open = async (params: IOpenParams) => {
  try {
    startLoading()
    const { type, name, dictType } = params
    dicName.value = name
    dicType.value = dictType
    drawerType.value = type
    resetFormData()
    formData.value.list = []
    ycDrawerRef.value?.open()
    if (type !== 'add') {
      const { data = [], error } = await fetchDictDetailByType(dictType)
      setFormData({
        dictType: dictType,
        typeDescription: name,
        list: data.map((item) => ({
          dictValue: item.dictValue,
          priority: item.priority,
          dictDescription: item.dictDescription
        }))
      })
    }
  } finally {
    endLoading()
  }
}

// 关闭抽屉
const close = () => {
  ycDrawerRef.value?.close()
}

// 新增一行
const handleAdd = () => {
  const priority = formData.value.list.length + 1
  const dictValue = formData.value.list.length ? Math.max(...formData.value.list.map((item: any) => item.dictValue)) + 1 : 1 // 自动生成编号
  formData.value.list.push({
    priority,
    dictValue,
    dictDescription: null
  })
}

// 删除一行
const handleDelete = (index: number) => {
  formData.value.list.splice(index, 1)
}

// 提交表单
const handleConfirm = async () => {
  await validate()
  if (!formData.value.list.length) return window?.$message.error('请添加')
  const isFull = formData.value.list.every((item) => item.dictValue && item.dictDescription)
  if (!isFull) return window?.$message.error('请填写完整字典选项')
  try {
    startConfirmLoading()
    const { data, error } = await fetchDictUpdate(formData.value)
    if (error) return
    window?.$message?.success(`更新成功`)
    close()
    emit('refresh')
  } finally {
    endConfirmLoading()
  }
}

defineExpose({
  open
})
</script>

<template>
  <YcDrawer
    ref="ycDrawerRef"
    :title="drawerTitle"
    width="600px"
  >
    <div class="h-full w-full flex flex-col">
      <NSpin
        :show="loading"
        class="h-full"
      >
        <div class="mb-12px flex items-center justify-between">
          <span>字典项名称：{{ dicName }}</span>
          <div
            v-if="!isView"
            class="flex items-center justify-end"
          >
            <NButton
              type="primary"
              @click="handleAdd"
              >添加</NButton
            >
          </div>
        </div>
        <NTable :single-line="false">
          <thead>
            <tr>
              <th>字典值编号</th>
              <th>字典值说明</th>
              <th>排序</th>
              <th v-if="!isView">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, index) in formData.list"
              :key="index"
            >
              <td class="w-30%">
                <NInput
                  v-model:value="item.dictValue"
                  placeholder="请输入字典值编号"
                  disabled
                ></NInput>
              </td>
              <td class="w-30%">
                <NInput
                  v-model:value="item.dictDescription"
                  placeholder="请输入字典值说明"
                  :disabled="isView"
                ></NInput>
              </td>
              <td class="w-30%">
                <NInputNumber
                  v-model:value="item.priority"
                  :min="1"
                  button-placement="both"
                  :disabled="isView"
                >
                  <template #add-icon>
                    <IconCarbonCaretUp />
                  </template>
                  <template #minus-icon>
                    <IconCarbonCaretDown />
                  </template>
                </NInputNumber>
              </td>
              <td v-if="!isView">
                <NButton
                  text
                  type="error"
                  @click="handleDelete(index)"
                  >删除</NButton
                >
              </td>
            </tr>
          </tbody>
        </NTable>
      </NSpin>
    </div>
    <template #footer>
      <NButton
        secondary
        strong
        @click="close"
        >返回</NButton
      >
      <NButton
        v-if="!isView"
        type="primary"
        :disabled="formData.list.length === 0"
        :loading="confirmLoading"
        @click="handleConfirm"
        >确定</NButton
      >
    </template>
  </YcDrawer>
</template>

<style scoped></style>
