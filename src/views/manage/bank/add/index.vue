<script setup lang="ts">
import YcFormPage from '@/components/common/yc-form-page.vue'
import DynamicForm from '@/components/business/dynamicForm.vue'
import type { FormConfig } from '@/typings/dynamic-form'
import { ref } from 'vue'
import useLoading from '~/packages/hooks/src/use-loading'
import { useRouterPush } from '@/hooks/useRouterPush'
import { getFormList } from './form'
import { useNaiveForm } from '@/hooks/useForm'

const { routerBack, route } = useRouterPush()

// 是否为编辑
const isEdit = computed(() => {
  return route.value.query.id
})

// 表单数据
const { formData, setFormData, resetFormData } = useNaiveForm({
  orgType: 'BANK',
  orgName: null,
  region: null,
  compensationRatio: null,
  receiptAccount: null,
  receiptName: null
})

const formList: Ref<FormConfig | any> = ref([])
const formConfig = computed(() => formList.value)

// 取消
const handleCancel = () => {
  routerBack()
}

// 表单事件
const handleFormEvent = (payload: any) => {}

// 提交
const DynamicFormRef: any = ref(null)
const { loading: submitLoading, startLoading: startSubmitLoading, endLoading: endSubmitLoading } = useLoading()
const handleSubmit = async () => {
  await DynamicFormRef.value.validate()
  const dynamicFormData = DynamicFormRef.value.getFormData()
  const params = Object.assign(formData.value, dynamicFormData)
  try {
    startSubmitLoading()
    const fetchApi = isEdit.value ? fetchManageApiOrgUpdate : fetchManageApiOrgSave
    const res = await fetchApi(params)
    if (!res.error) {
      window?.$message?.success(isEdit.value ? '编辑成功' : '新增成功')
      routerBack()
    }
  } finally {
    endSubmitLoading()
  }
}

// 初始化
const init = async () => {
  resetFormData()
  if (isEdit.value) {
    const res = await fetchManageApiOrgQueryPage({ id: route.value.query.id, pageNo: 1, pageSize: 1 })
    setFormData(res.data?.list?.[0] || {})
  }
  formList.value = getFormList({ query: route.value.query }) as FormConfig
}

onMounted(() => {
  init()
})
</script>

<template>
  <YcFormPage>
    <DynamicForm
      :config="formConfig"
      :initial-data="formData"
      @event="handleFormEvent"
      class="mb-10px"
      ref="DynamicFormRef"
    />
    <template #footer>
      <NButton @click="handleCancel"> 取消 </NButton>
      <NButton
        type="primary"
        @click="handleSubmit"
        :loading="submitLoading"
        >提交</NButton
      >
    </template>
  </YcFormPage>
</template>
