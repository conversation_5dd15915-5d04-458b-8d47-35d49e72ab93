import { FormConfig } from '@/typings/dynamic-form'

interface IParams {
  query: Record<string, any>
}

export const getFormList = (params: IParams): FormConfig => {
  const isEdit = !!unref(params?.query)?.id
  return [
    {
      title: '合作银行信息',
      items: [
        {
          label: '银行名称',
          type: 'BankSelectTree',
          RuleType: 'number',
          field: 'id',
          fieldLabel: 'orgName',
          required: true,
          disabled: isEdit
        },
        {
          label: '区域',
          type: 'RegionSelect',
          field: 'region',
          fieldLabel: 'regionName',
          required: true
        },
        {
          label: '风补比例',
          type: 'inputgroupNumber',
          field: 'compensationRatio',
          RuleType: 'number',
          props: {
            showButton: false,
            triggerText: '%',
            min: 0,
            max: 100
          }
        },
        {
          label: '收款账号',
          type: 'input',
          field: 'receiptAccount'
        },
        {
          label: '收款户名',
          type: 'input',
          field: 'receiptName'
        }
      ]
    }
  ]
}
