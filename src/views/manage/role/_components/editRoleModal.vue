<script setup lang="ts">
import type { Ref } from 'vue'
import { h, reactive, ref } from 'vue'
import { NTag, NTree } from 'naive-ui'
import { useLoading } from '@yc/hooks'
import { useFormRules, useNaiveForm } from '@/hooks/useForm'
import { fetchAddRole, fetchGetRoleById, fetchQueryElementList, fetchUpdateRole } from '@/service/api'

type Option = {
  label: string
  value: string
  children?: Option[]
}

interface IFormData {
  roleId?: number | null
  name: string
  description: string
  elementIdList: number[]
}

interface IOpenModalParams {
  id?: number | null
  type: 'add' | 'edit'
  data?: IFormData
}

interface IMenuTreeItem {
  pathArr: number[]
  value: string
  label: number
  children: IMenuTreeItem
}

interface Emits {
  (e: 'refresh'): void
}

const emits = defineEmits<Emits>()

const modalRef: Ref<Expose.YCModal | null> = ref(null)
const modalType = ref<'add' | 'edit'>('add')
const { loading, startLoading, endLoading } = useLoading()

const { formData, formRef, validate, resetFormData } = useNaiveForm({
  name: null,
  code: null,
  description: null,
  elementIdList: []
})
const { createRequiredRule } = useFormRules()

const rules = reactive({
  name: [
    {
      ...createRequiredRule('请输入角色名称'),
      trigger: 'blur'
    }
  ],
  code: [
    {
      ...createRequiredRule('请输入角色标识'),
      trigger: 'blur'
    }
  ],
  elementIdList: [
    {
      ...createRequiredRule('请选择关联权限'),
      trigger: 'change',
      type: 'array'
    }
  ]
})

// 获取菜单列表
const menuList: Ref<any> = ref([])
const manageMenuList: Ref<any> = ref([])
const mpMenuList: Ref<any> = ref([])
const roleData: Ref<any> = ref([])
const changeMenuList: Ref<any> = ref([])
const getMenuList = async () => {
  const transformTreeData = (data: IMenuTreeItem[], pathArr = []) => {
    return data.map((item: any) => {
      const { id, name, children, parentId, elementType, platformType } = item
      return {
        pathArr: [...pathArr, id],
        value: id,
        parentId: parentId,
        label: name,
        children: children && children.length ? transformTreeData(children, [...pathArr, id]) : null,
        suffix: () =>
          h(
            NTag,
            {
              size: 'small',
              type: elementType === 'MENU' ? 'primary' : undefined
            },
            {
              default: () => `${elementType === 'BUTTON' ? '按钮' : '菜单'} `
            }
          )
      }
    })
  }
  const promiseAll = await Promise.all([
    fetchQueryElementList({ platformType: 'MANAGE' }),
    fetchQueryElementList({ platformType: 'MANAGE_MP' })
  ])
  manageMenuList.value = transformTreeData(promiseAll[0].data)
  mpMenuList.value = transformTreeData(promiseAll[1].data)
}

const handleCancel = () => {
  modalRef.value?.close()
}

const open = async (param: IOpenModalParams) => {
  modalType.value = param.type
  if (param.type === 'edit') {
    const { id } = param
    const { data } = await fetchGetRoleById(id as number)
    if (data) {
      Object.assign(formData.value, {
        roleId: data.id,
        code: data.code,
        name: data.name,
        description: data.description,
        elementIdList: data.elementIdList
      })
      // 更新 roleData 的 elementIdList
      roleData.value = filteredElementIdList(data.elementIdList)
    } else {
      window.$message?.error('获取角色详情失败')
    }
  } else {
    resetFormData()
  }
  modalRef.value?.open()
}
getMenuList()
// 提交
const handleSubmit = async (e: MouseEvent) => {
  e.preventDefault()
  await validate()
  try {
    startLoading()
    let res = null
    if (modalType.value === 'add') {
      // 新增
      res = await fetchAddRole(formData.value)
    } else {
      // 编辑
      res = await fetchUpdateRole(formData.value)
    }
    if (res?.error || res.error?.code) return
    handleCancel()
    window.$message?.success(`${modalType.value === 'add' ? '新增' : '编辑'}成功`)
    emits('refresh')
  } finally {
    endLoading()
  }
}

// 选中树节点
// const handleCheckedTreeItem = (keys, option, meta) => {
//   console.log("🚀 ~ handleCheckedTreeItem ~ meta:",action, node, meta);
//   const { action, node } = meta;
//   if (action === "check") {
//     formData.elementIdList = [
//       ...new Set([...formData.elementIdList, node.value]),
//     ];
//   } else if (action === "uncheck") {
//     formData.elementIdList = formData.elementIdList.filter(
//       (item) => item !== node.value
//     );
//   }
// };

// 过滤出子节点 ID
const filteredElementIdList = (data: any) => {
  const childIds = new Set()
  const findChildren = (node: any) => {
    if (node.children && node.children.length > 0) {
      node.children.forEach((child: any) => {
        findChildren(child) // 递归查找子节点
      })
    } else {
      childIds.add(node.value) // 添加子节点 ID
    }
  }

  manageMenuList.value.forEach((node: any) => {
    findChildren(node)
  })

  // 过滤出只在子节点中的 ID
  return data.filter((id: any) => childIds.has(id))
}
// 获取所有选中的子节点的父节点
const getAllParentIds = (childIds) => {
  const parentIds = new Set()
  const findParent = (node) => {
    if (childIds.includes(node.value)) {
      parentIds.add(node.parentId) // 添加当前节点的父节点 ID
      return true // 找到目标节点
    }
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        if (findParent(child)) {
          if (node.parentId !== 0) {
            parentIds.add(node.parentId)
          }
          return true // 找到目标节点
        }
      }
    }
    return false // 未找到目标节点
  }
  // 遍历树形数据，查找每个节点的父节点
  manageMenuList.value.forEach((node) => {
    findParent(node)
  })
  // 递归查找所有父节点
  const allParentIds = Array.from(parentIds)
  allParentIds.forEach((id) => {
    let currentId = id
    while (currentId !== 0) {
      const parentNode = manageMenuList.value.find((node) => node.value === currentId)
      if (parentNode && parentNode.parentId !== 0) {
        parentIds.add(parentNode.parentId)
        currentId = parentNode.parentId
      } else {
        break
      }
    }
  })
  return Array.from(parentIds).filter((id) => id !== 0) // 过滤掉根节点 ID
}
const handleCascadeSelection = (keys: any) => {
  // const selectedKeys = new Set(keys);
  const parentIds = getAllParentIds(keys) // 获取所有父节点 ID
  const selectedKeys = new Set([...keys, ...parentIds]) // 合并并去重
  const updateKeys = (node: any) => {
    if (selectedKeys.has(node.value)) {
      // 选中父节点
      if (node.pathArr.length > 1) {
        const parentId = node.pathArr[node.pathArr.length - 2]
        selectedKeys.add(parentId)
      }
    }
    if (node.children) {
      node.children.forEach((child: any) => {
        updateKeys(child)
      })
    }
  }

  manageMenuList.value.forEach((item: any) => {
    updateKeys(item)
  })

  formData.value.elementIdList = Array.from(selectedKeys)
}
defineExpose({
  open
})
</script>

<template>
  <YcModal
    ref="modalRef"
    :title="`${modalType === 'add' ? '新增' : '编辑'}角色`"
    width="800px"
  >
    <NForm
      ref="formRef"
      :label-width="80"
      :model="formData"
      :rules="rules"
      label-placement="left"
      require-mark-placement="left"
    >
      <NFormItem
        label="角色名称"
        path="name"
      >
        <NInput
          v-model:value="formData.name"
          placeholder="请输入角色"
          maxlength="16"
          show-count
        />
      </NFormItem>
      <NFormItem
        label="角色标识"
        path="code"
      >
        <NInput
          v-model:value="formData.code"
          placeholder="请输入角色标识"
          maxlength="50"
          show-count
          :disabled="modalType == 'edit'"
        />
      </NFormItem>
      <NFormItem
        label="角色描述"
        path="description"
      >
        <NInput
          v-model:value="formData.description"
          placeholder="请输入角色描述"
          maxlength="100"
          show-count
          type="textarea"
        />
      </NFormItem>
      <NFormItem
        label="关联权限"
        path="elementIdList"
      >
        <div class="h-300px w-full flex flex-nowrap border-1px border-#e5e7eb rounded-6px">
          <div class="h-full w-50% flex flex-col gap-4px border-r-1px border-#e5e7eb p-12px">
            <div class="text-14px text-font_main font-700">管理后台权限</div>
            <div class="flex-1 overflow-auto">
              <NTree
                :default-checked-keys="roleData"
                block-line
                :data="manageMenuList"
                expand-on-click
                cascade
                checkable
                key-field="value"
                :on-update:checked-keys="handleCascadeSelection"
              />
            </div>
          </div>
        </div>
      </NFormItem>
    </NForm>
    <template #footer>
      <NButton @click="handleCancel">返回</NButton>
      <NButton
        type="primary"
        :loading="loading"
        @click="handleSubmit"
        >确定</NButton
      >
    </template>
  </YcModal>
</template>

<style scoped></style>
