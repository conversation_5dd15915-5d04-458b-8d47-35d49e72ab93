<script setup lang="ts">
import type { Ref } from 'vue'
import { h, ref } from 'vue'
import { NButton, NFlex, NPopconfirm } from 'naive-ui'
import type { TableUtil } from '@/typings/table'
import { useYcTable } from '@/hooks/useYcTable'
import { fetchDeleteRoleById, fetchQueryRolePage } from '@/service/api'
import EditRoleModal from './_components/editRoleModal.vue'
import YcSearchContent from '@/components/common/yc-search-content.vue'

// 新增角色
const editRoleModalRef = ref(null)
const handleAdd = () => {
  editRoleModalRef.value?.open({ type: 'add' })
}

const { tableRef, refreshTable } = useYcTable()
const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchQueryRolePage,
  apiParams: {
    keywords: null
  },
  columns: () => [
    {
      title: '角色名称',
      key: 'name',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '角色标识',
      key: 'code',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '角色描述',
      key: 'description',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '用户数',
      key: 'userNum',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '创建时间',
      key: 'createdTime',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '操作',
      key: 'action',
      width: '120px',
      render: (rowData) => {
        return h(NFlex, {}, () => [
          h(
            NButton,
            {
              type: 'primary',
              text: true,
              onClick: () => {
                editRoleModalRef.value?.open({ type: 'edit', id: rowData.id })
              }
            },
            { default: () => '编辑' }
          ),
          h(
            NPopconfirm,
            {
              onPositiveClick: async () => {
                const res = await fetchDeleteRoleById(rowData.id)
                if (!res.error) {
                  window.$message?.success('删除成功')
                  refreshTable()
                }
              }
            },
            {
              trigger: () => h(NButton, { type: 'error', text: true }, { default: () => '删除' }),
              default: () => '确定删除这个角色吗？'
            }
          )
        ])
      }
    }
  ]
})

const handleResetTable = () => {
  tableConfig.value.apiParams.keywords = null
  refreshTable(true)
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="角色管理"> </BorderTitleBar>
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
    >
      <template #header>
        <YcSearchContent
          @search="refreshTable"
          @reset="handleResetTable"
        >
          <YcSearchItem label="角色名称">
            <NInput
              v-model:value="tableConfig.apiParams.keywords"
              clearable
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <NButton
          type="primary"
          @click="handleAdd"
          >新增角色</NButton
        >
      </template>
    </YcTable>

    <EditRoleModal
      ref="editRoleModalRef"
      @refresh="refreshTable"
    />
  </YcContent>
</template>

<style scoped></style>
