<template>
  <YcContent>
    <BorderTitleBar title="消息通知" />
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
        >
          <YcSearchItem label="标题">
            <NInput
              v-model:value="tableConfig.apiParams.title"
              placeholder="请输入标题"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="类型">
            <NSelect
              v-model:value="tableConfig.apiParams.msgType"
              placeholder="请选择类型"
              :options="MsgTypeModeOptions"
              clearable
            />
          </YcSearchItem>
          <YcSearchItem label="状态">
            <NSelect
              v-model:value="tableConfig.apiParams.read"
              placeholder="请选择状态"
              :options="[
                {
                  label: '已读',
                  value: 1
                },
                {
                  label: '未读',
                  value: 0
                }
              ]"
              clearable
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <div class="flex items-center gap-8px">
          <NButton
            type="primary"
            @click="handleAllRead"
            :loading="readAllLoading"
            >全部已读</NButton
          >
        </div>
      </template>
    </YcTable>
  </YcContent>
</template>

<script setup lang="ts">
import { useRouterPush } from '@/hooks/useRouterPush'
import { TableUtil } from '@/typings/table'
import { createTextButton } from '@/utils/common'
import { useYcTable } from '@/hooks/useYcTable'
import { fetchManageApiNoticeBatchRead, fetchManageApiNoticeQueryPage, fetchManageApiNoticeRead } from '@/service/api'
import { MsgTypeModeOptions, MsgTypeModeRecord } from '@/constants/app'
import { NTag } from 'naive-ui'
import { useLoading } from '~/packages/hooks'

const { routerPushByKey } = useRouterPush()
const { tableRef, refreshTable } = useYcTable()

const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchManageApiNoticeQueryPage,
  apiParams: {
    title: null,
    msgType: null,
    read: null
  },
  columns: () => [
    {
      title: '来源',
      key: 'origin',
      width: 140,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '类型',
      key: 'msgType',
      width: 140,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h('span', {}, { default: () => MsgTypeModeRecord[rowData.msgType as UnionKey.MsgType] })
      }
    },
    {
      title: '标题',
      key: 'title',
      width: 300,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '通知内容',
      key: 'content',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '发送时间',
      key: 'createdTime',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '状态',
      key: 'read',
      width: 120,
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          NTag,
          { type: rowData.read === 0 ? 'warning' : 'success', size: 'small', rounded: true, bordered: false },
          { default: () => (rowData.read === 0 ? '未读' : '已读') }
        )
      }
    },
    {
      title: '操作',
      key: '__operate',
      width: 120,
      resizable: false,
      render: (rowData) => {
        const handleBtn = createTextButton({
          text: '去处理',
          onClick: async () => {
            await fetchManageApiNoticeRead({ id: rowData.id })
            await routerPushByKey('supplement_todo_info', {
              query: {
                id: rowData.attribute as string
              }
            })
          }
        })
        const readBtn = createTextButton({
          text: '已读',
          onClick: async () => {
            const res = await fetchManageApiNoticeRead({ id: rowData.id })
            if (!res.error) {
              window?.$message?.success('已读成功')
              refreshTable(false)
            }
          }
        })
        return h('div', { class: 'flex items-center gap-12px' }, [
          rowData.msgType === 'RISK_COMPENSATION_PROCESS' && handleBtn,
          rowData.read === 0 && readBtn
        ])
      }
    }
  ]
})

// 查询
const handleSearch = () => {
  tableRef.value?.refreshData(true)
}

// 重置
const handleReset = () => {
  tableConfig.value.apiParams = Object.assign(tableConfig.value.apiParams, {
    title: null,
    msgType: null,
    read: null
  })
  tableRef.value?.refreshData(true)
}

// 全部已读
const { loading: readAllLoading, startLoading: startReadAllLoading, endLoading: endReadAllLoading } = useLoading()
const handleAllRead = async () => {
  try {
    startReadAllLoading()
    const res = await fetchManageApiNoticeBatchRead({})
    if (!res.error) {
      window?.$message?.success('全部已读成功')
      refreshTable(false)
    }
  } finally {
    endReadAllLoading()
  }
}
</script>
