<script setup lang="ts">
import { computed, h, ref } from 'vue'
import type { FormRules, SelectRenderLabel } from 'naive-ui'
import { NSpace } from 'naive-ui'
import { useBoolean, useLoading } from '@yc/hooks'
import svgIcon from '@/components/custom/svg-icon.vue'
import { fetchAddElement, fetchGetElementById, fetchQueryApiList, fetchQueryElementList, fetchUpdateElement } from '@/service/api'
import { ElementTypeModeOptions } from '@/constants/app'
import { useFormRules, useNaiveForm } from '@/hooks/useForm'

interface IFormData {
  elementId?: number | null
  parentId?: number | null
  name: string
  iconUrl: string | null
  pageUrl: string
  activeMenu: ''
  apiUrlList: string[]
}

type EditType = 'addMenu' | 'addBtn' | 'addPage' | 'edit'

interface IOpenModalParams {
  type: EditType
  platformType: UnionKey.PlatformType
  elementType: UnionKey.ElementType
  id?: number
}

interface Emits {
  (e: 'refresh'): void
}

const emits = defineEmits<Emits>()

const { formRef, formData, resetFormData, validate } = useNaiveForm({
  name: null,
  parentId: null,
  elementId: null,
  iconUrl: null,
  pageUrl: null,
  isHide: false,
  activeMenu: null,
  elementType: 'MENU',
  platformType: 'MANAGE',
  apiUrlList: []
})
const { createRequiredRule } = useFormRules()

const renderLabel: SelectRenderLabel = (option) => {
  return h(
    NSpace,
    {
      align: 'center'
    },
    [
      h(
        svgIcon,
        {
          icon: '',
          size: 22
        },
        {}
      ),
      h(
        'span',
        {
          class: 'ml-8px'
        },
        option.label
      )
    ]
  )
}

const modalType = ref<EditType>('addMenu')
const { loading, startLoading, endLoading } = useLoading()
const { bool: isShow, setTrue: setShow, setFalse: setHidden } = useBoolean()

// 是否是新增
const isAdd = computed(() => ['addMenu', 'addPage', 'addBtn'].includes(modalType.value))

const rules: FormRules = {
  name: [
    {
      ...createRequiredRule('请输入名称'),
      trigger: 'blur'
    }
  ],
  pageUrl: [{ ...createRequiredRule('请输入页面路径'), trigger: 'blur' }],
  apiUrlList: []
}

// 菜单列表
const menuTreeList = ref([])
const getMenuList = async () => {
  const transformTreeData = (data: any) => {
    return data.map((item: any) => {
      const { name, children } = item
      return {
        key: item.pageUrl,
        label: name,
        children: children && children.length ? transformTreeData(children) : null
      }
    })
  }
  const { data = [] } = await fetchQueryElementList({
    platformType: formData.value.platformType
  })
  menuTreeList.value = transformTreeData(data)
}

// API配置列表
const apiUrlOptions = ref([])
// 打开
const open = async (param: IOpenModalParams) => {
  const { type, platformType: _platformType, elementType: _elementType, id = 0 } = param
  modalType.value = type
  if (param.type === 'edit') {
    const { data } = await fetchGetElementById(id)
    if (data) {
      Object.assign(formData.value, {
        elementId: data.id,
        name: data.name,
        iconUrl: data.iconUrl,
        pageUrl: data.pageUrl,
        isHide: data.isHide,
        activeMenu: data?.activeMenu ?? null,
        apiUrlList: data.apiList.map((i) => i.apiUrl),
        parentId: null,
        elementType: _elementType,
        platformType: _platformType
      })
    }
  } else {
    resetFormData()
    formData.value.elementType = _elementType
    formData.value.platformType = _platformType
    formData.value.parentId = id
  }
  setShow()
  if (['MENU', 'PAGE'].includes(formData.value.elementType)) {
    const { data = [] } = await fetchQueryApiList({})
    apiUrlOptions.value = data.map((item) => ({
      label: item.name,
      value: item.apiUrl
    }))
    await getMenuList()
  }
}

const isMenu = computed(() => formData.value.elementType === 'MENU')
const isPage = computed(() => formData.value.elementType === 'PAGE')
const isBtn = computed(() => formData.value.elementType === 'BUTTON')
const curElementTypeName = computed(() => (isMenu.value ? '菜单' : isPage.value ? '页面' : '按钮'))

// 关闭
const close = () => {
  setHidden()
}

// 弹窗确定
const handleConfirm = async (e: MouseEvent) => {
  e.preventDefault()
  await validate()
  try {
    startLoading()
    let res = null
    if (isAdd.value) {
      // 新增
      res = await fetchAddElement(formData.value)
    } else {
      // 编辑
      res = await fetchUpdateElement(formData.value)
    }
    if (res?.error || res.error?.code) return
    close()
    window.$message?.success(`${isAdd.value ? '新增' : '编辑'}${curElementTypeName.value}成功`)
    emits('refresh')
  } finally {
    endLoading()
  }
}

defineExpose({
  open,
  close
})
</script>

<template>
  <NSpace
    vertical
    class="h-full flex-1"
    :wrap-item="false"
  >
    <template v-if="isShow">
      <Transition
        name="fade-slide"
        mode="out-in"
        appear
      >
        <NSpace
          class="h-full w-full"
          :wrap-item="false"
          vertical
          justify="space-between"
        >
          <NForm
            ref="formRef"
            :label-width="80"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
          >
            <BorderTitleBar
              :title="`${isAdd ? '新增' : '编辑'}权限${curElementTypeName}`"
              class="mb-16px"
            />
            <NFormItem label="元素类型">
              <NSelect
                v-model:value="formData.elementType"
                :options="ElementTypeModeOptions"
                disabled
              />
            </NFormItem>
            <template v-if="isMenu || isPage">
              <NFormItem
                :label="`${curElementTypeName}名称`"
                path="name"
              >
                <NInput
                  v-model:value="formData.name"
                  :placeholder="`请输入${curElementTypeName}名称`"
                  maxlength="20"
                  show-count
                  clearable
                />
              </NFormItem>
              <NSpace
                :wrap-item="false"
                :wrap="false"
              >
                <NFormItem
                  label="是否隐藏"
                  class="w-50%"
                >
                  <NSwitch v-model:value="formData.isHide" />
                </NFormItem>
                <NFormItem
                  v-if="formData.isHide"
                  label="活动菜单"
                  class="w-50%"
                >
                  <NTreeSelect
                    v-model:value="formData.activeMenu"
                    :options="menuTreeList"
                    clearable
                  />
                </NFormItem>
              </NSpace>
              <NSpace
                :wrap-item="false"
                :wrap="false"
              >
                <NFormItem
                  label="图标"
                  class="w-50%"
                >
                  <NInput
                    v-model:value="formData.iconUrl"
                    clearable
                    placeholder="请输入图标地址"
                    :disabled="isPage"
                  />
                </NFormItem>
                <NFormItem
                  :label="`${curElementTypeName}地址`"
                  class="w-50%"
                >
                  <NInput
                    v-model:value="formData.pageUrl"
                    placeholder="请输入页面地址"
                    clearable
                  />
                </NFormItem>
              </NSpace>
              <!-- <NFormItem label="API配置">
                <NTransfer
                  ref="transfer"
                  v-model:value="formData.apiUrlList"
                  :options="apiUrlOptions"
                  source-filterable
                />
              </NFormItem> -->
            </template>
            <template v-else>
              <NFormItem
                label="按钮名称"
                path="name"
              >
                <NInput
                  v-model:value="formData.name"
                  placeholder="请输入按钮名称"
                  maxlength="10"
                  show-count
                  clearable
                />
              </NFormItem>
              <NFormItem
                label="按钮值"
                path="pageUrl"
              >
                <NInput
                  v-model:value="formData.pageUrl"
                  placeholder="请输入按钮权限值"
                  clearable
                />
              </NFormItem>
            </template>
          </NForm>
          <NSpace
            justify="end"
            class="h-56px border-t border-#E1E4EB px-20px"
            align="center"
          >
            <NButton @click="close">返回</NButton>
            <NButton
              type="primary"
              :loading="loading"
              @click="handleConfirm"
              >确定</NButton
            >
          </NSpace>
        </NSpace>
      </Transition>
    </template>
    <template v-else>
      <Transition
        name="fade-slide"
        mode="out-in"
        appear
      >
        <NSpace
          align="center"
          justify="center"
          class="h-full"
        >
          <NEmpty description="请选择新增或编辑菜单"></NEmpty>
        </NSpace>
      </Transition>
    </template>
  </NSpace>
</template>

<style scoped></style>
