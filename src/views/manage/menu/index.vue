<script setup lang="ts">
import { useLoading } from '@yc/hooks'
import type { Ref } from 'vue'
import { h, ref } from 'vue'
import type { TreeDropInfo, TreeOption, TreeProps } from 'naive-ui'
import { NDropdown, NFlex, NTag } from 'naive-ui'
import { fetchDeleteElementById, fetchQueryElementList, fetchSortElement } from '@/service/api'
import { ElementTypeModeRecord } from '@/constants/app'
import EditPermissionModal from './_components/editPermissionModal.vue'

const editPermissionModalRef = ref(null)
const searchTreeKeywords = ref('')
const platformType: Ref<UnionKey.PlatformType> = ref('MANAGE')

const { loading, startLoading, endLoading } = useLoading()

// 打开新增窗口
const handleAdd = (type: 'addMenu' | 'addBtn' | 'edit') => {
  editPermissionModalRef.value?.open({
    type,
    platformType: platformType.value,
    elementType: 'MENU'
  })
}

// 获取菜单列表
const menuList: Ref<any> = ref([])
const getMenuList = async (value?: UnionKey.PlatformType) => {
  value && (platformType.value = value)
  startLoading()
  const transformTreeData = (data: any) => {
    return data.map((item: any) => {
      const { id, name, children } = item
      return {
        key: id,
        label: name,
        data: item,
        children: children && children.length ? transformTreeData(children) : null
      }
    })
  }
  const { data = [] } = await fetchQueryElementList({
    platformType: platformType.value
  })
  menuList.value = transformTreeData(data)
  endLoading()
}

// 删除菜单
const deleteMenu = async (id: number) => {
  const res = await fetchDeleteElementById(id)
  if (res.error) return
  window?.$message?.success('删除菜单成功')
  await getMenuList()
}

// 渲染树后缀节点
const renderSuffix = ({ option }: { option: TreeOption }) => {
  const { elementType } = option.data
  const dropDownOptions = [
    {
      label: '新增子菜单',
      key: 'addMenu',
      show: elementType === 'MENU'
    },
    // {
    //   label: '新增页面',
    //   key: 'addPage',
    //   show: elementType === 'MENU'
    // },
    {
      label: '新增子按钮',
      key: 'addBtn',
      show: elementType === 'MENU'
    },
    {
      label: '编辑',
      key: 'edit'
    },
    {
      label: '删除',
      key: 'delete'
    }
  ]
  return h(
    NFlex,
    {
      align: 'center',
      size: 12
    },
    () => [
      h(
        NTag,
        {
          size: 'small',
          type: elementType === 'MENU' ? 'primary' : elementType === 'PAGE' ? 'warning' : undefined,
          bordered: false
        },
        {
          default: () => ElementTypeModeRecord[elementType]
        }
      ),
      h(
        NDropdown,
        {
          size: 'small',
          trigger: 'click',
          options: dropDownOptions,
          onSelect: (type) => {
            if (type === 'delete') {
              return deleteMenu(option.key as number)
            }
            editPermissionModalRef.value?.open({
              type,
              platformType: option.data.platformType,
              elementType: type === 'edit' ? option.data.elementType : type === 'addMenu' ? 'MENU' : type === 'addPage' ? 'PAGE' : 'BUTTON',
              id: option.key
            })
          }
        },
        {
          default: () => {
            return h(
              'div',
              {
                class: 'text-gray-500 icon-mdi:dots-horizontal'
              },
              {}
            )
          }
        }
      )
    ]
  )
}

// 更新排序节点
const sortMenus = async (parentId: number | null, elementList: number[]) => {
  const res = await fetchSortElement({
    parentId,
    elementList
  })
  await getMenuList()
  if (res.error) return
  window?.$message?.success('更新排序成功')
}

const findSiblingsAndIndex = (node: TreeOption, nodes?: TreeOption[]): [TreeOption[], number] | [null, null] => {
  if (!nodes) return [null, null]
  for (let i = 0; i < nodes.length; ++i) {
    const siblingNode = nodes[i]
    if (siblingNode.key === node.key) return [nodes, i]
    const [siblings, index] = findSiblingsAndIndex(node, siblingNode.children)
    if (siblings && index !== null) return [siblings, index]
  }
  return [null, null]
}

// 拖放节点
const handleDrop = async ({ node, dragNode, dropPosition }: TreeDropInfo) => {
  if (node.data.parentId !== dragNode.data.parentId || dropPosition === 'inside') return
  const [dragNodeSiblings, dragNodeIndex] = findSiblingsAndIndex(dragNode, menuList.value)
  if (dragNodeSiblings === null || dragNodeIndex === null) return
  dragNodeSiblings.splice(dragNodeIndex, 1)
  if (dropPosition === 'before') {
    const [nodeSiblings, nodeIndex] = findSiblingsAndIndex(node, menuList.value)
    if (nodeSiblings === null || nodeIndex === null) return
    nodeSiblings.splice(nodeIndex, 0, dragNode)
    const parentId = nodeSiblings[nodeSiblings.length - 1]?.data?.parentId || null
    await sortMenus(parentId, nodeSiblings.map((i) => i.key) as number[])
  } else if (dropPosition === 'after') {
    const [nodeSiblings, nodeIndex] = findSiblingsAndIndex(node, menuList.value)
    if (nodeSiblings === null || nodeIndex === null) return
    nodeSiblings.splice(nodeIndex + 1, 0, dragNode)
    const parentId = nodeSiblings[nodeIndex]?.data?.parentId || null
    await sortMenus(parentId, nodeSiblings.map((i) => i.key) as number[])
  }
}

// 初始化
const init = async () => {
  await getMenuList()
}

init()

type TreeThemeOverrides = NonNullable<TreeProps['themeOverrides']>
const treeThemeOverrides: TreeThemeOverrides = {
  nodeHeight: '40px',
  nodeColorActive: '#EFF5FD',
  nodeTextColor: '#3A3F50',
  nodeBorderRadius: '4px'
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="权限管理" />
    <div class="wh-full flex gap-16px overflow-y-auto">
      <NSpace
        vertical
        class="h-full w-360px border-1 border-#eee rounded py-20px pl-16px"
        :wrap-item="false"
      >
        <div class="pr-16px">
          <NInput
            v-model:value="searchTreeKeywords"
            placeholder="搜索"
            :maxlength="10"
            show-count
            clearable
          ></NInput>
        </div>
        <NSpace
          vertical
          class="h-full flex-1 overflow-y-auto"
        >
          <NButton
            type="primary"
            text
            @click="handleAdd('addMenu')"
          >
            <div class="icon-mdi:add"></div>
            新增菜单
          </NButton>
          <NSpin
            :show="loading"
            class="pr-16px"
          >
            <NTree
              block-line
              :data="menuList"
              :render-suffix="renderSuffix"
              :pattern="searchTreeKeywords"
              draggable
              :theme-overrides="treeThemeOverrides"
              @drop="handleDrop"
            />
          </NSpin>
        </NSpace>
      </NSpace>

      <EditPermissionModal
        ref="editPermissionModalRef"
        @refresh="getMenuList"
      />
    </div>
  </YcContent>
</template>

<style scoped lang="scss">
:deep(.n-tree-node--selected .n-tree-node-content__suffix) {
  color: #204d87;
}

:deep(.n-tree-node--selected .n-tree-node-content__text) {
  color: #204d87;
}

:deep(.n-tree-node--selected .n-tree-node-switcher__icon) {
  color: #204d87;
}
</style>
