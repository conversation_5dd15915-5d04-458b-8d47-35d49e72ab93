<script setup lang="ts">
import type { Ref } from 'vue'
import { computed, ref, unref } from 'vue'
import { useLoading } from '@yc/hooks'
import { cloneDeep } from 'lodash-es'
import { useFormRules, useNaiveForm } from '@/hooks/useForm'
import { fetchAddOrgQualificate, fetchGetOrgQualificateById, fetchUpdateOrgQualificate } from '@/service/api'
import dayjs from 'dayjs'
import { useAuthStore } from '@/store/modules/auth'
import { formatParams } from '@/utils/common'

const authStore = useAuthStore()

interface IOpenParams {
  type: Common.EditOrAdd | 'view'
  id?: number
}

const emit = defineEmits(['refresh'])

const { loading, startLoading, endLoading } = useLoading()
const { loading: spinLoading, startLoading: startSpinLoading, endLoading: endSpinLoading } = useLoading()

const ycDrawerRef: Ref<Expose.YCDrawer | null> = ref(null)
const drawerType = ref<IOpenParams['type']>('add')

const isAdd = computed(() => drawerType.value === 'add')
const isView = computed(() => drawerType.value === 'view')

const { formData, formRef, resetFormData, validate, setFormData } = useNaiveForm({
  orgCode: '长沙水泵厂有限公司',
  orgName: '79470468-2',
  orgCertificateId: 604271317655109,
  certificateCode: '',
  qualificateType: null,
  superviseType: null,
  detail: null,
  remark: null,
  problem: null,
  supervisionForm: null,
  notice: null,
  qualificateUser: unref(authStore.userInfo)?.name,
  qualificateTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
})
const { createRequiredRule } = useFormRules()

const rules = {
  orgName: [{ ...createRequiredRule('请输入单位名称'), trigger: 'blur' }],
  certificateCode: [{ ...createRequiredRule('请输入资格证书编码'), trigger: 'blur' }],
  qualificateType: [{ ...createRequiredRule('请选择资格证书类别'), trigger: 'blur' }]
}

// 打开抽屉
const open = async (params: IOpenParams) => {
  const { type, id = 0, organizaId } = params
  drawerType.value = type
  resetFormData()
  ycDrawerRef.value?.open()
  try {
    startSpinLoading()
    if (type !== 'add') {
      const { data = {} } = await fetchGetOrgQualificateById(id)
      Object.assign(formData.value, {
        ...data
      })
    } else {
      setFormData({
        organizaId: organizaId || null
      })
    }
    console.log(formData.value)
  } finally {
    endSpinLoading()
  }
}

// 关闭抽屉
const close = () => {
  ycDrawerRef.value?.close()
}

// 确认提交
const handleSubmit = async () => {
  await validate()
  try {
    startLoading()
    let res = null
    const sendParams = cloneDeep(formData.value)
    if (drawerType.value === 'add') {
      res = await fetchAddOrgQualificate(formatParams(sendParams))
    } else {
      res = await fetchUpdateOrgQualificate(formatParams(sendParams))
    }
    if (res.error) return
    window?.$message?.success(`${isAdd.value ? '新增成功' : '编辑成功'} `)
    emit('refresh')
    close()
  } finally {
    endLoading()
  }
}

defineExpose({
  open
})
</script>

<template>
  <YcDrawer
    ref="ycDrawerRef"
    width="700px"
    :title="`${isAdd ? '新增' : isView ? '查看' : '编辑'}资格保持情况`"
  >
    <NSpin :show="spinLoading">
      <NForm
        ref="formRef"
        :model="formData"
        label-align="left"
        label-placement="left"
        :rules="rules"
        require-mark-placement="left"
        label-width="130"
        :disabled="isView"
      >
        <NFormItem
          label="监督形式"
          path="supervisionForm"
        >
          <NRadioGroup
            v-model:value="formData.supervisionForm"
            :disabled="isView"
          >
            <NRadio
              v-for="item in []"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </NRadio>
          </NRadioGroup>
        </NFormItem>
        <!--        <NFormItem label="单位代号" path="orgCode">-->
        <!--          <NInput v-model:value="formData.orgCode" disabled />-->
        <!--        </NFormItem>-->
        <!--        <NFormItem label="单位名称" path="orgName">-->
        <!--          <NInput v-model:value="formData.orgName" placeholder="请输入单位名称" clearable />-->
        <!--        </NFormItem>-->
        <NFormItem
          label="资格证书编号"
          path="certificateCode"
        >
          <NInput
            v-model:value="formData.certificateCode"
            :disabled="!isAdd"
            placeholder="请输入资格证书编号"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="资格证书类别"
          path="qualificateType"
        >
          <NInput
            v-model:value="formData.qualificateType"
            :disabled="!isAdd"
            placeholder="请输入资格证书类别"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="监督方式"
          path="superviseType"
        >
          <NRadioGroup
            v-model:value="formData.superviseType"
            :disabled="isView"
          >
            <NRadio
              v-for="item in []"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </NRadio>
          </NRadioGroup>
        </NFormItem>
        <NFormItem
          label="监督内容及要求"
          path="detail"
        >
          <NInput
            v-model:value="formData.detail"
            type="textarea"
            show-count
            maxlength="200"
            placeholder="请输入监督内容及要求"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="监督过程记录"
          path="remark"
        >
          <NInput
            v-model:value="formData.remark"
            type="textarea"
            show-count
            maxlength="200"
            placeholder="请输入监督过程记录"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="发现的一般性问题或重大事项"
          path="problem"
        >
          <NInput
            v-model:value="formData.problem"
            type="textarea"
            show-count
            maxlength="200"
            placeholder="请输入发现的一般性问题或重大事项"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="附件"
          path="fileList"
        >
          <yc-upload
            :max="3"
            :disabled="isView"
            v-model:file-list="formData.fileList"
          >
            <NButton>上传文件</NButton>
          </yc-upload>
        </NFormItem>
        <NFormItem
          label="整改通知书编号"
          path="notice"
        >
          <NInput
            v-model:value="formData.notice"
            placeholder="请输入整改通知书编号"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="记录人"
          path="qualificateUser"
        >
          <NInput
            v-model:value="formData.qualificateUser"
            placeholder="请输入记录人"
            clearable
          />
        </NFormItem>
        <NFormItem
          label="记录日期"
          path="qualificateTime"
        >
          <NDatePicker
            v-model:formatted-value="formData.qualificateTime"
            type="date"
            class="w-full"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </NFormItem>
      </NForm>
    </NSpin>
    <template #footer>
      <NButton
        secondary
        strong
        @click="close"
        >返回</NButton
      >
      <NButton
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSubmit"
        >确定</NButton
      >
    </template>
  </YcDrawer>
</template>

<style scoped></style>
