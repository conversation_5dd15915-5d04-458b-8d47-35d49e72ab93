<script setup lang="ts">
import type { Ref } from 'vue'
import { h, ref, reactive } from 'vue'
import { NButton, NFlex, NPopconfirm } from 'naive-ui'
import type { TableUtil } from '@/typings/table'
import { useYcTable } from '@/hooks/useYcTable'
import { fetchOrgQueryOrgQualificate, fetchDeleteOrgQualificateById } from '@/service/api'
import EditQualificationDrawer from './editDrawer.vue'
import { createTextButton, createConfirmTextButton } from '@/utils/common'

const editQualificationDrawerRef = ref(null)

const handleAdd = () => {
  editQualificationDrawerRef.value?.open({ type: 'add', organizaId: 1 })
}

const { tableRef, refreshTable } = useYcTable()
const tableConfig: Ref<TableUtil.TableConfig> = ref({
  apiFn: fetchOrgQueryOrgQualificate,
  apiParams: {
    keywords: null
  },
  columns: () => [
    {
      title: '资格证书编号',
      key: 'certificateCode',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '证书名称',
      key: 'certificateName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '监督形式',
      key: 'supervisionForm',
      ellipsis: {
        tooltip: true
      },
      render: (row) => {
        // return SupervisionFormModeRecord[
        //   row.supervisionForm as UnionKey.SupervisionForm
        // ];
      }
    },
    {
      title: '监督方式',
      key: 'superviseType',
      ellipsis: {
        tooltip: true
      },
      render: (row) => {
        // return SuperviseTypeModeRecord[
        //   row.superviseType as UnionKey.SuperviseType
        // ];
      }
    },
    {
      title: '记录人',
      key: 'qualificateUser',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '日期',
      key: 'qualificateTime',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '操作',
      key: 'action',
      width: '150px',
      render: (rowData: any) => {
        const viewBtn = createTextButton({
          text: '查看',
          permissionCode: '10060',
          onClick: () => {
            editQualificationDrawerRef.value?.open({
              type: 'view',
              id: rowData.id
            })
          }
        })
        const editBtn = createTextButton({
          text: '编辑',
          permissionCode: '10061',
          onClick: () => {
            editQualificationDrawerRef.value?.open({
              type: 'edit',
              id: rowData.id
            })
          }
        })
        const delBtn = createConfirmTextButton({
          text: '删除',
          permissionCode: '10061',
          confirmText: '确定删除该数据？',
          onClick: async () => {
            const res = await fetchDeleteOrgQualificateById(rowData.id)
            if (res.error) return
            window?.$message?.success('删除成功')
            refreshTable(true)
          }
        })
        return h(NFlex, {}, { default: () => [viewBtn, editBtn, delBtn] })
      }
    }
  ]
})
</script>

<template>
  <div>
    <YcTable
      ref="tableRef"
      :table-config="tableConfig"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-#3A3F50 text-16px font-500 ml-2px">数据列表</span>
          <div class="yc-table__header">
            <NButton
              type="primary"
              @click="handleAdd"
              v-permission="`10061`"
              >新增</NButton
            >
          </div>
        </div>
      </template>
    </YcTable>

    <EditQualificationDrawer
      ref="editQualificationDrawerRef"
      @refresh="refreshTable"
    />
  </div>
</template>

<style scoped></style>
