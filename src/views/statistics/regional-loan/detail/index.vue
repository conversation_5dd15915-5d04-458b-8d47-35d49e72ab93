<script setup lang="ts">
import YcContent from '@/components/common/yc-content.vue'
import { TableUtil } from '@/typings/table'
import { ref } from 'vue'
import { downloadFileByUrl } from '@/utils/common'
import { DataTableCreateSummary, NButton } from 'naive-ui'
import { useYcTable } from '@/hooks/useYcTable'
import { $toFixed } from '@/globals'
import { fetchManageApiLoanStatisticQueryPageByRegionOrg, fetchManageApiLoanStatisticQueryPageByRegionOrgExport } from '@/service/api'
import { useRouterPush } from '@/hooks/useRouterPush'
import CompanyDetailDrawer from '@/views/statistics/_components/companyDetailDrawer.vue'

// 全局合计
const totalInfo: any = ref({})
// 单页合计
const sumInfo: any = ref({})

const route = useRoute()
const { routerBack } = useRouterPush()

const companyDetailDrawerRef = ref(null)

const { tableRef, refreshTable } = useYcTable()
const tableConfig = ref<TableUtil.TableConfig>({
  apiFn: fetchManageApiLoanStatisticQueryPageByRegionOrg,
  apiParams: {
    region: route.query.region,
    recordDateStart: null,
    recordDateEnd: null
  },
  transformer: (res: any) => {
    const { list = [], rows = [], pageNum = 1, pageNo = 1, pageSize = 10, total = 0 } = res.data.pageInfo || {}
    const bindPageSize = pageSize <= 0 ? 10 : pageSize
    const recordsWithIndex = [...(list ? list : []), ...(rows ? rows : [])].map((item: any, index: number) => {
      return {
        ...item,
        index: (pageNum - 1) * pageSize + index + 1
      }
    })
    totalInfo.value = res.data.totalInfo
    sumInfo.value = res.data.sumInfo
    return {
      data: recordsWithIndex,
      totalData: res.data.sumInfo,
      pageNum,
      pageNo,
      pageSize: bindPageSize,
      total
    }
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60
    },
    {
      title: '机构名称',
      key: 'name',
      width: 120,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          NButton,
          {
            type: 'primary',
            text: true,
            onClick: () => {
              companyDetailDrawerRef.value?.open({
                orgId: rowData.id,
                loanType: rowData.loanType,
                region: route.query.region,
                title: '各区域贷款统计(省市)明细(企业)'
              })
            }
          },
          {
            default: () => rowData.name
          }
        )
      }
    },
    {
      title: '户数',
      key: 'enterpriseNum',
      width: 100,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款笔数',
      key: 'loanNum',
      width: 120,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计授信金额（万元）',
      key: 'sumCreditAmount',
      width: 160,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款备案金额（万元）',
      key: 'sumRecordAmount',
      width: 180,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '信用贷款',
      key: 'count7',
      align: 'center',
      children: [
        {
          title: '贷款笔数',
          key: 'loanNumCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款金额（万元）',
          key: 'sumLoanAmountCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款备案金额（万元）',
          key: 'sumRecordAmountCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷笔数',
          key: 'loanNumFirstCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷金额（万元）',
          key: 'sumLoanAmountFirstCredit',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷户数',
          key: 'enterpriseNumFirstCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    },
    {
      title: '担保贷款',
      key: 'count8',
      align: 'center',
      children: [
        {
          title: '贷款笔数',
          key: 'loanNumGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款金额（万元）',
          key: 'sumLoanAmountGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款备案金额（万元）',
          key: 'sumRecordAmountGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷笔数',
          key: 'loanNumFirstGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷金额（万元）',
          key: 'sumLoanAmountFirstGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷户数',
          key: 'enterpriseNumFirstGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    }
  ]
})
const searchTimeRange = ref(null)
const dateConfirm = (val: any) => {
  if (val) {
    tableConfig.value.apiParams.recordDateStart = val[0]
    tableConfig.value.apiParams.recordDateEnd = val[1]
  } else {
    tableConfig.value.apiParams.recordDateStart = ''
    tableConfig.value.apiParams.recordDateEnd = ''
  }
}

// 查询
const handleSearch = () => {
  refreshTable(true)
}
// 重置
const handleReset = () => {
  tableConfig.value.apiParams = Object.assign({}, tableConfig.value.apiParams, {
    recordDateStart: null,
    recordDateEnd: null
  })
  searchTimeRange.value = null
  refreshTable()
}
const modalRef: any = ref(null)
const HandleExport = () => {
  modalRef.value.open()
}
//导出
const exportSign = 'loanStatistic/queryPageByRegionOrgExport'
const handleData = async (data: any) => {
  let params: any = {
    region: route.query.region,
    pageSize: '',
    pageNo: 1,
    suffix: data.suffix,
    exportSign: exportSign,
    exportParamsList: [],
    allParamsList: []
  }
  if (data.pageSize == 'all') {
    params.pageSize = tableRef.value?.pagination.itemCount
  } else {
    params.pageSize = data.totalLimit
  }
  data.selectedColumns.map((item: any) => {
    params.allParamsList.push(item.key)
    if (item.checked) {
      params.exportParamsList.push(item.key)
    }
  })
  let res = await fetchManageApiLoanStatisticQueryPageByRegionOrgExport(params)
  await downloadFileByUrl(res.data.accessUrl, res.data.fileName)
  closeexport()
}
const closeexport = () => {
  modalRef.value.close()
}

// 合计行生成函数
const summary: DataTableCreateSummary = (pageData) => {
  // 需要合计的数值字段列表
  const sumKeys = [
    'enterpriseNum',
    'loanNum',
    'sumCreditAmount',
    'sumRecordAmount',
    'loanNumCredit',
    'sumLoanAmountCredit',
    'sumRecordAmountCredit',
    'loanNumFirstCredit',
    'sumLoanAmountFirstCredit',
    'sumCreditAmountFirstCredit',
    'sumLoanAmountFirstCredit',
    'enterpriseNumFirstCredit',
    'loanNumGuarantee',
    'sumLoanAmountGuarantee',
    'sumRecordAmountGuarantee',
    'loanNumFirstGuarantee',
    'sumLoanAmountFirstGuarantee',
    'sumCreditAmountFirstGuarantee',
    'enterpriseNumFirstGuarantee'
  ]
  return {
    name: {
      value: h('div', { class: 'flex flex-col gap-12px' }, [h('span', '单页合计'), h('span', '合计')])
    },
    ...Object.fromEntries(
      sumKeys.map((key) => [
        key,
        {
          value: h('div', { class: 'flex flex-col gap-12px' }, [
            h(
              'span',
              sumKeys.some((k) => k === key && key.includes('Amount')) ? $toFixed(sumInfo.value[key]) : sumInfo.value[key].toLocaleString()
            ),
            h(
              'span',
              sumKeys.some((k) => k === key && key.includes('Amount'))
                ? $toFixed(totalInfo.value[key])
                : totalInfo.value[key].toLocaleString()
            )
          ])
        }
      ])
    )
  }
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="各区域贷款统计(省市)明细">
      <template #right>
        <NButton @click="routerBack"> 返回 </NButton>
      </template>
    </BorderTitleBar>
    <YcTable
      :table-config="tableConfig"
      ref="tableRef"
      :scrollX="3200"
      :summary="summary"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
        >
          <YcSearchItem label="区域">
            <RegionSelect
              v-model="tableConfig.apiParams.region"
              :clearable="false"
            />
          </YcSearchItem>
          <YcSearchItem label="录入日期">
            <NDatePicker
              v-model:value="searchTimeRange"
              clearable
              type="daterange"
              class="w-full"
              separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @update:formatted-value="dateConfirm"
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <NButton @click="HandleExport">导出</NButton>
      </template>
    </YcTable>
    <YcModal
      ref="modalRef"
      title="导出"
      width="900px"
    >
      <exportTable
        :list="tableConfig.columns()"
        @close="closeexport"
        :exportSign="exportSign"
        @Export="handleData"
      ></exportTable>
    </YcModal>
    <company-detail-drawer ref="companyDetailDrawerRef" />
  </YcContent>
</template>

<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-td.n-data-table-td--summary) {
  background-color: #eaf1ff;
  font-weight: 500;
  font-size: 16px;
  color: #3a3f50;
}
</style>
