<script setup lang="ts">
import YcContent from '@/components/common/yc-content.vue'
import { TableUtil } from '@/typings/table'
import { ref } from 'vue'
import { fetchManageApiRiskStatisticQueryPageByBank } from '@/service/api'
import { downloadFileByUrl } from '@/utils/common'

import { useYcTable } from '@/hooks/useYcTable'
import OrgSelect from '@/components/business/orgSelect.vue'

const { tableRef, refreshTable } = useYcTable()
const tableConfig = ref<TableUtil.TableConfig>({
  apiFn: fetchManageApiRiskStatisticQueryPageByBank,
  apiParams: {
    recordDateStart: null,
    recordDateEnd: null,
    org: null
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60
    },
    {
      title: '机构',
      key: 'name',
      width: 140,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计申请笔数',
      key: 'loanNum',
      width: 120,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计申请风补金额（万元）',
      key: 'sumRecordAmount',
      width: 160,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '信用贷款',
      key: 'loanNumCredit',
      align: 'center',
      children: [
        {
          title: '申请笔数',
          key: 'loanNumCredit',
          width: 120,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '申请金额（万元）',
          key: 'sumRecordAmountCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '已审核笔数',
          key: 'loanNumAuditedCredit',
          width: 120,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '已审核风补金额（万元）',
          key: 'sumRecordAmountAuditedCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '已补偿风补笔数',
          key: 'loanNumCompensatedCredit',
          width: 120,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '已补偿风补金额（万元）',
          key: 'sumRecordAmountCompensatedCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '已拨付风补笔数',
          key: 'loanNumPaidCredit',
          width: 120,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '已拨付风补金额（万元）',
          key: 'sumRecordAmountPaidCredit',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    },
    {
      title: '担保贷款',
      key: 'loanNumGuarantee',
      align: 'center',
      children: [
        {
          title: '申请笔数',
          key: 'loanNumGuarantee',
          width: 120,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '申请金额（万元）',
          key: 'sumRecordAmountGuarantee',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '已审核笔数',
          key: 'loanNumAuditedGuarantee',
          width: 120,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '已审核风补金额（万元）',
          key: 'sumRecordAmountAuditedGuarantee',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        },
        // {
        //   title: '已补偿风补笔数',
        //   key: 'loanNumCompensatedGuarantee',
        //   width: 140,
        //   ellipsis: {
        //     tooltip: true
        //   }
        // },
        // {
        //   title: '已补偿风补金额（万元）',
        //   key: 'sumRecordAmountCompensatedGuarantee',
        //   width: 180,
        //   ellipsis: {
        //     tooltip: true
        //   }
        // },
        {
          title: '已拨付风补笔数',
          key: 'loanNumPaidGuarantee',
          width: 120,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '已拨付风补金额（万元）',
          key: 'sumRecordAmountPaidGuarantee',
          width: 160,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    }
  ]
})

const searchTimeRange = ref(null)
const dateConfirm = (val: any) => {
  if (val) {
    tableConfig.value.apiParams.recordDateStart = val[0]
    tableConfig.value.apiParams.recordDateEnd = val[1]
  } else {
    tableConfig.value.apiParams.recordDateStart = ''
    tableConfig.value.apiParams.recordDateEnd = ''
  }
}
//导出
const exportSign = 'riskStatistic/queryPageByBankExport'
const handleData = async (data: any) => {
  let params: any = {
    pageSize: '',
    pageNo: 1,
    suffix: data.suffix,
    exportSign: exportSign,
    exportParamsList: [],
    allParamsList: []
  }
  if (data.pageSize == 'all') {
    params.pageSize = tableRef.value?.pagination.itemCount
  } else {
    params.pageSize = data.totalLimit
  }
  data.selectedColumns.map((item: any) => {
    params.allParamsList.push(item.key)
    if (item.checked) {
      params.exportParamsList.push(item.key)
    }
  })
  let res = await riskStatisticqueryPageByBankExport(params)
  downloadFileByUrl(res.data.accessUrl, res.data.fileName)
  closeexport()
}
const modalRef: any = ref(null)
const HandleExport = () => {
  modalRef.value.open()
}
const closeexport = () => {
  modalRef.value.close()
}
// 查询
const handleSearch = () => {
  refreshTable(true)
}
// 重置
const handleReset = () => {
  tableConfig.value.apiParams = {
    org: null,
    recordDateStart: '',
    recordDateEnd: ''
  }
  searchTimeRange.value = null
  refreshTable()
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="各机构风险补偿统计" />
    <YcTable
      :table-config="tableConfig"
      ref="tableRef"
      :scrollX="3200"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
        >
          <YcSearchItem label="机构">
            <OrgSelect v-model="tableConfig.apiParams.org" />
          </YcSearchItem>
          <YcSearchItem label="贷款日期">
            <NDatePicker
              v-model:value="searchTimeRange"
              clearable
              type="daterange"
              class="w-full"
              separator="至"
              start-placeholder="开始时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              end-placeholder="结束时间"
              @update:formatted-value="dateConfirm"
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <NButton @click="HandleExport">导出</NButton>
      </template>
    </YcTable>
    <YcModal
      ref="modalRef"
      title="导出"
      width="900px"
    >
      <exportTable
        :list="tableConfig.columns()"
        :exportSign="exportSign"
        @close="closeexport"
        @Export="handleData"
      ></exportTable>
    </YcModal>
  </YcContent>
</template>

<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-td.n-data-table-td--summary) {
  background-color: #eaf1ff;
  font-weight: 500;
  font-size: 16px;
  color: #3a3f50;
}
</style>
