<script setup lang="ts">
import YcContent from '@/components/common/yc-content.vue'
import { TableUtil } from '@/typings/table'
import { ref } from 'vue'
import { downloadFileByUrl } from '@/utils/common'

import dayjs from 'dayjs'
import { useYcTable } from '@/hooks/useYcTable'
import { loanStatusModeRecord, loanTypeModeRecord } from '@/constants/app'
import { NSelect } from 'naive-ui'

const { tableRef, refreshTable } = useYcTable()
const searchTimeRange = ref(null)
const dateConfirm = (val: any) => {
  if (val) {
    tableConfig.value.apiParams.recordDateStart = val[0]
    tableConfig.value.apiParams.recordDateEnd = val[1]
  } else {
    tableConfig.value.apiParams.recordDateStart = ''
    tableConfig.value.apiParams.recordDateEnd = ''
  }
}
const tableConfig = ref<TableUtil.TableConfig>({
  apiFn: fetchManageApiLoanStatisticQueryPageByEnt,
  apiParams: {
    entName: '',
    loanBank: '',
    region: null,
    loanType: null,
    recordDateStart: null,
    recordDateEnd: null
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60
    },
    {
      title: '到期状态',
      key: 'status',
      exportKey: 'statusStr',
      ellipsis: {
        tooltip: true
      },
      sorter: (row1: any, row2: any) => {
        const quality1 = row1.status || '' // 如果为 null 或 undefined，使用空字符串
        const quality2 = row2.status || '' // 如果为 null 或 undefined，使用空字符串
        return quality1.localeCompare(quality2, 'zh-Hans')
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => loanStatusModeRecord[rowData.status as UnionKey.loanStatus]
          }
        )
      },
      width: 150
    },
    {
      title: '贷款方式',
      key: 'loanType',
      exportKey: 'loanTypeStr',
      width: 150,
      ellipsis: {
        tooltip: true
      },
      sorter: (row1: any, row2: any) => {
        const quality1 = row1.loanType || '' // 如果为 null 或 undefined，使用空字符串
        const quality2 = row2.loanType || '' // 如果为 null 或 undefined，使用空字符串
        return quality1.localeCompare(quality2, 'zh-Hans')
      },
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => loanTypeModeRecord[rowData.loanType as UnionKey.loanType]
          }
        )
      }
    },
    {
      title: '企业名称',
      key: 'entName',
      width: 150,
      sorter: (row1: any, row2: any) => {
        const quality1 = row1.entName || '' // 如果为 null 或 undefined，使用空字符串
        const quality2 = row2.entName || '' // 如果为 null 或 undefined，使用空字符串
        return quality1.localeCompare(quality2, 'zh-Hans')
      },
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业信用代码',
      key: 'socialCreditCode',
      width: 150,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款金额（万元）',
      width: 150,
      key: 'loanAmount',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '申请金额（万元）',
      width: 150,
      key: 'recordAmount',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '贷款开始日期',
      width: 150,
      key: 'loanBeginDate',
      render: (rowData: any) => {
        return h(
          'span',
          {},
          {
            default: () => (rowData.loanBeginDate ? dayjs(rowData.loanBeginDate).format('YYYY-MM-DD') : '无')
          }
        )
      }
    },
    {
      title: '贷款结束日期',
      width: 150,
      key: 'loanEndDate',
      render: (rowData: any) => {
        return h(
          'span',
          {},
          {
            default: () => (rowData.loanEndDate ? dayjs(rowData.loanEndDate).format('YYYY-MM-DD') : '无')
          }
        )
      }
    },
    {
      title: '贷款期限',
      width: 150,
      key: 'loanTerm'
    },
    {
      title: '录入日期',
      width: 150,
      key: 'recordDate',
      render: (rowData: any) => {
        return h(
          'span',
          {},
          {
            default: () => (rowData.recordDate ? dayjs(rowData.recordDate).format('YYYY-MM-DD') : '无')
          }
        )
      }
    },
    {
      title: '利率(%)',
      width: 150,
      key: 'loanRate'
    },
    {
      title: '贷款发放银行',
      width: 150,
      key: 'loanBankName',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '担保机构',
      width: 150,
      key: 'guaranteeOrg',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '企业负责人',
      width: 150,
      key: 'enterprisePrincipal'
    },
    {
      title: '企业联系人',
      width: 150,
      key: 'enterpriseContact'
    },
    {
      title: '手机号',
      width: 150,
      key: 'mobilePhoneNumber'
    },
    {
      title: '座机号',
      width: 150,
      key: 'landlinePhoneNumber'
    },
    {
      title: '区域',
      width: 150,
      key: 'regionName',
      render: (rowData: any) => {
        return h(
          'span',
          {},
          {
            default: () => regionMaps.value[rowData.region]
          }
        )
      }
    },
    {
      title: '首贷户',
      width: 150,
      key: 'firstTimeBorrower',
      render: (rowData) => {
        return h(
          'span',
          {},
          {
            default: () => (rowData.firstTimeBorrower ? '是' : '否')
          }
        )
      }
    },
    {
      title: '备注',
      width: 150,
      key: 'remark'
    },
    {
      title: '更新时间',
      width: 150,
      key: 'updatedTime',
      render: (rowData: any) => {
        return h(
          'span',
          {},
          {
            default: () => (rowData.updatedTime ? dayjs(rowData.updatedTime).format('YYYY-MM-DD') : '无')
          }
        )
      }
    }
  ]
})
const loanModeOptions = ref([
  {
    label: '信用贷款',
    value: '1'
  },
  {
    label: '担保贷款',
    value: '2'
  }
])
//导出
const exportSign = 'loanStatistic/queryPageByEntExport'
const handleData = async (data: any) => {
  let params: any = {
    pageSize: '',
    pageNo: 1,
    suffix: data.suffix,
    exportSign: exportSign,
    exportParamsList: [],
    allParamsList: []
  }
  if (data.pageSize == 'all') {
    params.pageSize = tableRef.value?.pagination.itemCount
  } else {
    params.pageSize = data.totalLimit
  }
  data.selectedColumns.map((item: any) => {
    params.allParamsList.push(item.key)
    if (item.checked) {
      params.exportParamsList.push(item.key)
    }
  })
  let res = await queryPageByEntExport(params)
  downloadFileByUrl(res.data.accessUrl, res.data.fileName)
  closeexport()
}
const modalRef: any = ref(null)
const HandleExport = () => {
  modalRef.value.open()
}
const closeexport = () => {
  modalRef.value.close()
}
// 查询
const handleSearch = () => {
  refreshTable(true)
}
// 重置
const handleReset = () => {
  tableConfig.value.apiParams = {
    entName: '',
    loanBank: '',
    socialCreditCode: '',
    region: null,
    loanType: null,
    recordDateStart: '',
    recordDateEnd: ''
  }
  searchTimeRange.value = null
  refreshTable()
}
const regionList = ref([])
const regionMaps: any = ref({})
const getregionList = async () => {
  const { data, error } = await fetchManageApiOrganizationQueryPage({
    pageNo: 1,
    pageSize: 1000,
    parentId: '***************'
  })
  if (!error) {
    regionList.value = (data?.list || []).map((item: any) => ({
      label: item.name,
      value: item.id.toString()
    }))
    const object = regionList.value.reduce((acc: any, item: any) => {
      acc[item.value] = item.label
      return acc
    }, {})
    Object.assign(regionMaps.value, { ...object })
  }
}
getregionList()
</script>

<template>
  <YcContent>
    <BorderTitleBar title="各企业贷款明细" />
    <YcTable
      :table-config="tableConfig"
      ref="tableRef"
      :scrollX="3200"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
        >
          <YcSearchItem label="企业名称">
            <NInput
              v-model:value="tableConfig.apiParams.entName"
              placeholder="请输入企业名称"
            />
          </YcSearchItem>
          <YcSearchItem label="贷款发放银行">
            <NInput
              v-model:value="tableConfig.apiParams.loanBank"
              placeholder="请输入贷款发放银行"
            />
          </YcSearchItem>
          <YcSearchItem label="区域">
            <NSelect
              :options="regionList"
              v-model:value="tableConfig.apiParams.region"
              placeholder="请输入区域"
            />
          </YcSearchItem>
          <YcSearchItem label="贷款方式">
            <NSelect
              v-model:value="tableConfig.apiParams.loanType"
              :options="loanModeOptions"
              placeholder="请输入贷款方式"
            />
          </YcSearchItem>
          <YcSearchItem label="录入日期">
            <NDatePicker
              v-model:value="searchTimeRange"
              clearable
              type="daterange"
              class="w-full"
              separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @update:formatted-value="dateConfirm"
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <NButton @click="HandleExport">导出</NButton>
      </template>
    </YcTable>
    <YcModal
      ref="modalRef"
      title="导出"
      width="900px"
    >
      <exportTable
        :list="tableConfig.columns()"
        :exportSign="exportSign"
        @close="closeexport"
        @Export="handleData"
      ></exportTable>
    </YcModal>
  </YcContent>
</template>
