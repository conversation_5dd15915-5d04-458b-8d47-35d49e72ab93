<script setup lang="ts">
import YcContent from '@/components/common/yc-content.vue'
import { TableUtil } from '@/typings/table'
import { ref } from 'vue'
import { downloadFileByUrl } from '@/utils/common'
import { DataTableCreateSummary, NButton } from 'naive-ui'
import { useYcTable } from '@/hooks/useYcTable'
import { $toFixed } from '@/globals'
import OrgSelect from '@/components/business/orgSelect.vue'
import { useRouterPush } from '@/hooks/useRouterPush'
import { fetchManageApiLoanStatisticQueryPageByBankExport } from '@/service/api'

const totalInfo: any = ref({})
// 单页合计
const sumInfo: any = ref({})

const { routerPushByKey } = useRouterPush()
const { tableRef, refreshTable } = useYcTable()
const tableConfig = ref<TableUtil.TableConfig>({
  apiFn: fetchManageApiLoanStatisticQueryPageByBank,
  apiParams: {
    org: null,
    recordDateStart: null,
    recordDateEnd: null
  },
  transformer: (res: any) => {
    const { list = [], rows = [], pageNum = 1, pageNo = 1, pageSize = 10, total = 0 } = res.data.pageInfo || {}
    const bindPageSize = pageSize <= 0 ? 10 : pageSize
    const recordsWithIndex = [...(list ? list : []), ...(rows ? rows : [])].map((item: any, index: number) => {
      return {
        ...item,
        index: (pageNum - 1) * pageSize + index + 1
      }
    })
    totalInfo.value = res.data.totalInfo
    sumInfo.value = res.data.sumInfo
    return {
      data: recordsWithIndex,
      totalData: res.data.sumInfo,
      pageNum,
      pageNo,
      pageSize: bindPageSize,
      total
    }
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60
    },
    {
      title: '机构',
      key: 'name',
      width: 140,
      fixed: 'left',
      ellipsis: {
        tooltip: true
      },
      render: (rowData) => {
        return h(
          NButton,
          {
            type: 'primary',
            text: true,
            onClick: () => {
              routerPushByKey('statistics_bank-loan_detail', {
                query: {
                  org: rowData.id as string,
                  loanType: rowData.loanType as string
                }
              })
            }
          },
          {
            default: () => rowData.name
          }
        )
      }
    },
    {
      title: '户数',
      key: 'enterpriseNum',
      width: 100,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款笔数',
      key: 'loanNum',
      width: 120,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计贷款备案金额 (万元)',
      key: 'sumRecordAmount',
      width: 160,
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '信用贷款',
      key: 'count7',
      align: 'center',
      children: [
        {
          title: '贷款笔数',
          key: 'loanNumCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款金额 (万元)',
          key: 'sumLoanAmountCredit',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款备案余额 (万元)',
          key: 'sumRecordAmountCredit',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷笔数',
          key: 'loanNumFirstCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷金额 (万元)',
          key: 'sumLoanAmountFirstCredit',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷授信金额 (万元)',
          key: 'sumCreditAmountFirstCredit',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷户数',
          key: 'enterpriseNumFirstCredit',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    },
    {
      title: '担保贷款',
      key: 'count8',
      align: 'center',
      children: [
        {
          title: '贷款笔数',
          key: 'loanNumGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款金额 (万元)',
          key: 'sumLoanAmountGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '贷款备案余额 (万元)',
          key: 'sumRecordAmountGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷笔数',
          key: 'loanNumFirstGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷金额 (万元)',
          key: 'sumLoanAmountFirstGuarantee',
          width: 140,
          ellipsis: {
            tooltip: true
          }
        },
        {
          title: '首贷户数',
          key: 'enterpriseNumFirstGuarantee',
          width: 100,
          ellipsis: {
            tooltip: true
          }
        }
      ]
    }
  ]
})
const searchTimeRange = ref(null)
const dateConfirm = (val: any) => {
  if (val) {
    tableConfig.value.apiParams.recordDateStart = val[0]
    tableConfig.value.apiParams.recordDateEnd = val[1]
  } else {
    tableConfig.value.apiParams.recordDateStart = ''
    tableConfig.value.apiParams.recordDateEnd = ''
  }
}
//导出
const exportSign = 'loanStatistic/queryPageByBankExport'
const handleData = async (data: any) => {
  let params: any = {
    org: tableConfig.value.apiParams.org,
    loanType: tableConfig.value.apiParams.loanType,
    pageSize: '',
    pageNo: 1,
    suffix: data.suffix,
    exportSign: exportSign,
    exportParamsList: [],
    allParamsList: []
  }
  if (data.pageSize == 'all') {
    params.pageSize = tableRef.value?.pagination.itemCount
  } else {
    params.pageSize = data.totalLimit
  }
  data.selectedColumns.map((item: any) => {
    params.allParamsList.push(item.key)
    if (item.checked) {
      params.exportParamsList.push(item.key)
    }
  })
  let res = await fetchManageApiLoanStatisticQueryPageByBankExport(params)
  await downloadFileByUrl(res.data.accessUrl, res.data.fileName)
  closeexport()
}
const modalRef: any = ref(null)
const HandleExport = () => {
  modalRef.value.open()
}
const closeexport = () => {
  modalRef.value.close()
}
// 查询
const handleSearch = () => {
  refreshTable(true)
}
// 重置
const handleReset = () => {
  tableConfig.value.apiParams = {
    org: null,
    recordDateStart: '',
    recordDateEnd: ''
  }
  searchTimeRange.value = null
  refreshTable()
}
// 合计行生成函数
const summary: DataTableCreateSummary = (pageData) => {
  // 需要合计的数值字段列表
  const sumKeys = [
    'enterpriseNum',
    'loanNum',
    'sumRecordAmount',
    'loanNumCredit',
    'sumLoanAmountCredit',
    'sumRecordAmountCredit',
    'loanNumFirstCredit',
    'sumLoanAmountFirstCredit',
    'sumCreditAmountFirstCredit',
    'enterpriseNumFirstCredit',
    'loanNumGuarantee',
    'sumLoanAmountGuarantee',
    'sumRecordAmountGuarantee',
    'loanNumFirstGuarantee',
    'sumLoanAmountFirstGuarantee',
    'enterpriseNumFirstGuarantee'
  ]
  return {
    name: {
      value: h('div', { class: 'flex flex-col gap-12px' }, [h('span', '单页合计'), h('span', '合计')])
    },
    ...Object.fromEntries(
      sumKeys.map((key) => [
        key,
        {
          value: h('div', { class: 'flex flex-col gap-12px' }, [
            h(
              'span',
              sumKeys.some((k) => k === key && key.includes('Amount')) ? $toFixed(sumInfo.value[key]) : sumInfo.value[key].toLocaleString()
            ),
            h(
              'span',
              sumKeys.some((k) => k === key && key.includes('Amount'))
                ? $toFixed(totalInfo.value[key])
                : totalInfo.value[key].toLocaleString()
            )
          ])
        }
      ])
    )
  }
}
</script>

<template>
  <YcContent>
    <BorderTitleBar title="各机构贷款统计"></BorderTitleBar>
    <YcTable
      :table-config="tableConfig"
      ref="tableRef"
      :scrollX="3200"
      :summary="summary"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
        >
          <YcSearchItem label="机构名称">
            <org-select v-model="tableConfig.apiParams.org" />
          </YcSearchItem>
          <YcSearchItem label="录入日期">
            <NDatePicker
              v-model:value="searchTimeRange"
              clearable
              type="daterange"
              class="w-full"
              separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @update:formatted-value="dateConfirm"
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <NButton @click="HandleExport">导出</NButton>
      </template>
    </YcTable>
    <YcModal
      ref="modalRef"
      title="导出"
      width="900px"
    >
      <exportTable
        :list="tableConfig.columns()"
        :exportSign="exportSign"
        @close="closeexport"
        @Export="handleData"
      ></exportTable>
    </YcModal>
  </YcContent>
</template>

<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-td.n-data-table-td--summary) {
  background-color: #eaf1ff;
  font-weight: 500;
  font-size: 16px;
  color: #3a3f50;
}
</style>
