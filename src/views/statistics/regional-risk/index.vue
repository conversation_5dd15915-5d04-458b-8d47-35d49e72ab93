<script setup lang="ts">
import YcContent from '@/components/common/yc-content.vue'
import { TableUtil } from '@/typings/table'
import { ref } from 'vue'
import { useYcTable } from '@/hooks/useYcTable'
const { tableRef, refreshTable } = useYcTable()
import { downloadFileByUrl } from '@/utils/common'
import { loanTypeModeOptions } from '@/constants/app'

const tableConfig = ref<TableUtil.TableConfig>({
  apiFn: fetchManageApiRiskStatisticQueryPageByRegion,
  apiParams: {
    region: null,
    recordDateStart: null,
    recordDateEnd: null,
    loanType: null
  },
  columns: () => [
    {
      title: '序号',
      key: 'index',
      width: 60,
      render: (rowData: any, rowIndex: number) => {
        const pagination = tableRef.value?.pagination
        console.log(pagination)
        return h('span', {}, `${(pagination.page - 1) * pagination.pageSize + rowIndex + 1}`)
      }
    },
    {
      title: '区域',
      key: 'name',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计申请笔数',
      key: 'loanNum',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '累计申请风补金额（万元）',
      key: 'sumRecordAmount',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '已审核笔数',
      key: 'loanNumAudited',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '已审核风补金额（万元）',
      key: 'sumRecordAmountAudited',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '已补偿风补笔数',
      key: 'loanNumCompensated',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '已补偿风补金额（万元）',
      key: 'sumRecordAmountCompensated',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '已拨付风补笔数',
      key: 'loanNumPaid',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '已拨付风补金额（万元）',
      key: 'sumRecordAmountPaid',
      ellipsis: {
        tooltip: true
      }
    }
  ]
})
//导出
const exportSign = 'riskStatistic/queryPageByRegionExport'

const handleData = async (data: any) => {
  let params: any = {
    pageSize: '',
    pageNo: 1,
    suffix: data.suffix,
    exportSign: exportSign,
    exportParamsList: [],
    allParamsList: []
  }
  if (data.pageSize == 'all') {
    params.pageSize = tableRef.value?.pagination.itemCount
  } else {
    params.pageSize = data.totalLimit
  }
  data.selectedColumns.map((item: any) => {
    params.allParamsList.push(item.key)
    if (item.checked) {
      params.exportParamsList.push(item.key)
    }
  })
  let res = await riskStatisticqueryPageByRegionExport(params)
  downloadFileByUrl(res.data.accessUrl, res.data.fileName)
  closeexport()
}
const modalRef: any = ref(null)
const HandleExport = () => {
  modalRef.value.open()
}
const closeexport = () => {
  modalRef.value.close()
}
const searchTimeRange = ref(null)
const dateConfirm = (val: any) => {
  if (val) {
    tableConfig.value.apiParams.recordDateStart = val[0]
    tableConfig.value.apiParams.recordDateEnd = val[1]
  } else {
    tableConfig.value.apiParams.recordDateStart = ''
    tableConfig.value.apiParams.recordDateEnd = ''
  }
}
// 查询
const handleSearch = () => {
  refreshTable(true)
}
const loanModeOptions = ref([
  {
    label: '信用贷款',
    value: '1'
  },
  {
    label: '担保贷款',
    value: '2'
  }
])
// 重置
const handleReset = () => {
  tableConfig.value.apiParams = {
    region: null,
    recordDateStart: '',
    loanType: null,
    recordDateEnd: ''
  }
  searchTimeRange.value = null
  refreshTable()
}
const regionList = ref([])
const regionMaps: any = ref({})
const getregionList = async () => {
  const { data, error } = await fetchManageApiOrganizationQueryPage({
    pageNo: 1,
    pageSize: 1000,
    parentId: '659432209310085'
  })
  if (!error) {
    regionList.value = (data?.list || []).map((item: any) => ({
      label: item.name,
      value: item.id.toString()
    }))
    const object = regionList.value.reduce((acc: any, item: any) => {
      acc[item.value] = item.label
      return acc
    }, {})
    Object.assign(regionMaps.value, { ...object })
  }
}
getregionList()
</script>

<template>
  <YcContent>
    <BorderTitleBar title="各区域风险补偿统计" />
    <YcTable
      :table-config="tableConfig"
      ref="tableRef"
    >
      <template #header>
        <YcSearchContent
          @search="handleSearch"
          @reset="handleReset"
        >
          <YcSearchItem label="区域">
            <NSelect
              :options="regionList"
              v-model:value="tableConfig.apiParams.region"
              placeholder="请输入区域"
            />
          </YcSearchItem>
          <!-- <YcSearchItem label="贷款方式">
            <NSelect
              :options="loanModeOptions"
              v-model:value="tableConfig.apiParams.loanType"
              placeholder="请输入贷款方式"
            />
          </YcSearchItem> -->
          <YcSearchItem label="贷款日期">
            <NDatePicker
              v-model:value="searchTimeRange"
              clearable
              type="daterange"
              class="w-full"
              separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @update:formatted-value="dateConfirm"
            />
          </YcSearchItem>
        </YcSearchContent>
      </template>
      <template #header-sub>
        <NButton @click="HandleExport">导出</NButton>
      </template>
    </YcTable>
    <YcModal
      ref="modalRef"
      title="导出"
      width="900px"
    >
      <exportTable
        :list="tableConfig.columns()"
        :exportSign="exportSign"
        @close="closeexport"
        @Export="handleData"
      ></exportTable>
    </YcModal>
  </YcContent>
</template>
