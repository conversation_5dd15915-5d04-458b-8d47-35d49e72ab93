<script setup lang="ts">
import YcDrawer from '@/components/common/yc-drawer.vue'
import YcContent from '@/components/common/yc-content.vue'
import { TableUtil } from '@/typings/table'
import { ref } from 'vue'
import { downloadFileByUrl } from '@/utils/common'
import { DataTableCreateSummary } from 'naive-ui'
import { useYcTable } from '@/hooks/useYcTable'
import { $toFixed } from '@/globals'
import { fetchManageApiLoanStatisticQueryPageByRegionOrgEnt, fetchManageApiLoanStatisticQueryPageByRegionOrgEntExport } from '@/service/api'
import { Expose } from '@/typings/expose'
import OrgSelect from '@/components/business/orgSelect.vue'

// 全局合计
const totalInfo: any = ref({})
// 单页合计
const sumInfo: any = ref({})

const route = useRoute()

const { tableRef, refreshTable } = useYcTable()

const ycDrawerRef = ref<Expose.YCDrawer>()
const loanType = ref(0)
const drawerTitle = ref('')
// 打开弹窗
const open = async (params: any) => {
  drawerTitle.value = params.title as string
  loanType.value = Number(params.loanType)
  ycDrawerRef.value?.open()
  tableConfig.value.apiParams.region = params.region.toString()
  tableConfig.value.apiParams.org = params.orgId.toString()
  tableConfig.value.apiParams.loanType = loanType.value
  await nextTick()
  handleSearch()
}

// 关闭弹窗
const close = () => {
  ycDrawerRef.value?.close()
}

const tableConfig = ref<TableUtil.TableConfig>({
  immediate: false,
  apiFn: fetchManageApiLoanStatisticQueryPageByRegionOrgEnt,
  apiParams: {
    region: null,
    org: null,
    loanType: null,
    recordDateStart: null,
    recordDateEnd: null
  },
  transformer: (res: any) => {
    const { list = [], rows = [], pageNum = 1, pageNo = 1, pageSize = 10, total = 0 } = res.data.pageInfo || {}
    const bindPageSize = pageSize <= 0 ? 10 : pageSize
    const recordsWithIndex = [...(list ? list : []), ...(rows ? rows : [])].map((item: any, index: number) => {
      return {
        ...item,
        index: (pageNum - 1) * pageSize + index + 1
      }
    })
    totalInfo.value = res.data.totalInfo
    sumInfo.value = res.data.sumInfo
    return {
      data: recordsWithIndex,
      totalData: res.data.sumInfo,
      pageNum,
      pageNo,
      pageSize: bindPageSize,
      total
    }
  },
  columns: () =>
    [
      {
        title: '序号',
        key: 'index',
        width: 60
      },
      {
        title: '企业名称',
        key: 'name',
        width: 160,
        fixed: 'left',
        ellipsis: {
          tooltip: true
        }
      },
      {
        title: '户数',
        key: 'enterpriseNum',
        width: 80,
        ellipsis: {
          tooltip: true
        }
      },
      {
        title: '累计贷款笔数',
        key: 'loanNum',
        width: 120,
        ellipsis: {
          tooltip: true
        }
      },
      {
        title: '累计授信金额 (万元)',
        key: 'sumCreditAmount',
        width: 160,
        ellipsis: {
          tooltip: true
        }
      },
      {
        title: '累计贷款备案金额 (万元)',
        key: 'sumRecordAmount',
        width: 180,
        ellipsis: {
          tooltip: true
        }
      },
      {
        title: loanType.value === 1 ? '信用贷款' : '担保贷款',
        key: 'count8',
        align: 'center',
        children: [
          {
            title: '贷款笔数',
            key: 'loanNumCredit',
            width: 100,
            ellipsis: {
              tooltip: true
            }
          },
          {
            title: '贷款金额 (万元)',
            key: 'sumLoanAmountCredit',
            width: 140,
            ellipsis: {
              tooltip: true
            }
          },
          {
            title: '贷款备案金额 (万元)',
            key: 'sumRecordAmountCredit',
            width: 160,
            ellipsis: {
              tooltip: true
            }
          },
          {
            title: '首贷笔数',
            key: 'loanNumFirstCredit',
            width: 100,
            ellipsis: {
              tooltip: true
            }
          },
          {
            title: '首贷金额 (万元)',
            key: 'sumLoanAmountFirstCredit',
            width: 140,
            ellipsis: {
              tooltip: true
            }
          },
          {
            title: '首贷户数',
            key: 'enterpriseNumFirstCredit',
            width: 100,
            ellipsis: {
              tooltip: true
            }
          }
        ]
      }
    ].filter(Boolean) as TableUtil.TableColumn[]
})
const searchTimeRange = ref(null)
const dateConfirm = (val: any) => {
  if (val) {
    tableConfig.value.apiParams.recordDateStart = val[0]
    tableConfig.value.apiParams.recordDateEnd = val[1]
  } else {
    tableConfig.value.apiParams.recordDateStart = ''
    tableConfig.value.apiParams.recordDateEnd = ''
  }
}

// 查询
const handleSearch = () => {
  refreshTable(true)
}
// 重置
const handleReset = () => {
  tableConfig.value.apiParams = Object.assign({}, tableConfig.value.apiParams, {
    recordDateStart: null,
    recordDateEnd: null
  })
  searchTimeRange.value = null
  refreshTable(true)
}
const modalRef: any = ref(null)
const HandleExport = () => {
  modalRef.value.open()
}
//导出
const exportSign = 'loanStatistic/queryPageByRegionOrgEntExport'
const handleData = async (data: any) => {
  let params: any = {
    region: tableConfig.value.apiParams.region,
    org: tableConfig.value.apiParams.org,
    loanType: tableConfig.value.apiParams.loanType,
    pageSize: 1000,
    pageNo: 1,
    suffix: data.suffix,
    exportSign: exportSign,
    exportParamsList: [],
    allParamsList: []
  }
  if (data.pageSize == 'all') {
    params.pageSize = tableRef.value?.pagination.itemCount
  } else {
    params.pageSize = data.totalLimit
  }
  data.selectedColumns.map((item: any) => {
    params.allParamsList.push(item.key)
    if (item.checked) {
      params.exportParamsList.push(item.key)
    }
  })
  let res = await fetchManageApiLoanStatisticQueryPageByRegionOrgEntExport(params)
  await downloadFileByUrl(res.data.accessUrl, res.data.fileName)
  closeexport()
}
const closeexport = () => {
  modalRef.value.close()
}

// 合计行生成函数
const summary: DataTableCreateSummary = (pageData) => {
  // 需要合计的数值字段列表
  const sumKeys = [
    'enterpriseNum',
    'loanNum',
    'sumCreditAmount',
    'sumRecordAmount',
    'loanNumCredit',
    'sumLoanAmountCredit',
    'sumRecordAmountCredit',
    'loanNumFirstCredit',
    'sumLoanAmountFirstCredit',
    'enterpriseNumFirstCredit'
  ]
  return {
    name: {
      value: h('div', { class: 'flex flex-col gap-12px' }, [h('span', '单页合计'), h('span', '合计')])
    },
    ...Object.fromEntries(
      sumKeys.map((key) => [
        key,
        {
          value: h('div', { class: 'flex flex-col gap-12px' }, [
            h(
              'span',
              sumKeys.some((k) => k === key && key.includes('Amount')) ? $toFixed(sumInfo.value[key]) : sumInfo.value[key].toLocaleString()
            ),
            h(
              'span',
              sumKeys.some((k) => k === key && key.includes('Amount'))
                ? $toFixed(totalInfo.value[key])
                : totalInfo.value[key].toLocaleString()
            )
          ])
        }
      ])
    )
  }
}

defineExpose({
  open
})
</script>

<template>
  <YcDrawer
    width="88%"
    ref="ycDrawerRef"
    :title="drawerTitle"
  >
    <YcContent>
      <YcTable
        :table-config="tableConfig"
        ref="tableRef"
        :summary="summary"
      >
        <template #header>
          <YcSearchContent
            @search="handleSearch"
            @reset="handleReset"
          >
            <YcSearchItem label="区域">
              <RegionSelect
                v-model="tableConfig.apiParams.region"
                :clearable="false"
              />
            </YcSearchItem>
            <YcSearchItem label="机构名称">
              <OrgSelect
                v-model="tableConfig.apiParams.org"
                :clearable="false"
              />
            </YcSearchItem>
            <YcSearchItem label="录入日期">
              <NDatePicker
                v-model:value="searchTimeRange"
                clearable
                type="daterange"
                class="w-full"
                separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @update:formatted-value="dateConfirm"
              />
            </YcSearchItem>
          </YcSearchContent>
        </template>
        <template #header-sub>
          <NButton @click="HandleExport">导出</NButton>
        </template>
      </YcTable>
      <YcModal
        ref="modalRef"
        title="导出"
        width="900px"
      >
        <exportTable
          :list="tableConfig.columns()"
          @close="closeexport"
          :exportSign="exportSign"
          @Export="handleData"
        ></exportTable>
      </YcModal>
    </YcContent>
  </YcDrawer>
</template>

<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-td.n-data-table-td--summary) {
  background-color: #eaf1ff;
  font-weight: 500;
  font-size: 16px;
  color: #3a3f50;
}
</style>
