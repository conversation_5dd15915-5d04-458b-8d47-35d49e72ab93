<script setup lang="ts">
import { useLoading } from '~/packages/hooks'

const props = withDefaults(
  defineProps<{
    clearable?: boolean
    multiple?: boolean
  }>(),
  {
    clearable: true,
    multiple: false
  }
)

const _emits = defineEmits(['update:value', 'update:item'])

const modelValue = defineModel()
const modelLabel = defineModel('label')

const regionList = ref<any[]>([])
const { loading, startLoading, endLoading } = useLoading()

const getRegion = async () => {
  try {
    startLoading()
    const { data, error } = await fetchManageApiOrganizationQueryPage({
      pageNo: 1,
      pageSize: 1000,
      parentId: '659432209310085'
    })
    if (!error) {
      regionList.value = (data?.list || []).map((item: any) => ({
        label: item.name,
        value: item.id.toString()
      }))
    }
  } finally {
    endLoading()
  }
}
getRegion()

const handleUpdateValue = (value: any) => {
  if (!value) {
    modelLabel.value = null
    _emits('update:item', null)
    _emits('update:value', null)
    return
  }
  const item = regionList.value.find((item) => item.value === value)
  modelLabel.value = item.label || null
  _emits('update:item', item.label)
  _emits('update:value', {
    regionSelectLabel: item.label,
    regionSelectValue: value
  })
}
</script>

<template>
  <NSelect
    v-model:value="modelValue"
    :options="regionList"
    :clearable="props.clearable"
    :multiple="props.multiple"
    class="w-full"
    :loading="loading"
    max-tag-count="responsive"
    @update:value="handleUpdateValue"
  ></NSelect>
</template>

<style scoped></style>
