<script setup lang="ts">
import { useLoading } from '~/packages/hooks'

const props = withDefaults(
  defineProps<{
    clearable?: boolean
    multiple?: boolean
    filterable?: boolean
  }>(),
  {
    clearable: true,
    multiple: false,
    filterable: true
  }
)

const _emits = defineEmits(['update:value', 'update:item'])

const modelValue = defineModel()
const modelLabel = defineModel('label')

const orgList = ref<any[]>([])
const { loading, startLoading, endLoading } = useLoading()

const getOrg = async () => {
  try {
    startLoading()
    const { data } = await fetchManageApiOrgQueryPage({
      pageNo: 1,
      pageSize: 1000
    })
    if (data.list) {
      data.list.map((t: any) => {
        t.label = t.orgName
        t.value = t.id.toString()
        return t
      })
    }
    orgList.value = data.list
  } finally {
    endLoading()
  }
}
getOrg()

const handleUpdateValue = (value: any) => {
  if (!value) {
    modelLabel.value = null
    _emits('update:item', null)
    _emits('update:value', null)
    return
  }
  const item = orgList.value.find((item) => item.value === value)
  modelLabel.value = item.label || null
  _emits('update:item', item.label)
  _emits('update:value', {
    orgSelectLabel: item.label,
    orgSelectValue: value
  })
}
</script>

<template>
  <NSelect
    v-model:value="modelValue"
    :options="orgList"
    :clearable="props.clearable"
    :multiple="props.multiple"
    :filterable="props.filterable"
    class="w-full"
    :loading="loading"
    max-tag-count="responsive"
    @update:value="handleUpdateValue"
  ></NSelect>
</template>

<style scoped></style>
