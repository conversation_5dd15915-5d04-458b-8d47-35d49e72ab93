<template>
  <div class="">
    <!-- <div class="flex items-center justify-between mb-8px">
      <NTabs v-model:value="currentTab" type="line">
        <NTab name="0">审核记录</NTab>
        <NTab name="1">流程图</NTab>
      </NTabs>
    </div> -->
    <n-data-table
      :columns="columns"
      :data="auditData"
      :single-line="false"
      v-if="currentTab == '0'"
    />
    <img
      :src="flowImgData"
      alt=""
      class=""
      v-if="currentTab == '1'"
    />
  </div>
</template>

<script setup lang="ts">
//@ts-ignore
import { auditStatusModeRecord } from '@/constants/app'
import { useRoute } from 'vue-router' // 导入 useRoute
import dayjs from 'dayjs'
import { h, ref } from 'vue'
import { fetchManageApiRiskQueryHisTask } from '@/service/api'
import { NUpload, NUploadFileList } from 'naive-ui'

const route: any = useRoute() // 获取路由对象
const props = defineProps(['instanceId'])
const currentTab = ref('0')
const columns: any = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: '80',
    render: (row: any, index: number) => {
      return h('span', index + 1) // 显示序号
    }
  },
  {
    title: '节点名称',
    align: 'center',
    key: 'nodeName'
  },
  {
    title: '电子签名',
    align: 'center',
    key: 'approver'
  },
  {
    title: '签名日期',
    align: 'center',
    key: 'updateTime',
    render: (row: any) => {
      return dayjs(row.updateTime).format('YYYY-MM-DD')
    }
  },
  // {
  //   title: '审核结论',
  //   align: 'center',
  //   key: 'skipType',
  //   render: (row: any, index: number) => {
  //     return h('span', auditStatusModeRecord[row.skipType as UnionKey.auditStatusTypeForm]) // 显示序号
  //   }
  // },
  {
    title: '备注',
    align: 'center',
    key: 'message',
    render: (row: any) => {
      if (row.file) {
        const fileList = [Object.assign({}, row.file, { id: 111, name: row.file.fileName, url: row.file.fileUrl, status: 'finished' })]
        return h(
          NUpload,
          {
            action: '',
            abstract: true,
            showRemoveButton: false,
            defaultFileList: fileList
          },
          () => {
            return h(NUploadFileList)
          }
        )
      } else {
        return row.message
      }
    }
  }
]
const auditData = ref([])
const flowImgData = ref('')
const getData = async () => {
  const { data = [] } = await fetchManageApiRiskQueryHisTask({ id: props.instanceId })
  auditData.value = data.reverse()
}

watchEffect(() => {
  if (props.instanceId) {
    getData()
  }
})
</script>

<style scoped>
.file-name {
  margin-right: 10px;
  color: #333;
  flex-grow: 1;
}
</style>
