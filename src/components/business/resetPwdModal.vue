<script setup lang="ts">
import type { Ref } from 'vue'
import { reactive, ref, unref } from 'vue'
import type { FormItemRule } from 'naive-ui'
import { useFormRules, useNaiveForm } from '@/hooks/useForm'
import { useLoading } from '~/packages/hooks'
import { useAuthStore } from '@/store/modules/auth'
import { fetchUpdatePwd } from '@/service/api'

const modalRef: Ref<Expose.YCModal | null> = ref(null)

const authStore = useAuthStore()

// 表单数据
const initialData = {
  userName: unref(authStore.userInfo)?.name,
  phone: unref(authStore.userInfo)?.phone,
  userId: unref(authStore.userInfo)?.userId,
  oldPwd: null,
  pwd: null,
  confirmPwd: null
}

const { formRef, validate, formData, resetFormData } = useNaiveForm(initialData)
const { formRules } = useFormRules()
const { loading, startLoading, endLoading } = useLoading()

// 表单校验规则
function validatePasswordSame(rule: FormItemRule, value: string | null): boolean {
  return value === formData.value.pwd
}

const rules = reactive({
  oldPwd: formRules.password,
  pwd: formRules.password,
  confirmPwd: [
    ...formRules.password,
    {
      validator: validatePasswordSame,
      message: '两次输入的密码不一致',
      trigger: ['blur', 'password-input']
    }
  ]
})

const handleCancel = () => {
  modalRef.value?.close()
}

const open = () => {
  modalRef.value?.open()
  resetFormData()
}

// 确认
const handleSubmit = async () => {
  await validate()
  try {
    startLoading()
    const res = await fetchUpdatePwd(formData.value.oldPwd, formData.value.pwd, formData.value.userId)
    if (res.error) return
    handleCancel()
    window?.$message?.success('密码修改成功，请重新登录!')
    setTimeout(() => {
      authStore.logout()
    }, 1000)
  } finally {
    endLoading()
  }
}

defineExpose({
  open
})
</script>

<template>
  <YcModal
    ref="modalRef"
    title="修改密码"
    width="440px"
    content-css="!pb-0"
  >
    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="80"
      label-align="left"
      require-mark-placement="left"
    >
      <NFormItem label="用户名">
        <div>{{ formData.userName }}</div>
      </NFormItem>
      <NFormItem label="手机号">
        <div>{{ formData.phone }}</div>
      </NFormItem>
      <NFormItem
        path="oldPwd"
        label="原密码"
      >
        <NInput
          v-model:value="formData.oldPwd"
          placeholder="请输入原密码"
          type="password"
          show-password-on="click"
          :input-props="{ autocomplete: 'on' }"
        />
      </NFormItem>
      <NFormItem
        path="pwd"
        label="新密码"
      >
        <NInput
          v-model:value="formData.pwd"
          placeholder="请输入新密码"
          type="password"
          show-password-on="click"
          :input-props="{ autocomplete: 'on' }"
        />
      </NFormItem>
      <NFormItem
        path="confirmPwd"
        label="确认密码"
      >
        <NInput
          v-model:value="formData.confirmPwd"
          placeholder="请再次输入密码"
          type="password"
          show-password-on="click"
          :input-props="{ autocomplete: 'on' }"
        />
      </NFormItem>
    </NForm>
    <template #footer>
      <NButton @click="handleCancel">返回</NButton>
      <NButton
        type="primary"
        :loading="loading"
        @click="handleSubmit"
        >确认</NButton
      >
    </template>
  </YcModal>
</template>

<style scoped></style>
