<script setup lang="ts">
import { useLoading } from '~/packages/hooks'

const props = withDefaults(
  defineProps<{
    clearable?: boolean
    filterable?: boolean
    placeholder?: string
  }>(),
  {
    clearable: true,
    filterable: true,
    placeholder: '请选择'
  }
)

const _emits = defineEmits(['update:value', 'update:item'])

const modelValue = defineModel()
const modelLabel = defineModel('label')

const bankTreeData = ref<any[]>([])
const { loading, startLoading, endLoading } = useLoading()

const getBankTree = async () => {
  try {
    startLoading()
    const { data } = await fetchManageApiOrganizationGetOrganizationTree({
      id: ***************
    })
    bankTreeData.value = data
  } finally {
    endLoading()
  }
}
getBankTree()

// 更新
const handleUpdateValue = (value: number) => {
  const findTreeItemByValue = (value: number, treeData: any[]) => {
    for (const item of treeData) {
      if (item.id === value) {
        return item
      }
      if (item.children) {
        const foundItem = findTreeItemByValue(value, item.children)
        if (foundItem) {
          return foundItem
        }
      }
    }
    return null
  }
  const item = findTreeItemByValue(value, bankTreeData.value)
  modelLabel.value = item.name || null
}
</script>

<template>
  <NTreeSelect
    v-model:value="modelValue"
    :options="bankTreeData"
    :clearable="props.clearable"
    class="w-full"
    :loading="loading"
    label-field="name"
    key-field="id"
    children-field="children"
    :placeholder="props.placeholder"
    @update:value="handleUpdateValue"
  />
</template>

<style scoped></style>
