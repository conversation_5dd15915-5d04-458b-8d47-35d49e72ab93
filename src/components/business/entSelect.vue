<script setup lang="ts">
import { useLoading } from '~/packages/hooks'

const props = withDefaults(
  defineProps<{
    clearable?: boolean
    multiple?: boolean
    filterable?: boolean
  }>(),
  {
    clearable: true,
    multiple: false,
    filterable: true
  }
)

const _emits = defineEmits(['update:value', 'update:item'])

const modelValue = defineModel()
const modelLabel = defineModel('label')

const entList = ref<any[]>([])
const { loading, startLoading, endLoading } = useLoading()

const fetchParams = ref({
  pageNo: 1,
  pageSize: 50,
  entName: ''
})
const total = ref(0)
const getEntList = async () => {
  try {
    startLoading()
    const { data } = await fetchManageApiSciEntQueryPage(fetchParams.value)
    if (data.rows) {
      total.value = data.total
      return data.rows.map((t: any) => {
        return {
          ...t,
          label: t.entName,
          value: t.id.toString()
        }
      })
    }
  } finally {
    endLoading()
  }
}

const init = async () => {
  entList.value = await getEntList()
}
init()

// 滚动
const handleScroll = async (e: Event) => {
  const currentTarget = e.currentTarget as HTMLElement
  if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight && entList.value.length < total.value) {
    fetchParams.value.pageNo++
    entList.value = [...entList.value, ...(await getEntList())]
  }
}

const handleUpdateValue = (value: any) => {
  if (!value) {
    modelLabel.value = null
    _emits('update:item', null)
    _emits('update:value', null)
    return
  }
  const item = entList.value.find((item) => item.value === value)
  modelLabel.value = item.label || null
  _emits('update:item', item.label)
  _emits('update:value', item)
}

const handleSearch = async (value: string) => {
  fetchParams.value.entName = value
  fetchParams.value.pageNo = 1
  entList.value = await getEntList()
}
</script>

<template>
  <NSelect
    v-model:value="modelValue"
    :options="entList"
    :clearable="props.clearable"
    :multiple="props.multiple"
    :filterable="props.filterable"
    class="w-full"
    :loading="loading"
    max-tag-count="responsive"
    @update:value="handleUpdateValue"
    :reset-menu-on-options-change="false"
    @scroll="handleScroll"
    remote
    @search="handleSearch"
  ></NSelect>
</template>

<style scoped></style>
