<script setup lang="ts">
const props = defineProps<{
  loading?: boolean
}>()
</script>

<template>
  <NSpin
    :show="props.loading"
    class="w-full h-full"
  >
    <div class="w-full relative">
      <div
        class="form-page-content"
        :class="{ 'pb-64px': $slots.footer }"
      >
        <slot></slot>
      </div>
      <div
        class="form-page-footer"
        v-if="$slots.footer"
      >
        <slot name="footer"></slot>
      </div>
    </div>
  </NSpin>
</template>

<style scoped lang="scss">
.form-page-content {
  display: flex;
  flex-direction: column;
  gap: 12px;

  :deep(.card) {
    padding: 10px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 14px;
  }

  :deep(.n-form) {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

.form-page-footer {
  width: 100%;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 56px;
  box-shadow: 0 -5px 10px -1px #0000000d;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 20px;
  gap: 16px;
}
</style>
