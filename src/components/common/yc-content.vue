<script setup lang="ts">
import { useAttrs, ref } from "vue";
const contentRef: any = ref<HTMLElement | null>(null);
const backtop = () => {
  if (contentRef.value) {
    contentRef.value.scrollTo({ top: 0, behavior: "smooth" });
  } else {
    console.error("contentRef is null or undefined");
  }
};
defineExpose({ backtop }); // 暴露 ref
const attrs = useAttrs();
</script>

<template>
  <div class="h-full w-full">
    <div class="yc-content" :class="attrs?.class" ref="contentRef">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
// shadow-sm
.yc-content {
  @apply h-full w-full flex flex-col gap-12px overflow-y-auto border border-#EFEFF5 rounded-8px border-solid p-16px bg-#fff;
}
</style>
