<script lang="ts">
import DocumentEditor from '@ckeditor/ckeditor5-build-decoupled-document';
import '@ckeditor/ckeditor5-build-decoupled-document/build/translations/zh-cn';
import CKEditor from '@ckeditor/ckeditor5-vue';
import { ImageUploadAdapter } from '@/utils/editorUploadAdapter';

export default {
  components: {
    // Use the <ckeditor> component in this view.
    Ckeditor: CKEditor.component
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    height: {
      type: String,
      default: 'auto'
    },
    rootClass: {
      type: String,
      default: ''
    }
  },
  emits: ['blur', 'update:modelValue'],
  data() {
    return {
      editor: DocumentEditor,
      editorConfig: {
        placeholder: this.placeholder,
        toolbar: {
          items: [
            'heading',
            '|',
            'fontfamily',
            'fontsize',
            'fontColor',
            'fontBackgroundColor',
            '|',
            'bold',
            'italic',
            'strikethrough',
            'underline',
            'alignment',
            '|',
            // 'link',
            'insertImage',
            // 'mediaEmbed',
            'blockQuote',
            '|',
            'bulletedList',
            'numberedList',
            'outdent',
            'indent',
            'insertTable'
          ],
          shouldNotGroupWhenFull: true
        },
        image: {
          resizeUnit: '%',
          resizeOptions: [
            {
              name: 'resizeImage:original',
              value: null
            },
            {
              name: 'resizeImage:50',
              value: '50'
            },
            {
              name: 'resizeImage:75',
              value: '75'
            },
            {
              name: 'resizeImage:100',
              value: '100'
            }
          ],
          toolbar: [
            'imageStyle:alignLeft',
            'imageStyle:alignRight',
            '|',
            'imageStyle:alignBlockLeft',
            'imageStyle:alignCenter',
            'imageStyle:alignBlockRight',
            '|',
            'resizeImage',
            '|',
            'toggleImageCaption'
          ],
          insert: {
            integrations: ['upload'],
            type: 'auto'
          }
        },
        fontFamily: {
          supportAllValues: true,
          options: [
            'default',
            '微软雅黑, Microsoft YaHei',
            '宋体, SimSun',
            '仿宋, FangSong',
            '楷体, KaiTi',
            '隶书, LiSu',
            '幼圆, YouYuan',
            '华文细黑, STXihei',
            '华文楷体, STKaiti',
            'Arial, Helvetica, sans-serif',
            'Courier New, Courier, monospace',
            'Georgia, serif',
            'Lucida Sans Unicode, Lucida Grande, sans-serif',
            'Tahoma, Geneva, sans-serif',
            'Times New Roman, Times, serif',
            'Trebuchet MS, Helvetica, sans-serif',
            'Verdana, Geneva, sans-serif'
          ]
        },
        fontSize: {
          supportAllValues: true,
          options: ['default', 12, 14, 16, 18, 20, 22, 26, 28, 36, 48]
        },
        language: 'zh-cn'
      }
    };
  },
  computed: {
    editorData: {
      get() {
        return this.modelValue || '';
      },
      set(val: string) {
        this.$emit('update:modelValue', val);
      }
    }
  },
  methods: {
    onEditorReady(editor) {
      // 在可编辑区域之前插入工具栏。
      editor.ui
        .getEditableElement()
        .parentElement.insertBefore(editor.ui.view.toolbar.element, editor.ui.getEditableElement());

      console.log('editor', editor);
      // 图片上传适配器。
      editor.plugins.get('FileRepository').createUploadAdapter = loader => {
        return new ImageUploadAdapter(loader);
      };
    },
    onEditorFocus() {},
    onEditorBlur() {
      this.$emit('blur', this.editorData);
    },
    onEditorInput() {},
    onEditorDestroy() {}
  }
};
</script>

<template>
  <div class="yc-editor h-full w-full" :class="rootClass">
    <Ckeditor
      v-model="editorData"
      :editor="editor"
      :config="editorConfig"
      class="editor-wrapper"
      :style="{ height: height }"
      :disabled="disabled"
      @ready="onEditorReady"
      @focus="onEditorFocus"
      @blur="onEditorBlur"
      @input="onEditorInput"
      @destroy="onEditorDestroy"
    />
  </div>
</template>

<style>
.ck-powered-by-balloon {
  display: none !important;
}
</style>

<style scoped lang="scss">
.yc-editor {
  .editor-wrapper {
    height: calc(100% - 40px);
    border: 1px solid var(--ck-color-toolbar-border);
    border-radius: var(--ck-border-radius);
    position: relative;
    top: -1px;
  }
}
</style>
