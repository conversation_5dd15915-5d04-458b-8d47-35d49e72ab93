<template>
  <!-- 头部标题 -->

  <!-- 主体内容 -->
  <div class="content-wrapper">
    <!-- 左侧配置区域 -->
    <div class="config-section">
      <!-- 文件格式选择 -->
      <n-form-item
        label="文件格式："
        :label-placement="'left'"
      >
        <n-select
          v-model:value="exportData.suffix"
          :options="formatOptions"
          placeholder="请选择内容"
          style="width: 200px"
        />
      </n-form-item>

      <!-- 配置选项 -->
      <n-form-item
        label="全部条数："
        :label-placement="'left'"
      >
        <n-select
          v-model:value="exportData.pageSize"
          :options="TotalOptions"
          placeholder="请选择"
          style="width: 200px"
        />
        <n-input-number
          v-if="exportData.pageSize == 'other'"
          v-model:value="totalLimit"
          :min="1"
          :max="10000"
          :show-button="false"
          style="width: 200px"
        />
      </n-form-item>
      <!-- <n-form-item
        label="全部条数："
        :label-placement="'left'"
      >
        <n-switch v-model:value="totalLimit" />
        <span class="text-[#636878] text-[12px] pl-10px"> 导出列为数值类型，仅支持数据映射 </span>
      </n-form-item> -->
      <!-- 显示规则 -->

      <!-- 导出列选择 -->
      <n-form-item
        label="导出列："
        :label-placement="'left'"
      >
        <div class="flex gap-10px flex-wrap">
          <div
            class="btnbox"
            @click="changeBtn(item)"
            :class="item.checked ? 'btnboxactive' : ''"
            v-for="item in columnOptions"
          >
            {{ item.title }}
          </div>
        </div>
      </n-form-item>
    </div>
  </div>
  <n-h4 class="history-title pl-20px">导出历史</n-h4>
  <div class="history-section">
    <YcTable
      ref="tableRef"
      :showSubHeader="false"
      :table-config="tableConfig"
      :bordered="true"
      :striped="true"
    ></YcTable>
    <!-- <n-data-table
      :columns="historyColumns"
      :data="exportHistory"
      :pagination="pagination"
      class="history-table"
    /> -->
  </div>
  <!-- 底部操作按钮 -->
  <n-divider />
  <div class="footer-actions">
    <n-space>
      <n-button
        class=""
        @click="handleClose"
        >关闭</n-button
      >
      <n-button
        type="primary"
        @click="handleExport"
        >导出</n-button
      >
    </n-space>
  </div>
</template>

<script setup>
import { ref, h, withDirectives } from 'vue'
import {
  NIcon,
  NCard,
  NFormItem,
  NSelect,
  NInputNumber,
  NAlert,
  NCheckboxGroup,
  NCheckbox,
  NSpace,
  NButton,
  NH3,
  NDataTable,
  NPopconfirm
} from 'naive-ui'
import { downloadFileByUrl } from '@/utils/common'
import { addOrUpdate } from '@/service/api'
import { useYcTable } from '@/hooks/useYcTable'
const { tableRef, refreshTable } = useYcTable()
import { DownloadOutline, TrashOutline } from '@vicons/ionicons5'
const props = defineProps({
  list: Array,
  exportSign: {
    // 新增exportSign prop
    type: String,
    required: true
  }
})
// 文件格式选项
const formatOptions = [
  { label: 'xlsx', value: '.xlsx' }
  // { label: 'csv', value: '.csv' }
]
const TotalFormat = ref('all')
const TotalOptions = [
  { label: '全部', value: 'all' },
  { label: '自定义', value: 'other' }
]
const tableConfig = ref({
  apiFn: exportLog,
  apiParams: {
    exportSign: props.exportSign
  },
  columns: () => [
    {
      title: '文件名',
      key: 'fileName',
      // fixed: 'left',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '日期',
      key: 'createdTime',
      // fixed: 'left',
      ellipsis: {
        tooltip: true
      }
    },
    {
      title: '状态',
      key: 'status',
      // fixed: 'left',
      render(row) {
        console.log('🚀 ~ render ~ row:', row)
        return h(
          'div',
          {
            class: 'status-container',
            style: 'display: flex; align-items: center; justify-content: space-between;'
          },
          [
            h('span', { class: 'status-text' }, '完成'),
            h('div', { class: `icon-group`, style: row?.showIcon ? 'opacity:1;' : 'display:none' }, [
              h(
                NIcon,
                {
                  class: 'action-icon download cursor-pointer mr-5px',
                  size: 18,
                  color: '#1890ff',
                  onClick: (e) => {
                    e.stopPropagation()
                    downloadFileByUrl(row.accessUrl, row.fileName)
                  }
                },
                h(DownloadOutline)
              ),
              h(
                NPopconfirm,
                {
                  positiveText: '确认',
                  negativeText: '取消',
                  onPositiveClick: () => handleDelete(row.id)
                },
                {
                  trigger: () =>
                    h(
                      NButton,
                      {
                        type: 'error',
                        text: true,
                        style: { padding: '0 5px' },
                        focusable: false
                      },
                      {
                        default: () => h(NIcon, { component: TrashOutline, size: 18 })
                      }
                    ),
                  default: () => '确认要删除这条记录吗？'
                }
              )
            ])
          ]
        )
      }
    }
  ]
})
// 导出列选项 把children属性的值放到一个数组中
const exportData = ref({
  pageSize: 'all',
  pageNo: 1,
  suffix: '.xlsx',
  exportSign: '',
  exportParamsList: [],
  allParamsList: []
})
const changeBtn = (item) => {
  if (item.checked) {
    item.checked = false
  } else {
    item.checked = true
  }
}
const handleDelete = async (id) => {
  // 删除逻辑
  const res = await deleteLog({ id: id })
  if (!res.error) {
    window?.$message?.success('删除成功')
    refreshTable(true)
  }
}
const columnOptions = ref([])
props.list.map((item) => {
  if (item.children) {
    item.children.map((child) => {
      columnOptions.value.push({
        title: child.title + '（' + item.title + '）',
        key: child.exportKey ? child.exportKey : child.key,
        checked: false
      })
    })
  } else {
    columnOptions.value.push({
      title: item.title,
      key: item.exportKey ? item.exportKey : item.key,
      checked: false
    })
  }
})
// 导出历史数据
const exportHistory = ref([])

const emit = defineEmits()
const init = async () => {
  const { data: Infodata } = await exportConfiggetInfo({ exportSign: props.exportSign, pageNo: 1, pageSize: 10 })
  columnOptions.value.map((item) => {
    Infodata.exportParamsList.map((child) => {
      if (item.key == child) {
        item.checked = true
      }
    })
  })
}
init()
// 响应式数据
const exportFormat = ref('xlsx')
const totalLimit = ref(5000)

// 事件处理
const handleClose = () => {
  // 关闭逻辑
  emit('close', {})
}

const handleExport = async () => {
  let arr = [],
    allParamsList = []
  columnOptions.value.map((item) => {
    allParamsList.push(item.key)
    if (item.checked) {
      arr.push(item.key)
    }
  })
  await addOrUpdate({ exportSign: props.exportSign, exportParamsList: arr, allParamsList: allParamsList })
  emit(
    'Export',
    Object.assign(
      {
        totalLimit: totalLimit.value,
        selectedColumns: columnOptions.value
      },
      exportData.value
    )
  )
  // 导出逻辑
}
</script>
<style scoped lang="scss">
:root {
  --n-merged-td-color-hover: #f1f7ff;
}
.show {
  opacity: 1;
}
.hide {
  opacity: 0;
}
/* 主要容器 */
.export-container {
  --primary-color: #1890ff;
  --border-color: #e5e7eb;
  --background-light: #f8f9fa;
  max-width: 1200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.header-section {
  margin-bottom: 1.5rem;
}
.header {
  color: #1f2937;
  margin: 0;
}

/* 内容布局 */

/* 配置区域 */
:deep(.n-form-item-label) {
  width: 100px;
  justify-content: flex-end;
}

/* 复选框布局 */
.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px 24px;
}
.checkbox-item {
  :deep(.n-checkbox__label) {
    color: #4b5563;
  }
}

/* 未选中提示 */
.unselected-hint {
  color: #9ca3af;
  font-size: 0.9em;
  margin-top: 8px;
}

/* 历史记录区域 */
.history-section {
  height: 300px;
  & :deep .n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover {
    background-color: #f1f7ff;
  }
  & :deep .n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover > .n-data-table-td {
    background-color: #f1f7ff;
  }
}

.history-title {
  margin-top: 0;
  color: #374151;
  position: relative;
  font-size: 16px;
  font-weight: 500;
  &::after {
    content: '';
    position: absolute;
    top: 4px;
    left: 5px;
    width: 5px;
    height: 17px;
    border-radius: 44px;
    background: var(---, #285fcc);
  }
}
.history-table {
  :deep(th) {
    background-color: var(--background-light);
    color: #6b7280;
  }
  :deep(td) {
    color: #4b5563;
  }
}

/* 操作按钮 */
.footer-actions {
  display: flex;
  justify-content: flex-end;
}
.action-btn {
  min-width: 100px;
  border-radius: 4px;
}
.btnboxactive {
  padding: 8px 12px;
  gap: 10px;
  border-radius: 2px;
  border: 1px solid var(---, #bdd3ff);
  background: var(--2, #eef6fd);
  color: #285fcc;
}
.btnbox {
  cursor: pointer;
  padding: 8px 12px;
  gap: 10px;
  border-radius: 2px;
  border: 1px solid var(----, #e1e4eb);
  color: #3a3f50;
}
.btnbox:hover {
  border: 1px solid var(---, #bdd3ff);
  background: var(--2, #eef6fd);
  color: #285fcc;
}
</style>
