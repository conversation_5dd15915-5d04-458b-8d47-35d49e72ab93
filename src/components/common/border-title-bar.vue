<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    title: string
  }>(),
  {
    title: '标题栏'
  }
)
</script>

<template>
  <div class="h-34px w-full flex items-center justify-between">
    <div class="flex items-center gap-6px">
      <div class="h-16px w-6px rounded bg-primary"></div>
      <div class="text-18px font-500">{{ props.title }}</div>
    </div>
    <div class="flex items-center gap-6px">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<style scoped></style>
