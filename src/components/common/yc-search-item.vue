<script setup lang="ts">
import type { GlobalThemeOverrides } from 'naive-ui'
import { NConfigProvider } from 'naive-ui'

const props = withDefaults(
  defineProps<{
    label?: string
    width?: string
  }>(),
  {
    width: '316px'
  }
)

const searchItemThemeOverrides: GlobalThemeOverrides = {
  common: {
    // borderColor: "transparent",
  },
  Input: {
    // color: "#F7F8FA",
  },
  InternalSelection: {
    // color: "#F7F8FA",
  }
}
</script>

<template>
  <div
    class="flex-center gap-16px"
    :style="{ width: props.width }"
  >
    <span
      v-if="!!props.label"
      class="flex-shrink-0 text-form-label"
      >{{ props.label }}</span
    >
    <NConfigProvider
      :theme-overrides="searchItemThemeOverrides"
      class="w-full"
    >
      <slot></slot>
    </NConfigProvider>
  </div>
</template>

<style scoped></style>
