<template>
  <div class="flex gap-16px items-start justify-between">
    <div
      class="flex-1 flex items-start overflow-hidden gap-16px min-h-[36px]"
      :class="{ 'transition-height duration-300': showExpandBtn }"
      :style="{ height: contentHeight }"
    >
      <div
        class="flex items-center gap-16px flex-wrap w-full"
        ref="searchContentRef"
      >
        <slot></slot>
      </div>
    </div>
    <div class="flex flex-col gap-16px flex-shrink-0 items-start">
      <div class="flex-center gap-12px">
        <NButton
          type="primary"
          @click="handleSearch"
          >查询</NButton
        >
        <NButton @click="handleReset">重置</NButton>
      </div>
      <NButton
        v-if="showExpandBtn"
        text
        icon-placement="right"
        size="small"
        type="primary"
        @click="isExpanded = !isExpanded"
      >
        {{ isExpanded ? '收起' : '展开' }}
        <template #icon>
          <IconAntDesignDownOutlined
            class="text-12px transition-transform duration-300"
            :style="{ transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)' }"
          />
        </template>
      </NButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useElementSize } from '@vueuse/core'
import { computed, ref } from 'vue'

const _emits = defineEmits<{
  (e: 'search'): void
  (e: 'reset'): void
}>()

const searchContentRef = ref<HTMLElement>()
const { height } = useElementSize(searchContentRef)

const isExpanded = ref(false)
const showExpandBtn = computed(() => height.value > 84)

const contentHeight = computed(() => {
  if (height.value <= 84) return `${height.value}px`
  return isExpanded.value ? `${height.value}px` : '84px'
})

const handleSearch = () => {
  _emits('search')
}

const handleReset = () => {
  _emits('reset')
}
</script>
