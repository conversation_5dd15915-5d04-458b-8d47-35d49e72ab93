<script setup lang="ts">
import { computed, ref, useSlots } from 'vue';

interface Emits {
  (e: 'close'): void;
}

const props = withDefaults(
  defineProps<{
    title: string;
    width?: number | string;
    contentCss?: string;
  }>(),
  {
    width: '558px',
    title: 'YC Modal Title'
  }
);

const slots = useSlots();

const emits = defineEmits<Emits>();
const showModal = ref(false);
const modalWidth = computed(() => {
  return typeof props.width === 'number' ? `${props.width}px` : props.width;
});

// 打开弹窗
const open = () => {
  showModal.value = true;
};

// 关闭弹窗
const close = () => {
  showModal.value = false;
  emits('close');
};

defineExpose({
  open,
  close
} as Expose.YCModal);
</script>

<template>
  <NModal v-model:show="showModal" :auto-focus="false" :mask-closable="false">
    <NSpace class="yc-modal" vertical :style="{ width: modalWidth }">
      <NSpace class="yc-modal__header" align="center" justify="space-between">
        <span class="text-text_primary text-16px font-500">{{ title }}</span>
        <NButton text style="font-size: 24px" @click="close">
          <SvgIcon icon="majesticons:close-line" class="text-20px" />
        </NButton>
      </NSpace>
      <div class="yc-modal__content" :class="[contentCss]">
        <slot></slot>
      </div>
      <div v-if="slots.footer" class="yc-modal__footer">
        <slot name="footer"></slot>
      </div>
    </NSpace>
  </NModal>
</template>

<style scoped lang="scss">
.yc-modal {
  @apply bg-#ffffff rounded-8px;
  &__header {
    @apply w-full h-56px px-20px border-b-1 border-solid border-#e1e4eb;
  }

  &__content {
    @apply w-full h-full px-20px pt-8px pb-16px;
  }

  &__footer {
    @apply h-56px px-20px border-t border-#E1E4EB flex justify-end items-center gap-16px;
  }
}
</style>
