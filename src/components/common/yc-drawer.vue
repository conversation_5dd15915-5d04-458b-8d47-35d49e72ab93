<script setup lang="ts">
import { computed, ref, useSlots } from 'vue';

const props = withDefaults(
  defineProps<{
    title: string;
    width?: number | string;
  }>(),
  {
    width: '500px',
    title: 'YC Drawer Title'
  }
);

const slots = useSlots();

const active = ref(false);
const drawerWidth = computed(() => {
  return typeof props.width === 'number' ? `${props.width}px` : props.width;
});

// 打开弹窗
const open = () => {
  active.value = true;
};

// 关闭弹窗
const close = () => {
  active.value = false;
};

defineExpose({
  open,
  close
} as Expose.YCDrawer);
</script>

<template>
  <NDrawer v-model:show="active" :width="drawerWidth" :auto-focus="false" :mask-closable="false" :close-on-esc="false">
    <NDrawerContent closable>
      <template #header>{{ props.title }}</template>
      <slot></slot>
      <template v-if="slots.footer" #footer>
        <div class="w-full flex items-center justify-end gap-12px">
          <slot name="footer"></slot>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
