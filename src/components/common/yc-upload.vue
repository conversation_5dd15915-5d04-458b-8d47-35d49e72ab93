<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui'
import { useDialog } from 'naive-ui'

import { computed, ref } from 'vue'
import { getServiceBaseURL } from '@/utils/service'
import { fileDeleteById } from '@/service/api'
import type { FormItemInjection } from 'naive-ui/es/_mixins/use-form-item'

const formItem = inject<FormItemInjection>('n-form-item')

const dialog = useDialog()
const props = withDefaults(
  defineProps<{
    max?: number
    disabled?: boolean
    showTrigger?: boolean
    defaultUpload?: boolean
    accept?: string
    deleteConfirm?: boolean
    listType?: 'text' | 'image' | 'image-card'
    limitSize?: number | null
    shouldUseThumbnailUrl?: boolean
    showFileList?: boolean
    showRemoveBtn?: boolean
    actionurl?: string
  }>(),
  {
    showTrigger: true,
    defaultUpload: true,
    showFileList: true,
    showRemoveBtn: true,
    deleteConfirm: false,
    listType: 'text',
    limitSize: null,
    max: 50
  }
)
// 定义 emits
const emit = defineEmits<{
  (e: 'upload-success', response: any): void // 定义上传成功事件
}>()
const fileList = defineModel<UploadFileInfo[]>('fileList')

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y'
const { baseURL } = getServiceBaseURL(import.meta.env, isHttpProxy)

const action = computed(() => `${baseURL}${props.actionurl ? props.actionurl : '/attachment/upload'}`)

function handleUploadChange(data: { fileList: UploadFileInfo[] }) {
  fileList.value = data.fileList
}

function handleRemove(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
  if (props.deleteConfirm) {
    return new Promise((resolve) => {
      window?.$dialog?.warning({
        title: '提示',
        content: `是否确认删除 ${data.file?.name ?? ''} 文件`,
        positiveText: '确定',
        negativeText: '返回',
        onPositiveClick: async () => {
          if (typeof data.file.id === 'number' && !isNaN(data.file.id)) await fileDeleteById({ id: data.file.id })
          window?.$message?.success('删除成功')
          resolve(true)
        },
        onNegativeClick: () => {
          resolve(false)
        }
      })
    })
  }
  return true
}

function handleFileListChange() {}

// 上传拦截
function handleBeforeUpload(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
  if (
    props.listType == 'image' &&
    data.file.file?.type !== 'image/png' &&
    data.file.file?.type !== 'image/jpeg' &&
    data.file.file?.type !== 'image/jpg'
  ) {
    window?.$message?.error('只能上传图片文件，请重新上传')
    return false
  }
  // 文件大小限制
  if (props.limitSize && data.file.file?.size > props.limitSize) {
    window?.$message?.error(`文件大小不能超过 ${props.limitSize / 1024}KB`)
    return false
  }
  return true
}

// 上传结束
function handleUploadFinish(options: { file: UploadFileInfo; event?: ProgressEvent }) {
  const res = options.event?.target?.response
  if (res.errcode === 0) {
    options.file.url = res.data.url
    emit('upload-success', res) // 触发上传成功事件
    formItem?.handleContentChange() // 上传成功后调用
  } else if (res.error?.errmsg) {
    window?.$message?.error(res.error?.errmsg)
    const fileIndex = fileList.value?.findIndex((item) => item.id === options.file.id)
    if (fileIndex !== -1 && fileList.value?.length) {
      fileList.value.splice(fileIndex as number, 1)
    }
    throw new Error(res.error?.errmsg)
  }
}
const fileUploadRef = ref(null)
const openFileDialog = () => {
  // dialog.warning({
  //   title: '严厉禁止上传涉密文件',
  //   content: '是否确认？',
  //   positiveText: '确定',
  //   negativeText: '返回',
  //   onPositiveClick: () => {
  //     fileUploadRef.value?.openOpenFileDialog()
  //   },
  //   onNegativeClick: () => {
  //     console.log('negative')
  //   }
  // })
  fileUploadRef.value?.openOpenFileDialog()
}
const handlePreview = () => {
  // 阻止默认行为的多重方式
  // if (event) {
  //   event.preventDefault();
  //   event.stopPropagation();
  // }
  // // 阻止链接跳转的多种方法
  // try {
  //   window.event && (window.event.returnValue = false);
  // } catch {}
  // let base64Text = window.btoa(unescape(encodeURIComponent(file.url)));
  // window.open(otherBaseURL.FILE + base64Text, "_target");
  // // 返回 false 阻止默认行为
  // return false;
}
defineExpose({
  openFileDialog
})
</script>

<template>
  <NUpload
    ref="fileUploadRef"
    v-model:file-list="fileList"
    response-type="json"
    :action="action"
    :max="props.max"
    :show-trigger="props.showTrigger"
    :disabled="props.disabled"
    :default-upload="props.defaultUpload"
    :accept="props.accept"
    :list-type="props.listType"
    :show-file-list="props.showFileList"
    :show-remove-button="props.showRemoveBtn"
    @change="handleUploadChange"
    @remove="handleRemove"
    @update:file-list="handleFileListChange"
    @finish="handleUploadFinish"
    @before-upload="handleBeforeUpload"
    @preview="(file, event) => handlePreview(file, event)"
  >
    <slot></slot>
  </NUpload>
</template>

<style scoped></style>
