<script setup lang="ts">
import { useAttrs } from 'vue';

const props = withDefaults(
  defineProps<{
    title?: string;
  }>(),
  {
    title: '暂无数据'
  }
);

const attrs = useAttrs();
</script>

<template>
  <div class="box-border h-full w-full flex flex-col items-center justify-center gap-6px" :class="attrs.className">
    <img src="@/assets/images/no-data.png" alt="" class="h-100px w-122px" />
    <div class="text-font_placeholder">
      {{ props.title }}
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
