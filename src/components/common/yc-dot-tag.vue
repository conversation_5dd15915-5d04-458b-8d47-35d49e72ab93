<script setup lang="ts">
import { computed } from "vue";
import { AuditTypeModeRecord } from "@/constants/app";

const props = withDefaults(
  defineProps<{
    status: UnionKey.AuditType;
    statusType?: string;
  }>(),
  {
    statusType: "0",
  }
);

const modeRecord = computed(() =>
  props.statusType === "0" ? AuditTypeModeRecord : AuditTypeModeRecord
);
</script>

<template>
  <div
    class="flex items-center gap-4px text-14px font-400 !bg-transparent"
    :class="`audit-tag_${props.status}`"
  >
    <IconCarbonDotMark class="w-14px h-14px flex-shrink-0" />
    <div class="flex-1 truncate">{{ modeRecord[props.status] }}</div>
  </div>
</template>

<style scoped></style>
