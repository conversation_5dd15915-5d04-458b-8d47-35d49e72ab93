<script setup lang="ts">
import { useLoading } from '@yc/hooks'
import { ref } from 'vue'
import * as XLSX from 'xlsx' // 导入 xlsx 库
import { Expose } from '@/typings/expose'

const props = withDefaults(
  defineProps<{
    title?: string | null
    type?: boolean
  }>(),
  {
    title: '导入',
    type: true
  }
)

interface IYcImportProps {
  templateParams: Record<string, any>
  templateApi: Function
  uploadApi: Function
  uploadParams?: Record<string, any>
}

const modalRef: any = ref(null)

const emits = defineEmits(['success', 'upload-success'])

const { loading, startLoading, endLoading } = useLoading()

const fileList: any = ref([])

// 打开弹窗
const modalParams = ref<IYcImportProps>()
const open = (props: IYcImportProps) => {
  modalParams.value = props
  modalRef.value?.open()
  fileList.value = []
}

// 关闭弹窗
const close = () => {
  modalRef.value?.close()
}

// 模板下载
const isDownloading = ref(false)
const downloadTemp = async () => {
  if (isDownloading.value) return
  window?.$message?.warning('正在下载中，请稍后...')
  try {
    isDownloading.value = true
    const fetchApi = modalParams.value?.templateApi ?? (() => {})
    const res = await fetchApi(modalParams.value?.templateParams ?? {})
    if (res.error) throw res.error
    const { response, data } = res
    const element = document.createElement('a')
    const blob = new Blob([data], { type: 'application/vnd.ms-excel' })
    const href = window.URL.createObjectURL(blob)
    element.href = href
    element.download = decodeURIComponent(response.headers['content-disposition'].split('filename=')[1])
    element.click()
    URL.revokeObjectURL(href)
  } finally {
    isDownloading.value = false
  }
}

// 确定
const handleConfirm = async () => {
  if (!fileList.value.length) {
    return window?.$message?.warning('请选择文件')
  }
  try {
    startLoading()
    const file = fileList.value[0].file
    if (props.type) {
      const formData = new FormData()
      formData.append('file', file)
      if (modalParams.value?.uploadParams) {
        Object.keys(modalParams.value?.uploadParams).forEach((key) => {
          formData.append(key, modalParams.value?.uploadParams[key])
        })
      }
      const res = await modalParams.value?.uploadApi(formData)
      if (!res.error) {
        window?.$message?.success('导入成功')
        close()
        emits('success', res.data)
      }
    } else {
      const reader = new FileReader()
      reader.onload = (e: any) => {
        const data = new Uint8Array(e.target.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })

        // 假设我们只读取第一个工作表
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        // 将工作表转换为 JSON 格式
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) // header: 1 表示将第一行作为数据
        console.log(jsonData) // 在控制台输出数据
        // 你可以在这里处理 jsonData，例如将其存储到状态中
        emits(
          'upload-success',
          jsonData.filter((row: any) => {
            // 检查行是否为空，假设行中每个单元格都可以是 null、undefined 或空字符串
            return row.some((cell: any) => cell !== null && cell !== undefined && cell !== '')
          })
        )
      }
      reader.readAsArrayBuffer(file) // 读取文件
    }
  } finally {
    endLoading()
  }
}

defineExpose({
  open,
  close
} as Expose.YCModal)
</script>

<template>
  <YcModal
    ref="modalRef"
    :title="props.title"
    width="600px"
  >
    <NSpace vertical>
      <YcUpload
        v-model:file-list="fileList"
        :max="1"
        :default-upload="false"
        accept=".xls,.xlsx"
      >
        <NButton>选择文件</NButton>
      </YcUpload>
      <p class="text-12px text-#9195a2 font-400">
        请按模板上传文件,模板下载：
        <span
          class="cursor-pointer text-primary"
          @click="downloadTemp"
          >点我下载</span
        >
      </p>
    </NSpace>
    <template #footer>
      <NButton @click="close">返回</NButton>
      <NButton
        type="primary"
        :loading="loading"
        @click="handleConfirm"
        >确定</NButton
      >
    </template>
  </YcModal>
</template>
