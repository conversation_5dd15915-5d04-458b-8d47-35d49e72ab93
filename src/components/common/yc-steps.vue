<script>
export default {
  name: 'Steps',
  props: {
    steps: {
      type: Array,
      required: true
    },
    current: {
      type: Number,
      default: 0
    }
  },
  computed: {
    isLast() {
      return this.current === this.steps.length - 1;
    }
  }
};
</script>

<template>
  <div class="steps">
    <div
      v-for="(step, index) in steps"
      :key="index"
      class="step-line"
      :class="{ 'is-last': index === steps.length - 1 }"
    >
      <div class="step w-full" :class="{ active: current === index, completed: current > index }">
        <div class="w-full flex items-center">
          <div class="step-marker bg-#2F6CF4"></div>
<!--          <div class="mr-5px w-36px h-36px flex-center rounded-50% bg-#2F6CF4 text-16px text-#fff">{{ index + 1 }}</div>-->
          <div v-if="index !== steps.length - 1" class="step-connector w-full"></div>
        </div>
        <div class="step-label">
          {{ step }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.steps {
  display: flex;
  align-items: center;
}

.step-line {
  display: flex;
  flex: 1;
  position: relative;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: center;
  z-index: 1;
  transform: translateX(calc(50% - 23px));
}

.step-marker {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('@/assets/images/step_wait.png') no-repeat;
  background-size: 36px 36px;
  background-position: center;
}

.step.active {
  .step-marker {
    background: url('@/assets/images/step_process.png') 100% 100% no-repeat;
    background-size: cover;
  }

  .step-label {
    color: #204d87;
  }
}

.step.completed {
  .step-marker {
    background: url('@/assets/images/step_finish.png') no-repeat;
    background-size: 36px 36px;
    background-position: center;
  }

  .step-connector {
    background-color: #204d87;
  }

  .step-label {
    color: #3a3f50;
  }
}

.step-connector {
  width: calc(100% - 46px);
  height: 3px;
  background-color: #e1e4eb;
}

.step-label {
  transform: translateX(calc(-50% + 23px));
  font-size: 14px;
  font-weight: 500;
  color: #9195a2;
}

.step-line.is-last .step-connector {
  display: none;
}
.is-last .step{
  width: 50%;
  transform: translateX(calc(100% - 23px));
}
</style>
