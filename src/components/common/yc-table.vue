<script setup lang="ts">
import { computed, h, ref } from 'vue'
import { useTable } from '@/hooks/useTable'
import type { TableUtil } from '@/typings/table'
import type { Expose } from '@/typings/expose'
import { VueDraggable } from 'vue-draggable-plus'

import { DataTableCreateSummary, TableProps } from 'naive-ui'

type TableThemeOverrides = NonNullable<TableProps['themeOverrides']>

const props = withDefaults(
  defineProps<{
    tableConfig: TableUtil.TableConfig
    showPaginate?: boolean
    bordered?: boolean
    striped?: boolean
    /** 表格内容的横向宽度，如果列被水平固定了，则需要设定它 */
    scrollX?: number | string
    defaultCheckedRowKeys?: Array<string | number>
    checkedRowKeys?: Array<string | number>
    showSubHeader?: boolean
    showSubHeaderBorder?: boolean
    summary?: DataTableCreateSummary
    tableThemeOverrides?: TableThemeOverrides
  }>(),
  {
    bordered: false,
    striped: true,
    showPaginate: true,
    defaultCheckedRowKeys: () => [],
    showSubHeader: true,
    showSubHeaderBorder: true,
    tableThemeOverrides: () => ({
      thTextColor: '#9195A2',
      thColor: '#F0F2F5',
      borderColor: '#E1E4EB'
    })
  }
)

const emits = defineEmits(['update:checkedRowKeys'])

const renderCell = (value: string | number) => {
  if (!value && value !== 0) {
    return h('span', {}, { default: () => '--' })
  }
  return value
}

const baseConfig = ref({
  transformer: (res: any) => {
    const { list = [], rows = [], pageNum = 1, pageNo = 1, pageSize = 10, total = 0 } = res.data.pageInfo || res.data || {}
    const bindPageSize = pageSize <= 0 ? 10 : pageSize
    const recordsWithIndex = [...(list ? list : []), ...(rows ? rows : [])].map((item: any, index: number) => {
      return {
        ...item,
        index: (pageNum - 1) * pageSize + index + 1
      }
    })
    return {
      data: recordsWithIndex,
      totalData: res.data.sumInfo,
      pageNum,
      pageNo,
      pageSize: bindPageSize,
      total
    }
  }
})

const tableConfig = computed(() => {
  return Object.assign(baseConfig.value, props.tableConfig)
})

const {
  columns,
  columnChecks,
  data,
  loading,
  totalData,
  pagination,
  sortState,
  handleSortChange,
  getData,
  updatePagination,
  updateSearchParams,
  reloadColumns
} = useTable(tableConfig.value)
// 刷新数据
const refreshData = (isReset = true) => {
  reloadColumns()
  updateSearchParams(tableConfig.value.apiParams)
  isReset && updatePagination({ page: 1 })
  getData()
}

// 是否有数据
const hasData = computed(() => {
  return Boolean(pagination.itemCount)
})

// 选中行
const handleCheck = (keys: Array<string | number>) => {
  emits('update:checkedRowKeys', keys)
}
const rowProps = (row: any) => {
  return {
    onmouseenter: () => {
      row['showIcon'] = true
    },
    onmouseleave: () => {
      row['showIcon'] = false
    }
  }
}

// 提取列设置相关逻辑到组合式函数
const useColumnSettings = (columns: Ref<any[]>) => {
  /**
   * 将列移动到顶部
   * @param index 列索引
   */
  const handleTop = (index: number) => {
    const item = columns.value.splice(index, 1)[0]
    columns.value.unshift(item)
  }

  /**
   * 将列移动到底部
   * @param index 列索引
   */
  const handleBottom = (index: number) => {
    const item = columns.value.splice(index, 1)[0]
    columns.value.push(item)
  }

  return {
    handleTop,
    handleBottom
  }
}

// 使用组合式函数
const { handleTop, handleBottom } = useColumnSettings(columnChecks)

defineExpose({
  refreshData,
  hasData,
  currentPageData: data,
  alltotalData: totalData,
  pagination
} as Expose.YCTable)
</script>

<template>
  <div class="yc-table h-full w-full flex flex-col justify-start gap-16px">
    <slot name="header"></slot>

    <div
      v-if="props.showSubHeader"
      class="flex items-center justify-between"
      :class="{ 'pt-16px border-t border-t-solid border-t-#e1e4eb': props.showSubHeaderBorder }"
    >
      <div class="flex-1 h-full">
        <slot name="header-sub"></slot>
      </div>
      <NPopover
        placement="bottom-end"
        trigger="click"
        style="max-height: 300px"
        scrollable
        content-class="!px-8px"
      >
        <template #trigger>
          <NButton>
            <template #icon>
              <IconAntDesignSettingOutlined />
            </template>
            列设置
          </NButton>
        </template>
        <div class="pb-8px border-b border-[#eee] mb-8px px-8px">
          <NCheckbox
            :checked="columnChecks.every((item: any) => item.checked)"
            @update:checked="(column) => columnChecks.forEach((item: any) => (item.checked = column))"
            >全选</NCheckbox
          >
        </div>
        <VueDraggable
          v-model="columnChecks"
          :animation="150"
          filter=".none_draggable"
        >
          <div
            v-for="(item, index) in columnChecks"
            :key="index"
            class="h-36px px-8px group flex items-center items-start justify-center rounded-4px hover:(bg-primary bg-opacity-20)"
          >
            <NCheckbox
              v-model:checked="item.checked"
              class="flex-1 none_draggable"
              >{{ item.title }}</NCheckbox
            >
            <div class="flex gap-4px none_draggable">
              <NButton
                quaternary
                type="text"
                size="tiny"
                @click.stop="handleTop(index)"
                v-if="index !== 0"
              >
                <template #icon>
                  <IconAntDesignVerticalAlignTopOutlined />
                </template>
              </NButton>
              <NButton
                quaternary
                type="text"
                size="tiny"
                @click.stop="handleBottom(index)"
                v-if="index !== columnChecks.length - 1"
              >
                <template #icon>
                  <IconAntDesignVerticalAlignTopOutlined class="rotate-180" />
                </template>
              </NButton>
            </div>
            <IconMdiDrag class="h-full cursor-move text-icon" />
          </div>
        </VueDraggable>
      </NPopover>
    </div>

    <NDataTable
      ref="table"
      :scroll-x="props.scrollX"
      flex-height
      :bordered="bordered"
      :checked-row-keys="props.checkedRowKeys"
      :striped="striped"
      class="h-full w-full"
      remote
      :single-line="false"
      :render-cell="renderCell"
      :columns="columns"
      :data="data"
      :loading="loading"
      :pagination="pagination"
      :paginate-single-page="showPaginate"
      :row-key="(rowData) => rowData.id"
      :default-checked-row-keys="props.defaultCheckedRowKeys"
      @update:checked-row-keys="handleCheck"
      @update:sorter="handleSortChange"
      :row-props="rowProps"
      :summary="props.summary"
      :theme-overrides="props.tableThemeOverrides"
    >
      <template #empty>
        <YcEmpty />
      </template>
    </NDataTable>
    <slot name="footer"></slot>
  </div>
</template>

<style scoped lang="scss">
:deep(.n-data-table .n-data-table-empty) {
  padding: 0 !important;
}
</style>
