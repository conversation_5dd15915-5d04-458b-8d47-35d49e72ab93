<template>
  <YcModal ref="modalRef" title="定制列" width="600px">
    <n-transfer
      class="h-450px"
      v-model:value="value"
      :options="options"
      :render-target-list="renderTargetList"
      source-filterable
    />
    <template #footer>
      <NButton @click="handleCancel">返回</NButton>
      <NButton type="primary" :loading="loading" @click="handleSubmit"
        >确认</NButton
      >
    </template>
  </YcModal>
</template>

<script lang="ts" setup>
import type { Expose } from "@/typings/expose";

import { ref, h, computed, watch } from "vue";
import { NIcon } from "naive-ui";
import draggable from "vuedraggable";
import { useLoading } from "~/packages/hooks";
const emits = defineEmits(["update:show", "handleSubmit"]);

const { loading } = useLoading();
//@ts-ignore
import { Close } from "@vicons/ionicons5"; // 使用 Ionicons 的 Close 图标
const props = withDefaults(
  defineProps<{
    list?: any;
    targetlist?: Array<string | number>;
    show?: boolean;
  }>(),
  {
    list: () => [],
    targetlist: () => [],
  }
);
const value = ref<Array<string | number>>(props.targetlist);
const options = ref([]);
options.value = props.list.map((item) => ({
  label: item.title,
  value: item.key,
}));
// const transformedArray = computed(() => {
//   return props.list.map((item) => ({
//     label: item.title,
//     value: item.key,
//   }));
// });
const modalRef = ref<Expose.YCModal>();
const handleCancel = () => {
  modalRef.value?.close();
  // emits("update:show", false);
};
const handleSubmit = () => {
  emits("handleSubmit", value.value);
  handleCancel();
};
const renderTargetList = function () {
  return h(
    draggable,
    {
      modelValue: value.value,
      "onUpdate:modelValue": (newValue: Array<string | number>) => {
        value.value = newValue;
      },
      group: "items",
      animation: 200,
    },
    {
      item: ({ element }: { element: string | number }) => {
        const option = options.value.find((opt) => opt.value === element);
        return h(
          "div",
          {
            key: element,
            class: "hover",
            style:
              "padding: 8px 16px; margin: 4px; border-radius: 4px; display: flex; justify-content: space-between; align-items: center;",
          },
          [
            option ? option.label : element,
            h(
              NIcon,
              {
                onClick: () => {
                  // 点击图标时从目标列表中移除该项
                  value.value = value.value.filter((item) => item !== element);
                },
              },
              {
                default: () => h(Close), // 使用 Close 图标
              }
            ),
          ]
        );
      },
    }
  );
};
const Handopen = () => {
  console.log(modalRef.value);
  modalRef.value?.open();
};
watch([() => props.show, () => props.targetlist], ([show, newcheckData]) => {
  if (show) {
    modalRef.value?.open();
  } else {
    handleCancel();
  }
  if (newcheckData) {
    value.value = newcheckData;
  }
});
defineExpose({
  open: () => Handopen(),
  close: () => handleCancel(),
} as Expose.YCModal);
</script>
<style>
.hover:hover {
  background: rgb(243, 243, 245);
}
</style>
