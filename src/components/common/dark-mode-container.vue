<!--
 * @Author: yyh <EMAIL>
 * @Date: 2024-10-24 08:42:02
 * @LastEditors: yyh <EMAIL>
 * @LastEditTime: 2024-11-18 10:10:04
 * @FilePath: \szhjj-html-manage\src\components\common\dark-mode-container.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
defineOptions({ name: "DarkModeContainer" });

defineProps<Props>();

interface Props {
  inverted?: boolean;
}
</script>

<template>
  <div
    class="text-base_text transition-300"
    :class="{ 'bg-inverted text-#1f1f1f': inverted }"
  >
    <slot></slot>
  </div>
</template>

<style scoped></style>
