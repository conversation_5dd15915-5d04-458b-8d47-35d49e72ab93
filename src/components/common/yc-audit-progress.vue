<script setup lang="ts">
import { useLoading } from '@yc/hooks';
import { ref, watchEffect } from 'vue';
import { TaskStatusModeRecord } from '@/constants/app';
import { fetchFlowQueryAuditInfo } from '@/service/api';

const props = withDefaults(
  defineProps<{
    id: string;
  }>(),
  {}
);

const { loading, startLoading, endLoading } = useLoading();

const auditDetail = ref<any>({});

async function getInfo(id: string) {
  try {
    startLoading();
    const { data, error } = await fetchFlowQueryAuditInfo(id);
    if (error) throw error;
    auditDetail.value = data;
  } finally {
    endLoading();
  }
}

const tagCss = ref<Record<UnionKey.TaskStatus, string>>({
  TODO: 'bg-#EFF5FD text-#3C62AC',
  WITHDRAW: 'bg-#F0F2F5 text-#9195A2',
  PASS: 'bg-#ECF7EC text-#3EB349',
  DENY: 'bg-#FEE0D5 text-#E56B5C'
});

const Icons = {
  DENY: new URL('@/assets/images/audit_DENY.png', import.meta.url).href,
  PASS: new URL('@/assets/images/audit_PASS.png', import.meta.url).href,
  TODO: new URL('@/assets/images/audit_TODO.png', import.meta.url).href,
  WITHDRAW: new URL('@/assets/images/audit_WITHDRAW.png', import.meta.url).href
};

watchEffect(() => {
  if (props.id) {
    getInfo(props.id);
  }
});
</script>

<template>
  <NSpin :show="loading" class="h-full w-full overflow-y-auto">
    <div class="box-border w-full w-full bg-white px-16px py-12px">
      <div v-for="(item, index) in auditDetail.taskInfoList" :key="index" class="flex justify-between gap-6px">
        <div class="flex flex-col items-center">
          <img :src="Icons[item.taskStatus]" class="mt-6px h-14px w-14px" />
          <div class="h-full flex-1 translate-x-[-1px] border-r-1px border-#E1E4EB border-r-dashed py-4px" />
        </div>
        <div class="flex-1">
          <div class="flex justify-between text-font_main">
            <div>{{ item.stepName }}</div>
          </div>
          <div
            v-if="item.taskUserInfoList && item.taskUserInfoList.length && !item.isClose"
            class="flex flex-col gap-12px pb-12px pt-12px"
          >
            <div v-for="(sub, subIndex) in item.taskUserInfoList" :key="subIndex" class="">
              <div class="flex flex-col justify-between gap-4px">
                <div class="flex items-center gap-8px">
                  <div class="text-14px text-font_main font-400">
                    {{ sub.userName }}
                  </div>
                  <div class="yc-tag text-12px font-400" :class="tagCss[sub.taskStatus as UnionKey.TaskStatus]">
                    {{
                      sub?.isCreator && sub.taskStatus === 'PASS'
                        ? '已提交'
                        : sub?.isCreator && sub.taskStatus === 'TODO'
                          ? '待提交'
                          : TaskStatusModeRecord[sub.taskStatus as UnionKey.TaskStatus]
                    }}
                  </div>
                </div>
                <div v-if="sub.remark" class="mt-4px text-12px text-font_placeholder">退回原因：{{ sub.remark }}</div>
                <div class="text-12px text-font_placeholder">
                  {{ sub.auditDate }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NSpin>
</template>

<style scoped lang="scss"></style>
