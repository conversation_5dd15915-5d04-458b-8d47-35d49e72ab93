import { NButton, NPopconfirm } from 'naive-ui'
import { h, resolveDirective, withDirectives } from 'vue'

// 等待
export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * Transform record to option
 *
 * @example
 *   ```ts
 *   const record = {
 *     key1: 'label1',
 *     key2: 'label2'
 *   };
 *   const options = transformRecordToOption(record);
 *   // [
 *   //   { value: 'key1', label: 'label1' },
 *   //   { value: 'key2', label: 'label2' }
 *   // ]
 *   ```;
 *
 * @param record
 */
export function transformRecordToOption<T extends Record<string, string>>(record: T) {
  return Object.entries(record).map(([value, label]) => ({
    value,
    label
  })) as Common.Option<keyof T>[]
}

export function filterRecordOptionByValues(options: any, values: string[]) {
  return options.filter((option) => !values.includes(option.value))
}
export function generateSerial() {
  const now = new Date();
  // 格式化年月日为 YYYYMMDD
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const datePart = `${year}${month}${day}`;
  
  // 生成五位随机数（允许前导零）
  const randomPart = String(Math.floor(Math.random() * 100000)).padStart(5, '0');
  
  // 拼接流水号
 return `FBBA${datePart}${randomPart}`;
}

/**
 * Toggle html class
 *
 * @param className
 */
export function toggleHtmlClass(className: string) {
  function add() {
    document.documentElement.classList.add(className)
  }

  function remove() {
    document.documentElement.classList.remove(className)
  }

  return {
    add,
    remove
  }
}

// 创建文本按钮
export function createTextButton(params: { text: string; onClick?: () => void; permissionCode?: string }) {
  return withDirectives(
    h(
      NButton,
      {
        text: true,
        type: 'primary',
        onClick: () => params.onClick?.()
      },
      {
        default: () => params.text
      }
    ),
    params?.permissionCode ? [[vPermission, params.permissionCode]] : []
  )
}

// 创建删除确认弹窗 文本按钮
export function createConfirmTextButton(params: {
  text: string
  confirmText: string
  onClick: () => void
  type?: 'error' | 'warning' | 'info' | 'success'
  buttonType?: 'primary' | 'error'
  permissionCode?: string
}) {
  return h(
    NPopconfirm,
    {
      onPositiveClick: params.onClick
    },
    {
      trigger: () =>
        withDirectives(
          h(
            NButton,
            {
              type: params.buttonType ? params.buttonType : 'error',
              text: true
            },
            { default: () => params.text }
          ),
          params?.permissionCode ? [[vPermission, params.permissionCode]] : []
        ),
      default: () => params.confirmText
    }
  )
}

// 过滤掉空值
export function formatParams(params: Record<string, unknown>) {
  const formattedParams: Record<string, unknown> = {}

  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      formattedParams[key] = value
    }
  })

  return formattedParams
}

// 根据url地址获取文件名称
export function getFileNameFromUrl(url: string) {
  if (url) {
    const lastSlashIndex = url.lastIndexOf('/')
    const paramsIndex = url.lastIndexOf('?')

    if (paramsIndex <= 0) {
      return lastSlashIndex <= 0 ? url : url.slice(lastSlashIndex + 1)
    } // 存在拼接请求参数
    return lastSlashIndex <= 0 ? url : url.slice(lastSlashIndex + 1, paramsIndex)
  }

  return ''
}

// 根据url地址获取文件后缀
export function getFileExtFromUrl(url: string) {
  const fileName = getFileNameFromUrl(url)
  if (fileName !== null) {
    const lastDotIndex = fileName.lastIndexOf('.')
    if (lastDotIndex <= 0) {
      return ''
    }
    return fileName.slice(lastDotIndex + 1)
  }
}

// 根据url下载文件
export async function downloadFileByUrl(url: string, title?: string) {
  const blobFile = await fetch(url).then((res) => res.blob())
  if (blobFile) {
    const url = URL.createObjectURL(blobFile)
    const a = document.createElement('a')
    a.href = url
    a.download = title || getFileNameFromUrl(url)
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }
}

// 预览
export function previewDataByUrl(url: string) {
  const type = getFileExtFromUrl(url)
  const isImage = ['jpg', 'jpeg', 'png'].includes(type)
  const isVideo = ['mp4', 'avi'].includes(type)
  const isAudio = ['mp3', 'mp4'].includes(type)
  let contentHTML = ''
  if (isImage) {
    contentHTML = `<img src='${url}' width="40%" style="display: block;
    -webkit-user-select: none;
    margin: auto;
    background-color: hsl(0, 0%, 90%);
    transition: background-color 300ms;"/>`
  } else if (isVideo) {
    contentHTML = `<video src='${url}' width="90%" controls></video>`
  } else if (isAudio) {
    contentHTML = `<audio controls src="${url}"></audio>`
  }
  window.open('', '', '').document.write(
    `<!DOCTYPE html>
          <html>
            <body
                style="margin: 0;height: 100vh;background-color: rgb(14, 14, 14);display: flex;justify-content: center;align-items: center;">
              ${contentHTML}
            </body>
          </html>
`
  )
}

// 将全名称部门数组转换为每个部门最后一级部门并连接的字符串
export function transformDepartmentName(departmentName: string[]) {
  if (!departmentName.length) return '--'
  return departmentName.map((name) => name.split('/').pop() ?? '').join('、')
}

/**
 * 秒钟转为时分秒
 *
 * @param seconds
 * @returns hh小时mm分ss秒 / mm分ss秒 / ss秒
 */
export function secondsToTime(seconds: number) {
  const h = Math.floor(seconds / 3600)
  const m = Math.floor((seconds % 3600) / 60)
  const s = seconds % 60
  return h > 0 ? `${h}小时${m}分${s}秒` : m > 0 ? `${m}分${s}秒` : `${s}秒`
}

/**
 * 时间戳转日期
 * @param timestamp
 * @returns yy-mm-dd
 */
export function timestampToFormattedDate(timestamp: number) {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = ('0' + (date.getMonth() + 1)).slice(-2)
  const day = ('0' + date.getDate()).slice(-2)
  return `${year}-${month}-${day}`
}

/**
 * 防抖
 * @param fn
 * @param delay
 * @returns
 */
export function debounce(fn: any, delay = 300) {
  let timer: any = null
  return (...args: any) => {
    if (timer != null) {
      clearTimeout(timer)
      timer = null
    }
    timer = setTimeout(() => {
      fn.call(this, ...args)
    }, delay)
  }
}
/**
 * 过滤字典
 * @param fn
 * @param delay
 * @returns
 */
export function loadDict(data: any) {
  let arr = data.map((t: any) => {
    t.label = t.dictDescription
    t.value = t.dictValue
    return t
  })
  return arr
}
/**
 * 字典回显
 * @param url
 * @param
 * @returns
 */
import { fetchDictDetailByType } from '@/service/api'
import { log } from 'console'
export async function getCertificateType(url: string, val?: string) {
  const { data = [] } = await fetchDictDetailByType(url)
  const DictList = data.map((t: any) => {
    return { label: t.dictDescription, value: t.dictValue }
  })
  const object = data.reduce((acc: any, item: any) => {
    acc[item.dictValue] = item.dictDescription
    return acc
  }, {})

  return val ? object[val] : DictList
}
