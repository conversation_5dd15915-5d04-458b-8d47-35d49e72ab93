import { SM2 } from 'gm-crypto'

const { publicKey, privateKey } = SM2.generateKeyPair()

// SM2 加密
export const encryptedDataBySM2 = (data: unknown) => {
  const newData = JSON.stringify(data);
  const encryptData = SM2.encrypt(newData, publicKey);
  return encryptData;
}

// SM2 解密
export const decryptedDataBySM2 = (cipherText: string) => {
  const decryptData = SM2.decrypt(cipherText, privateKey);
  const parseDecryptData = JSON.parse(decryptData);
  console.log('----decryptData----', parseDecryptData);
  return parseDecryptData || null;
}
