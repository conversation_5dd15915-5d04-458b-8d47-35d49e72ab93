// 自定义图片上传
export class ImageUploadAdapter {
  constructor(loader: any) {
    // The file loader instance to use during the upload.
    this.loader = loader;
  }
  // Starts the upload process.
  upload() {
    return this.loader.file.then(file => {
      return new Promise((resolve, reject) => {
        const formData = new FormData();
        formData.append('file', file, file.name);
        /// todo 需要修改上传逻辑
        console.log('file ---', file);
        resolve({
          default: 'xxxx.jpg'
        });
      });
    });
  }
}
