import { computed, ref, unref } from 'vue'
import type { RouteRecordRaw } from 'vue-router'
import { defineStore } from 'pinia'
import { useBoolean } from '@yc/hooks'
import type { CustomRoute, ElegantConstRoute, LastLevelRouteKey, RouteKey, RouteMap } from '@elegant-router/types'
import { SetupStoreId } from '@/enum'
import { router } from '@/router'
import { createRoutes, getAuthVueRoutes, ROOT_ROUTE } from '@/router/routes'
import { getRouteName, getRoutePath } from '@/router/elegant/transform'
import { fetchIsRouteExist } from '@/service/api'
import { useAppStore } from '../app'
import { useAuthStore } from '../auth'
import { useTabStore } from '../tab'
import {
  findFirstRouterName,
  getBreadcrumbsByRoute,
  getCacheRouteNames,
  getGlobalMenusByAuthRoutes,
  getSelectedMenuKeyPathByKey,
  isRouteExistByRouteName,
  sortRoutesByOrder,
  transformRoutePathToRouteName,
  updateLocaleOfGlobalMenus
} from './shared'

export const useRouteStore = defineStore(SetupStoreId.Route, () => {
  const appStore = useAppStore()
  const authStore = useAuthStore()
  const tabStore = useTabStore()
  const { bool: isInitAuthRoute, setBool: setIsInitAuthRoute } = useBoolean()
  const removeRouteFns: (() => void)[] = []

  /**
   * Auth route mode
   *
   * It recommends to use static mode in the development environment, and use dynamic mode in the production
   * environment, if use static mode in development environment, the auth routes will be auto generated by plugin
   * "@elegant-router/vue"
   */
  const authRouteMode = ref(import.meta.env.VITE_AUTH_ROUTE_MODE)

  /** Home route key */
  const routeHome = ref(import.meta.env.VITE_ROUTE_HOME)

  /**
   * Set route home
   *
   * @param routeKey Route key
   */
  function setRouteHome(routeKey: LastLevelRouteKey) {
    routeHome.value = routeKey
  }

  /** Global menus */
  const menus = ref<App.Global.Menu[]>([])

  /** Get global menus */
  function getGlobalMenus(routes: ElegantConstRoute[]) {
    menus.value = getGlobalMenusByAuthRoutes(routes)
  }

  /** Update global menus by locale */
  function updateGlobalMenusByLocale() {
    menus.value = updateLocaleOfGlobalMenus(menus.value)
  }

  /** Cache routes */
  const cacheRoutes = ref<RouteKey[]>([])

  /**
   * Get cache routes
   *
   * @param routes Vue routes
   */
  function getCacheRoutes(routes: RouteRecordRaw[]) {
    const { constantVueRoutes } = createRoutes()

    cacheRoutes.value = getCacheRouteNames([...constantVueRoutes, ...routes])
  }

  /**
   * Add cache routes
   *
   * @param routeKey
   */
  function addCacheRoutes(routeKey: RouteKey) {
    if (cacheRoutes.value.includes(routeKey)) return

    cacheRoutes.value.push(routeKey)
  }

  /**
   * Remove cache routes
   *
   * @param routeKey
   */
  function removeCacheRoutes(routeKey: RouteKey) {
    const index = cacheRoutes.value.findIndex((item) => item === routeKey)

    if (index === -1) return

    cacheRoutes.value.splice(index, 1)
  }

  /**
   * Re-cache routes by route key
   *
   * @param routeKey
   */
  async function reCacheRoutesByKey(routeKey: RouteKey) {
    removeCacheRoutes(routeKey)

    await appStore.reloadPage()

    addCacheRoutes(routeKey)
  }

  /**
   * Re-cache routes by route keys
   *
   * @param routeKeys
   */
  async function reCacheRoutesByKeys(routeKeys: RouteKey[]) {
    for await (const key of routeKeys) {
      await reCacheRoutesByKey(key)
    }
  }

  /** Global breadcrumbs */
  const breadcrumbs = computed(() => getBreadcrumbsByRoute(router.currentRoute.value, menus.value))

  /** Reset store */
  async function resetStore() {
    const routeStore = useRouteStore()

    routeStore.$reset()

    resetVueRoutes()
  }

  /** Reset vue routes */
  function resetVueRoutes() {
    removeRouteFns.forEach((fn) => fn())
    removeRouteFns.length = 0
  }

  /** Init auth route */
  async function initAuthRoute() {
    if (authRouteMode.value === 'static') {
      await initStaticAuthRoute()
    } else {
      await initDynamicAuthRoute()
    }

    tabStore.initHomeTab(router)
  }

  /** Init static auth route */
  async function initStaticAuthRoute() {
    const { authRoutes } = createRoutes()

    // const filteredAuthRoutes = filterAuthRoutesByRoles(authRoutes, authStore.userInfo.roles);

    handleAuthRoutes(authRoutes)
    console.log('authRoutes', authRoutes)

    setIsInitAuthRoute(true)
  }

  // 获取Map 菜单数据
  const getFlatMenuListMap = (menuList: any[]) => {
    const flatMenuListMap = new Map<number, string>()
    // 将tree数据转换成map数据
    const loopFunction = (list: any[]) => {
      list.forEach((item) => {
        const currentRouteName = transformRoutePathToRouteName(item.pageUrl)
        flatMenuListMap.set(item.id, currentRouteName)
        if (item?.children && item.children.length) loopFunction(item.children)
      })
    }
    loopFunction(menuList)
    return flatMenuListMap
  }

  /** 获取后端返回的菜单数据名称 */
  function handleMenuToRoutes(menuList: any[], flatMenuListMap: Map<number, string>, isChildren = false, level = 1) {
    return menuList.map((item) => {
      const currentRouteName = transformRoutePathToRouteName(item.pageUrl)
      const isLocalIcon = item.iconUrl?.startsWith('local:')
      const keepAliveRouteName = [
        'science_list',
        'creditrecord_bank',
        'creditrecord_guarantee',
        'supplement_bank',
        'supplement_guarantee',
        'supplement_todo',
        'supplement_done',
        'statistics_bank-loan',
        'statistics_bank-loan_detail',
        'statistics_regional-loan',
        'statistics_regional-loan_detail'
      ]
      const blankRouteNames = ['board']
      return {
        name: currentRouteName,
        path: item.pageUrl,
        component: blankRouteNames.includes(currentRouteName)
          ? `layout.blank$view.${currentRouteName}`
          : level === 1 && !item.children?.length
            ? `layout.base$view.${currentRouteName}`
            : isChildren || !item.children?.length
              ? `view.${currentRouteName}`
              : 'layout.base',
        children: item.children && item.children.length ? handleMenuToRoutes(item.children, flatMenuListMap, true, level + 1) : null,
        meta: {
          title: item?.name ?? currentRouteName,
          icon: item.iconUrl && !isLocalIcon ? item.iconUrl : null,
          localIcon: isLocalIcon ? item.iconUrl?.replace('local:', '') : null,
          hideInMenu: item.isHide,
          activeMenu: item?.activeMenu ? transformRoutePathToRouteName(item.activeMenu) : null
        }
      }
    })
  }

  /** Init dynamic auth route */
  async function initDynamicAuthRoute() {
    const elementItemList = unref(authStore.userInfo).elementItemList || []
    if (elementItemList.length) {
      const flatMenuListMap = getFlatMenuListMap(elementItemList)
      const routes = handleMenuToRoutes(elementItemList, flatMenuListMap, false)
      console.log('newRoutes', routes)

      handleAuthRoutes(routes)

      const firstRouteName = findFirstRouterName(routes)
      console.log('firstRouteName', firstRouteName)
      setRouteHome(firstRouteName)

      handleUpdateRootRouteRedirect(firstRouteName)

      setIsInitAuthRoute(true)
    } else {
      window?.$message?.error('获取权限列表失败')
      await authStore.logout()
    }
  }

  /**
   * Handle routes
   *
   * @param routes Auth routes
   */
  function handleAuthRoutes(routes: ElegantConstRoute[]) {
    const sortRoutes = sortRoutesByOrder(routes)
    console.log('🚀 ~ handleAuthRoutes ~ sortRoutes:', sortRoutes)

    const vueRoutes = getAuthVueRoutes(sortRoutes)

    console.log('vueRoutes', vueRoutes)

    addRoutesToVueRouter(vueRoutes)

    getGlobalMenus(sortRoutes)

    getCacheRoutes(vueRoutes)
  }

  /**
   * Add routes to vue router
   *
   * @param routes Vue routes
   */
  function addRoutesToVueRouter(routes: RouteRecordRaw[]) {
    routes.forEach((route) => {
      const removeFn = router.addRoute(route)
      addRemoveRouteFn(removeFn)
    })
  }

  /**
   * Add remove route fn
   *
   * @param fn
   */
  function addRemoveRouteFn(fn: () => void) {
    removeRouteFns.push(fn)
  }

  /**
   * Update root route redirect when auth route mode is dynamic
   *
   * @param redirectKey Redirect route key
   */
  function handleUpdateRootRouteRedirect(redirectKey: LastLevelRouteKey) {
    const redirect = getRoutePath(redirectKey)

    if (redirect) {
      const rootRoute: CustomRoute = { ...ROOT_ROUTE, redirect }

      router.removeRoute(rootRoute.name)

      const [rootVueRoute] = getAuthVueRoutes([rootRoute])

      router.addRoute(rootVueRoute)
    }
  }

  /**
   * Get is auth route exist
   *
   * @param routePath Route path
   */
  async function getIsAuthRouteExist(routePath: RouteMap[RouteKey]) {
    const routeName = getRouteName(routePath)

    if (!routeName) {
      return false
    }

    if (authRouteMode.value === 'static') {
      const { authRoutes } = createRoutes()

      return isRouteExistByRouteName(routeName, authRoutes)
    }

    const { data } = await fetchIsRouteExist(routeName)

    return data
  }

  /**
   * Get selected menu key path
   *
   * @param selectedKey Selected menu key
   */
  function getSelectedMenuKeyPath(selectedKey: string) {
    return getSelectedMenuKeyPathByKey(selectedKey, menus.value)
  }

  return {
    resetStore,
    routeHome,
    menus,
    authRouteMode,
    updateGlobalMenusByLocale,
    cacheRoutes,
    reCacheRoutesByKey,
    reCacheRoutesByKeys,
    breadcrumbs,
    initAuthRoute,
    isInitAuthRoute,
    setIsInitAuthRoute,
    getIsAuthRouteExist,
    getSelectedMenuKeyPath
  }
})
