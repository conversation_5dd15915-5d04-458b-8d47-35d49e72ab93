import type { Ref } from 'vue'
import { computed, ref } from 'vue'
import { defineStore } from 'pinia'
import { useLoading } from '@yc/hooks'
import { SetupStoreId } from '@/enum'
import { useRouterPush } from '@/hooks/useRouterPush'
import { fetchLogin, fetchLogout } from '@/service/api'
import { localStg } from '@/utils/storage'
import { $t } from '@/locales'
import { sleep } from '@/utils/common'
import { useRouteStore } from '../route'
import { clearAuthStorage, getToken, getUserInfo } from './shared'

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const routeStore = useRouteStore()
  const { route, toLogin, redirectFromLogin } = useRouterPush(false)
  const { loading: loginLoading, startLoading, endLoading } = useLoading()

  const token = computed(() => getToken())

  const userInfo: Ref<Api.Auth.UserInfo> = ref(getUserInfo())

  /** Is login */
  const isLogin = computed(() => Boolean(token.value))

  /** Reset auth store */
  async function resetStore() {
    const authStore = useAuthStore()

    clearAuthStorage()

    authStore.$reset()

    if (!route.value.meta.constant) {
      await toLogin()
    }

    routeStore.resetStore()
  }

  /**
   * 登录
   * @param userName 用户名
   * @param password 密码
   */
  async function login(userName: string, password: string, verifyCode: string) {
    startLoading()
    try {
      const { data: resUserInfo, error } = await fetchLogin(userName, password, verifyCode)
      if (!error) {
        // const pass = await loginByToken(loginToken);
        userInfo.value = resUserInfo as Api.Auth.UserInfo
        localStg.set('token', `token-${resUserInfo?.userId}`)
        localStg.set('refreshToken', `refreshToken-${resUserInfo?.userId}`)
        localStg.set('userInfo', resUserInfo)

        await sleep(400)

        await routeStore.initAuthRoute()

        await redirectFromLogin()

        if (routeStore.isInitAuthRoute) {
          window.$notification?.success({
            title: $t('page.login.common.loginSuccess'),
            content: $t('page.login.common.welcomeBack', { userName: resUserInfo.name }),
            duration: 4500
          })
        }
      } else {
        resetStore()
      }
    } finally {
      endLoading()
    }
  }

  // 退出登录
  async function logout(isFetchLogout = true) {
    isFetchLogout && (await fetchLogout())
    await resetStore()
  }

  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('token', loginToken.token)
    localStg.set('refreshToken', loginToken.refreshToken)

    // const { data: info, error } = await fetchGetUserInfo();
    //
    // if (!error) {
    //   // 2. store user info
    //   localStg.set('userInfo', info);
    //
    //   // 3. update auth route
    //   token.value = loginToken.token;
    //   Object.assign(userInfo, info);
    //
    //   return true;
    // }

    return false
  }

  return {
    token,
    userInfo,
    isLogin,
    loginLoading,
    resetStore,
    login,
    logout
  }
})
