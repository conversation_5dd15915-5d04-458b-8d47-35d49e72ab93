<script lang="ts">
import { computed, defineComponent, h } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useThemeStore } from '@/store/modules/theme'
import { useRouteStore } from '@/store/modules/route'
import HorizontalMenu from '../global-menu/base-menu.vue'
import GlobalLogo from '../global-logo/index.vue'
import GlobalBreadcrumb from '../global-breadcrumb/index.vue'
import { useMixMenuContext } from '../../hooks/use-mix-menu'
import ThemeButton from './components/theme-button.vue'
import NotificationButton from './components/notification-button.vue'
import UserAvatar from './components/user-avatar.vue'
import { createDiscreteApi, NButton, NotificationReactive } from 'naive-ui'
import { fetchManageApiNoticeQueryUnReadCount } from '@/service/api'
import { useIntervalFn } from '@vueuse/core'
import dayjs from 'dayjs'
import { useRouterPush } from '@/hooks/useRouterPush'

const { notification } = createDiscreteApi(['notification'], {
  notificationProviderProps: {
    placement: 'bottom-right'
  }
})

export default defineComponent({
  name: 'GlobalHeader',
  components: {
    // DarkModeContainer is assumed to be globally registered
    GlobalLogo,
    HorizontalMenu,
    GlobalBreadcrumb,
    ThemeButton,
    UserAvatar,
    NotificationButton
  },
  props: {
    showLogo: {
      type: Boolean,
      default: undefined
    },
    showMenuToggler: {
      type: Boolean,
      default: undefined
    },
    showMenu: {
      type: Boolean,
      default: undefined
    }
  },
  setup(props) {
    const appStore = useAppStore()
    const themeStore = useThemeStore()
    const routeStore = useRouteStore()
    const { routerPushByKey } = useRouterPush()
    // const { isFullscreen, toggle } = useFullscreen();
    const { menus } = useMixMenuContext()

    const headerMenus = computed(() => {
      if (themeStore.layout.mode === 'horizontal') {
        return routeStore.menus
      }

      if (themeStore.layout.mode === 'horizontal-mix') {
        return menus.value
      }

      return []
    })

    const notificationIns: Ref<NotificationReactive | null> = ref(null)
    const notificationNum = ref(0)
    const checkUnreadMessages = async () => {
      console.log('开始轮询--->')
      const res = await fetchManageApiNoticeQueryUnReadCount({})
      notificationNum.value = res.data || 0
      if (!res.error && !notificationIns.value && notificationNum.value > 0) {
        notificationIns.value = notification.create({
          type: 'warning',
          title: '消息通知',
          content: `您有${notificationNum.value}条未读消息`,
          meta: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          keepAliveOnHover: true,
          action: () => {
            return h(
              NButton,
              {
                text: true,
                type: 'primary',
                onClick: () => {
                  notificationIns.value?.destroy()
                  notificationIns.value = null
                  routerPushByKey('manage_messages')
                }
              },
              {
                default: () => '去查看'
              }
            )
          },
          onAfterLeave: () => {
            notificationIns.value?.destroy()
            notificationIns.value = null
            pause()
          }
        })
      }
      if (notificationIns.value && notificationNum.value === 0) {
        notificationIns.value?.destroy()
        notificationIns.value = null
      }
      if (notificationIns.value) {
        notificationIns.value.meta = dayjs().format('YYYY-MM-DD HH:mm:ss')
        notificationIns.value.content = `您有${res.data}条未读消息`
      }
    }

    const { pause, resume } = useIntervalFn(checkUnreadMessages, 30000, { immediate: false }) // 30秒轮询一次

    onMounted(() => {
      checkUnreadMessages() // 立即执行一次
      resume() // 开始轮询
    })

    onUnmounted(() => {
      pause() // 停止轮询
      if (notificationIns.value) {
        notificationIns.value?.destroy()
        notificationIns.value = null
      }
    })

    return {
      appStore,
      themeStore,
      routeStore,
      headerMenus,
      notificationNum
    }
  }
})
</script>

<template>
  <DarkModeContainer class="flex-y-center justify-between h-full shadowboder">
    <GlobalLogo
      v-if="showLogo"
      class="h-full"
      :style="{ width: themeStore.sider.width + 'px' }"
    />
    <div v-else></div>
    <!--    <HorizontalMenu v-if="showMenu" mode="horizontal" :menus="headerMenus" class="px-12px" />-->
    <!--    <div v-else class="flex-1-hidden flex-y-center h-full">-->
    <!--      <MenuToggler v-if="showMenuToggler" :collapsed="appStore.siderCollapse" @click="appStore.toggleSiderCollapse" />-->
    <!--      <GlobalBreadcrumb v-if="!appStore.isMobile" class="ml-12px" />-->
    <!--    </div>-->
    <div class="flex-y-center justify-end h-full gap-12px">
      <!--      <FullScreen v-if="!appStore.isMobile" :full="isFullscreen" @click="toggle" />-->
      <!--      <LangSwitch :lang="appStore.locale" :lang-options="appStore.localeOptions" @change-lang="appStore.changeLocale" />-->
      <!--      <ThemeSchemaSwitch-->
      <!--        :theme-schema="themeStore.themeScheme"-->
      <!--        :is-dark="themeStore.darkMode"-->
      <!--        @switch="themeStore.toggleThemeScheme"-->
      <!--      />-->
      <!--      <ThemeButton />-->
      <!--      <NotificationButton :value="notificationNum" />-->
      <UserAvatar />
    </div>
  </DarkModeContainer>
</template>

<style scoped>
.shadowboder {
  background-color: #132e66;
  /* display: flex;
  align-items: center;
  gap: 20px;
  border-bottom: 1px solid var(--55, #ffffff); */
}
</style>
