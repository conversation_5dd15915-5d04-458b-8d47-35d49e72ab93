<script setup lang="ts">
import { computed, ref } from 'vue'
import type { Ref, VNode } from 'vue'
import { useSvgIconRender } from '@yc/hooks'
import { useAuthStore } from '@/store/modules/auth'
import { useRouterPush } from '@/hooks/useRouterPush'
import { $t } from '@/locales'
import SvgIcon from '@/components/custom/svg-icon.vue'
import { getUserInfo } from '@/store/modules/auth/shared'

defineOptions({
  name: 'UserAvatar'
})

const authStore = useAuthStore()
const { routerPushByKey, toLogin } = useRouterPush()
const { SvgIconVNode } = useSvgIconRender(SvgIcon)

function loginOrRegister() {
  toLogin()
}

type DropdownKey = 'change-pwd' | 'logout'

type DropdownOption =
  | {
      key: DropdownKey
      label: string
      icon?: () => VNode
    }
  | {
      type: 'divider'
      key: string
    }

const options = computed(() => {
  const opts: DropdownOption[] = [
    {
      label: '修改密码',
      key: 'change-pwd',
      icon: SvgIconVNode({ icon: 'mdi:account-edit-outline', fontSize: 18 })
    },
    {
      type: 'divider',
      key: 'divider'
    },
    {
      label: $t('common.logout'),
      key: 'logout',
      icon: SvgIconVNode({ icon: 'ph:sign-out', fontSize: 18 })
    }
  ]

  return opts
})

function logout() {
  window.$dialog?.info({
    title: $t('common.tip'),
    content: $t('common.logoutConfirm'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: () => {
      authStore.logout()
    }
  })
}

const resetPwdModalRef: Ref<Expose.YCModal | null> = ref(null)

function handleDropdown(key: DropdownKey) {
  if (key === 'logout') {
    logout()
  } else {
    resetPwdModalRef.value?.open()
  }
}

const userName = ref(getUserInfo().name)
</script>

<template>
  <NButton
    v-if="!authStore.isLogin"
    quaternary
    @click="loginOrRegister"
  >
    {{ $t('page.login.common.loginOrRegister') }}
  </NButton>
  <NDropdown
    v-else
    placement="bottom"
    trigger="click"
    :options="options"
    @select="handleDropdown"
  >
    <div>
      <ButtonIcon
        style="
          --n-text-color: #fff;
          --n-text-color-hover: #fff;
          --n-text-color-pressed: #fff;
          --n-text-color-focus: #fff;
        "
      >
        <!-- <SvgIcon icon="ph:user-circle" class="text-icon-large" /> -->
        <img
          src="@/assets/svg-icon/user.svg"
          alt=""
          class="text-icon-large w-35px rounded-3xl"
        />
        <span class="text-16px font-medium">{{ userName }}</span>
        <!-- <SvgIcon icon="carbon:caret-down" /> -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 16 16"
          class="w-18px"
        >
          <path
            d="M12.6673 5.66699L8.00065 10.3337L3.33398 5.66699"
            stroke="#fff"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </ButtonIcon>
    </div>
  </NDropdown>

  <ResetPwdModal ref="resetPwdModalRef" />
</template>

<style scoped></style>
