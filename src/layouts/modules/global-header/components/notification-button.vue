<script setup lang="ts">
import { useRouterPush } from '@/hooks/useRouterPush'

const props = withDefaults(
  defineProps<{
    value?: number
    max?: number
  }>(),
  {
    value: 0,
    max: 99
  }
)

const { routerPushByKey } = useRouterPush()

const handleToMessage = () => {
  routerPushByKey('manage_messages')
}
</script>

<template>
  <n-badge
    :value="props.value"
    :max="props.max"
  >
    <n-button
      text
      style="font-size: 24px"
      @click="handleToMessage"
    >
      <IconIconamoonNotificationBold class="text-white" />
    </n-button>
  </n-badge>
</template>

<style scoped></style>
