<script setup lang="ts">
import { $t } from '@/locales'
import { useThemeStore } from '@/store/modules/theme'
import GlobalLogo from '../global-logo/index.vue'
const themeStore = useThemeStore()
defineOptions({
  name: 'GlobalLogo'
})

withDefaults(defineProps<Props>(), {
  showTitle: true
})

interface Props {
  /** Whether to show the title */
  showTitle?: boolean
  /** Whether to show the logo */
  showLogo?: App.Global.HeaderProps['showLogo']
  /** Whether to show the menu toggler */
  showMenuToggler?: App.Global.HeaderProps['showMenuToggler']
  /** Whether to show the menu */
  showMenu?: App.Global.HeaderProps['showMenu']
}
</script>

<template>
  <RouterLink
    to="/"
    class="flex-center w-full nowrap-hidden"
  >
    <SystemLogo class="text-44px text-primary" />
    <h2
      v-show="showTitle"
      class="text-16px font-600 text-white transition duration-300 ease-in-out"
    >
      {{ $t('system.title') }}
    </h2>
    <!-- <GlobalLogo
      v-if="showLogo"
      class="h-full"
      :style="{ width: themeStore.sider.width + 'px' }"
    /> -->
  </RouterLink>
</template>

<style lang="scss" scoped></style>
