<!--
 * @Author: yyh <EMAIL>
 * @Date: 2024-10-24 08:42:02
 * @LastEditors: yyh <EMAIL>
 * @LastEditTime: 2024-11-18 10:12:18
 * @FilePath: \szhjj-html-manage\src\layouts\modules\global-content\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useThemeStore } from "@/store/modules/theme";
import { useRouteStore } from "@/store/modules/route";

defineOptions({
  name: "GlobalContent",
});

withDefaults(defineProps<Props>(), {
  showPadding: true,
});

interface Props {
  /** Show padding for content */
  showPadding?: boolean;
}

const appStore = useAppStore();
const themeStore = useThemeStore();
const routeStore = useRouteStore();
</script>

<template>
  <RouterView v-slot="{ Component, route }">
    <Transition
      :name="themeStore.page.animateMode"
      mode="out-in"
      @before-leave="appStore.setContentXScrollable(true)"
      @after-enter="appStore.setContentXScrollable(false)"
    >
      <KeepAlive :include="routeStore.cacheRoutes">
        <component
          :is="Component"
          v-if="appStore.reloadFlag"
          :key="route.path"
          :class="{ 'p-16px': showPadding }"
          class="flex-grow transition-300"
        />
      </KeepAlive>
    </Transition>
  </RouterView>
</template>

<style></style>
