<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { MentionOption, MenuProps } from 'naive-ui'
import { SimpleScrollbar } from '@yc/materials'
import type { RouteKey } from '@elegant-router/types'
import { useAppStore } from '@/store/modules/app'
import { useThemeStore } from '@/store/modules/theme'
import { useRouteStore } from '@/store/modules/route'
import { useRouterPush } from '@/hooks/useRouterPush'

defineOptions({
  name: 'BaseMenu'
})

const props = withDefaults(defineProps<Props>(), {
  mode: 'vertical'
})

interface Props {
  darkTheme?: boolean
  mode?: MenuProps['mode']
  menus: App.Global.Menu[]
}

const route = useRoute()
const appStore = useAppStore()
const themeStore = useThemeStore()
const routeStore = useRouteStore()
const { routerPushByKey } = useRouterPush()

const naiveMenus = computed(() => props.menus as unknown as MentionOption[])

const isHorizontal = computed(() => props.mode === 'horizontal')

const siderCollapse = computed(() => themeStore.layout.mode === 'vertical' && appStore.siderCollapse)

const headerHeight = computed(() => `${themeStore.header.height}px`)

const selectedKey = computed(() => {
  const { hideInMenu, activeMenu } = route.meta
  const name = route.name as string

  const routeName = (hideInMenu ? activeMenu : name) || name

  return routeName
})

const expandedKeys = ref<string[]>([])

function updateExpandedKeys() {
  if (isHorizontal.value || siderCollapse.value || !selectedKey.value) {
    expandedKeys.value = []
    return
  }
  expandedKeys.value = routeStore.getSelectedMenuKeyPath(selectedKey.value)
}

function handleClickMenu(key: RouteKey) {
  routerPushByKey(key)
}

watch(
  () => route.name,
  () => {
    updateExpandedKeys()
  },
  { immediate: true }
)
</script>

<template>
  <SimpleScrollbar>
    <NMenu
      style="--n-item-text-color: #fff; --n-item-icon-color: #fff"
      v-model:expanded-keys="expandedKeys"
      :mode="mode"
      :value="selectedKey"
      :collapsed="siderCollapse"
      :collapsed-width="themeStore.sider.collapsedWidth"
      :collapsed-icon-size="22"
      :options="naiveMenus"
      :inverted="darkTheme"
      :indent="18"
      responsive
      @update:value="handleClickMenu"
    />
  </SimpleScrollbar>
</template>

<style lang="scss" scoped>
:deep(.n-menu) {
  /* 提取公共颜色变量 */
  --primary-color: #2f6cf4;
  --active-bg: #1345aa;
  // --active-bg: linear-gradient(270deg, #4a77f1 2.04%, #7d9aff 100%);
  --active-shadow: 0 4px 12px 0 #3059c14f;
  --hover-bg: #ffffff26;
  --text-active: #fff;
  --icon-active: #4a82f3;

  /* 基础样式 */
  // --n-item-height: v-bind(headerHeight) !important;
  /* 通用内容样式 */
  .n-menu-item-content {
    &::before {
      transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .n-menu-item-content-header,
    .n-menu-item-content__icon {
      color: var(--text-active) !important;
    }
    .n-menu-item-content__arrow {
      color: var(--text-active) !important;
    }
    /* 激活状态通用样式 */
    &--child-active {
      &::before {
        background: var(--active-bg);
        box-shadow: none;
      }

      & .n-menu-item-content__arrow {
        color: var(--icon-active) !important;
      }
    }
    &--selected {
      &::before {
        background: var(--active-bg);
        // box-shadow: var(--active-shadow);
      }
      .n-menu-item-content-header,
      .n-menu-item-content__icon {
        color: var(--text-active) !important;
      }
      .n-menu-item-content__arrow {
        color: var(--icon-active) !important;
      }
    }

    /* 悬停状态 */
    &:not(.n-menu-item-content--disabled):hover {
      &::before {
        background: var(--hover-bg);
      }

      .n-menu-item-content-header,
      .n-menu-item-content__icon,
      .n-menu-item-content__arrow {
        color: var(--text-active);
      }
    }
  }

  /* 子菜单特殊样式 */
  .n-submenu-children {
    .n-menu-item-content--child-active {
      &::before {
        background: var(--hover-bg);
        // box-shadow: none;
      }

      .n-menu-item-content-header,
      .n-menu-item-content__icon,
      .n-menu-item-content__arrow {
        color: var(--primary-color) !important;
      }
    }
  }
  /* 第一个菜单项的特殊处理 */
  > .n-menu-item:first-child {
    .n-menu-item-content {
      &--selected {
        .n-menu-item-content-header,
        .n-menu-item-content__icon {
          color: var(--text-active) !important;
        }
      }

      .n-menu-item-content-header {
        width: 100%;
        color: var(--text-active) !important;
      }
    }
  }

  /* 激活状态指示条 */
  .n-menu-item-content--child-active::after {
    content: '';
    position: absolute;
    z-index: auto;
    left: 8px;
    top: 0px;
    bottom: 0px;
    background: var(--primary-color);
    border-radius: 0 8px 8px 0;
    pointer-events: none;
    width: 4px;
    height: 100%;
  }
}
</style>
