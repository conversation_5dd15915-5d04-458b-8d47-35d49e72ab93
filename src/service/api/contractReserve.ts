import { request } from "../request";

/** 获取承接项目列表分页 */
export function queryProjectPage(params: unknown) {
  return request<any>({
    url: "/project/queryProjectPage ",
    method: "post",
    data: params,
  });
}
/**
 *
 * @param params 新增承接项目
 * @returns
 */
export function addProject(params: unknown) {
  return request<any>({
    url: "/project/addProject",
    method: "post",
    data: params,
  });
}
export function projecthandle(params: unknown) {
  return request<any>({
    url: "/project/handle",
    method: "post",
    data: params,
  });
}
export function stateChange(params: unknown) {
  return request<any>({
    url: "/stateChange/handle",
    method: "post",
    data: params,
  });
}
/**
 *
 * @param params 删除承接项目
 * @returns
 */
export function projectDeleteProject(id: unknown) {
  return request<any>({
    url: "/project/deleteProject",
    method: "post",
    data: { id },
  });
}
//生成承制项目编号
export function projectCreateProjectCode(params: unknown) {
  return request<any>({
    url: "/project/createProjectCode",
    method: "post",
    data: params,
  });
}
//承制项目-更新
export function projectupdateProject(params: unknown) {
  return request<any>({
    url: "/project/updateProject",
    method: "post",
    data: params,
  });
}
//承制项目-详情
export function getProject(params: unknown) {
  return request<any>({
    url: "/project/getProject",
    method: "post",
    data: params,
  });
}
//查询区域以及项目类型字典
export function projectGetProType(params: unknown) {
  return request<any>({
    url: "/project/getProType",
    method: "post",
    data: params,
  });
}
//生成排产计划出厂编号
export function createFactoryNumber(params: unknown) {
  return request<any>({
    url: "/productTask/createFactoryNumber",
    method: "post",
    data: params,
  });
}
//生成成品或零件工号
// {
//   "finishTime": {
//     "year": 1 /**/,
//     "monthValue": 1 /**/,
//     "dayOfMonth": 1 /**/,
//     "month": "DECEMBER" /*：JANUARY|FEBRUARY|MARCH|APRIL|MAY|JUNE|JULY|AUGUST|SEPTEMBER|OCTOBER|NOVEMBER|DECEMBE*/,
//     "dayOfWeek": "SUNDAY" /*：MONDAY|TUESDAY|WEDNESDAY|THURSDAY|FRIDAY|SATURDAY|SUNDA*/,
//     "dayOfYear": 1 /**/
//   }
// }
export function createProductJobCode(params: unknown) {
  return request<any>({
    url: "/productTask/createProductJobCode",
    method: "post",
    data: params,
  });
}
//生成通知单编号

export function createNotificationNumber(params: unknown) {
  return request<any>({
    url: "/productTask/createNotificationNumber ",
    method: "post",
    data: params,
  });
}
//获得排产计划列表分页
export function queryProductTaskPage(params: unknown) {
  return request<any>({
    url: "/productTask/queryProductTaskPage",
    method: "post",
    data: params,
  });
}
//审批 排产
export function productTaskhandle(params: unknown) {
  return request<any>({
    url: "/productTask/handle",
    method: "post",
    data: params,
  });
}
//获得排产计划详情
export function getProductTaskById(params: unknown) {
  return request<any>({
    url: "/productTask/getProductTaskById",
    method: "post",
    data: params,
  });
}
//获取项目信息
export function getTaskProject(params: unknown) {
  return request<any>({
    url: "/productTask/getProject",
    method: "get",
    data: params,
  });
}
//获取产品库信息

export function getProduct(params: unknown) {
  return request<any>({
    url: "/productTask/getProduct",
    method: "get",
    data: params,
  });
}
//获取承制合同信息
export function getTaskContractProject(params: unknown) {
  return request<any>({
    url: "/productTask/getContractProject ",
    method: "get",
    data: params,
  });
}
//新增排产计划
export function addProductTask(params: unknown) {
  return request<any>({
    url: "/productTask/addProductTask",
    method: "post",
    data: params,
  });
}
//获取排产项目及其成品零部件信息
export function getProjectEndAndParts(params: unknown) {
  return request<any>({
    url: "/contractProject/getProjectEndAndParts",
    method: "get",
    data: params,
  });
}
//修改
export function updateProductTask(params: unknown) {
  return request<any>({
    url: "/productTask/updateProductTask",
    method: "post",
    data: params,
  });
}
//删除
export function deleteProductTask(params: unknown) {
  return request<any>({
    url: "/productTask/deleteProductTask",
    method: "post",
    data: params,
  });
}
//根据成品id删除
export function deleteEndProduct(params: unknown) {
  return request<any>({
    url: "/productTask/deleteEndProduct",
    method: "post",
    data: params,
  });
}
//根据零件id删
export function deleteComponentPart(params: unknown) {
  return request<any>({
    url: "/productTask/deleteComponentPart",
    method: "post",
    data: params,
  });
}
// {
//   "id": 1 /*主键id*/,
//   "projectId": 1 /*承制项目表id*/,
//   "contractProjectId": 1 /*合同业务id*/,
//   "pplJj": "F_JJ" /*是否军检（页面选择）：T_JJ|F_J*/,
//   "taskType": "WHK_TASK" /*排产类型：ZS_TASK|LW_TASK|BC_TASK|WHK_TAS*/,
//   "contractType": "contractType" /*合同类型*/,
//   "projectCode": "projectCode" /*项目号*/,
//   "contractCode": "contractCode" /*合同号*/,
//   "contractName": "contractName" /*合同名称*/,
//   "orderName": "orderName" /*订购单位*/,
//   "money": "money" /*合同金额（元）*/,
//   "remark": "remark" /*合同要求摘要*/,
//   "notificationNumber": "notificationNumber" /*通知单编号*/,
//   "taskTime": "2024-10-31 15:49:49" /*排产交货期*/,
//   "fileList": [
//     {
//       "fileUrl": "fileUrl" /*文件地址*/,
//       "fileSize": 1 /*文件大小[可选]*/
//     }
//   ],
//   "endProDuctItemList": [
//     {
//       "id": 1 /*主键id*/,
//       "contractProjectId": 1 /*业务id*/,
//       "productId": 1 /*产品id（产品库选择）*/,
//       "productCode": "productCode" /*工号*/,
//       "productDate": "2024-10-28 13:51:32" /*交货期*/,
//       "measurement": "measurement" /*计量单位*/,
//       "factoryCode": "factoryCode" /*出厂编号*/,
//       "productNumber": "productNumber" /*数量*/,
//       "productJj": "F_JJ" /*是否军检：T_JJ|F_J*/,
//       "remark": "remark" /*备注*/
//     }
//   ],
//   "componentPartsItemList": [
//     {
//       "id": 1 /*主键id*/,
//       "contractProjectId": 1 /*业务id*/,
//       "partInformationId": 1 /*零件id*/,
//       "jobCode": "jobCode" /*工号*/,
//       "partCode": "partCode" /*零件编号*/,
//       "partType": "partType" /*零件型号（物流编码库选择）*/,
//       "partName": "partName" /*零件名称*/,
//       "partDate": "2024-10-28 13:51:32" /*交货期*/,
//       "measurement": "measurement" /*计量单位*/,
//       "partNumber": "partNumber" /*数量*/,
//       "partJj": "F_JJ" /*是否军检：T_JJ|F_J*/,
//       "contractType": 1 /*合同类型*/,
//       "remark": "remark" /*备注*/
//     }
//   ]
// }

//承制合同评审 /manage-api/contractManufacture/getContractManufacturePage
//获得承制合同评审列表分页
export function getContractManufacturePage(params: unknown) {
  return request<any>({
    url: "/contractManufacture/getContractManufacturePage",
    method: "post",
    data: params,
  });
}
//获得承制合同评审详情
export function getContractManufacture(params: unknown) {
  return request<any>({
    url: "/contractManufacture/getContractManufacture",
    method: "post",
    data: params,
  });
}

//评审审批
export function contractManufacturehandle(params: unknown) {
  return request<any>({
    url: "/contractManufacture/handle",
    method: "post",
    data: params,
  });
}
//获取项目信息
export function getContractProject(params: unknown) {
  return request<any>({
    url: "/contractManufacture/getProject",
    method: "get",
    data: params,
  });
}
//新增承制合同评审
export function addContractManufacture(params: unknown) {
  return request<any>({
    url: "/contractManufacture/addContractManufacture",
    method: "post",
    data: params,
  });
}
// {
//   "id": 1 /*主键id*/,
//   "contractJj": "F_JJ" /*是否军检：T_JJ|F_J*/,
//   "contractType": "contractType" /*合同类型*/,
//   "projectId": 1 /*承制项目表id*/,
//   "projectCode": "projectCode" /*承制项目编号*/,
//   "produceType": "GAI_PRODUCT" /*产品类型：END_PRODUCT|NEW_PRODUCT|GAI_PRODUC*/,
//   "contractCode": "contractCode" /*合同编号*/,
//   "contractName": "contractName" /*合同名称*/,
//   "makeName": "makeName" /*承制单位*/,
//   "orderName": "orderName" /*订购单位*/,
//   "orderJd": "orderJd" /*订购单位监管军代室*/,
//   "contractTime": "2024-10-29 17:49:27" /*申请日期*/,
//   "contractUserName": "contractUserName" /*申请人*/,
//   "contractMoney": "contractMoney" /*合同金额（元）*/,
//   "contractDateil": "contractDateil" /*合同要求摘要*/,
//   "fileList": [
//     {
//       "fileUrl": "fileUrl" /*文件地址*/,
//       "fileSize": 1 /*文件大小[可选]*/
//     }
//   ]
// }

//修改
export function updateContractManufacture(params: unknown) {
  return request<any>({
    url: "/contractManufacture/updateContractManufacture",
    method: "post",
    data: params,
  });
}
//产品库下拉
export function queryProductName(params: unknown) {
  return request<any>({
    url: "/check/queryProductName",
    method: "post",
    data: params,
  });
}
export function queryLatestProductName(params: unknown) {
  return request<any>({
    url: "/check/queryLatestProductName",
    method: "post",
    data: params,
  });
}
//删除
export function deleteContractManufacture(params: unknown) {
  return request<any>({
    url: "/contractManufacture/deleteContractManufacture",
    method: "post",
    data: params,
  });
}
//合同标的新增

export function addContractMatter(params: unknown) {
  return request<any>({
    url: "/contractManufacture/addContractMatter",
    method: "post",
    data: params,
  });
}
// {
//   "id": 1 /*主键id*/,
//   "contractProjectId": 1 /*合同id*/,
//   "productId": 1 /*产品id（产品库选择）*/,
//   "cobjNumber": "cobjNumber" /*数量*/,
//   "cobjPrice": "cobjPrice" /*单价（元）*/,
//   "cobjMoney": "cobjMoney" /*价格小计（元）*/,
//   "productType": "productType" /*产品类型*/,
//   "product_name": "product_name" /*产品名称*/,
//   "remark": "remark" /*备注*/
// }

//根据合同id查询合同标的

export function getContractMatter(params: unknown) {
  return request<any>({
    url: "/contractManufacture/getContractMatter",
    method: "post",
    data: params,
  });
}
export function deleteContractMatter(params: unknown) {
  return request<any>({
    url: "/contractManufacture/deleteContractMatter",
    method: "post",
    data: params,
  });
}
//履约 addPerformancePlan
export function addPerformancePlan(params: unknown) {
  return request<any>({
    url: "/contractManufacture/addPerformancePlan",
    method: "post",
    data: params,
  });
}
export function getPerformancePlan(params: unknown) {
  return request<any>({
    url: "/contractManufacture/getPerformancePlan",
    method: "post",
    data: params,
  });
}
export function deletePerformancePlan(params: unknown) {
  return request<any>({
    url: "/contractManufacture/deletePerformancePlan",
    method: "post",
    data: params,
  });
}
// {
//   "id": 1 /*主键id*/,
//   "contratMatterId": 1 /*合同标的表id*/,
//   "number": "number" /*数量*/,
//   "plandate": "2024-10-28 13:51:32" /*交付日期*/,
//   "planUser": "planUser" /*履约责任人*/,
//   "remark": "remark" /*备注*/
// }

//付款 addPayment
export function addPayment(params: unknown) {
  return request<any>({
    url: "/contractManufacture/addPayment",
    method: "post",
    data: params,
  });
}
export function getPayment(params: unknown) {
  return request<any>({
    url: "/contractManufacture/getPayment",
    method: "post",
    data: params,
  });
}
export function deletePayment(params: unknown) {
  return request<any>({
    url: "/contractManufacture/deletePayment",
    method: "post",
    data: params,
  });
}
// {
//   "id": 1 /*主键id*/,
//   "contractProjectId": 1 /*合同表id*/,
//   "payNode": "payNode" /*付款节点名称*/,
//   "payMoney": "payMoney" /*付款金额（元）*/,
//   "payProport": "payProport" /*付款比例（%）*/,
//   "payDate": "2024-10-28 13:51:32" /*截止日期*/,
//   "remark": "remark" /*备注*/
// }

// 合同材料 addContractMaterial
export function addContractMaterial(params: unknown) {
  return request<any>({
    url: "/contractManufacture/addContractMaterial",
    method: "post",
    data: params,
  });
}
export function getContractMaterial(params: unknown) {
  return request<any>({
    url: "/contractManufacture/getContractMaterial",
    method: "post",
    data: params,
  });
}
export function deleteContractMaterial(params: unknown) {
  return request<any>({
    url: "/contractManufacture/deleteContractMaterial",
    method: "post",
    data: params,
  });
}
// {
//   "id": 1 /*主键id*/,
//   "contractProjectId": 1 /*合同表id*/,
//   "archivesMaterialId": 1 /*档案资料库id*/,
//   "name": "name" /*文件名称*/,
//   "number": "number" /*数量要求*/,
//   "format": "format" /*格式要求*/,
//   "remark": "remark" /*备注说明*/
// }

//承制合同管理
export function getContractProjectPage(params: unknown) {
  return request<any>({
    url: "/contractProject/getContractProjectPage",
    method: "post",
    data: params,
  });
}
//合同产品信息
export function queryContract(params: unknown) {
  return request<any>({
    url: "/common/queryContract",
    method: "post",
    data: params,
  });
}
export function addContractProject(params: unknown) {
  return request<any>({
    url: "/contractProject/addContractProject",
    method: "post",
    data: params,
  });
}
export function updContractProject(params: unknown) {
  return request<any>({
    url: "/contractProject/updateContractProject",
    method: "post",
    data: params,
  });
}
export function getContractProjectInfo(params: unknown) {
  return request<any>({
    url: "/contractProject/getContractProject",
    method: "post",
    data: params,
  });
}
export function deleteContractProject(params: unknown) {
  return request<any>({
    url: "/contractProject/deleteContractProject",
    method: "post",
    data: params,
  });
}
// /manage-api/contractProject/getContractProjectPage
// {
//   "contractName": "contractName" /*合同名称*/,
//   "orderName": "orderName" /*订购单位*/,
//   "contractCode": "contractCode" /*合同编号*/,
//   "contractType": "contractType" /*合同类型*/,
//   "pageNo": 1 /*当前页码*/,
//   "pageSize": 1 /*分页大小*/
// }

// /manage-api/contractProject/addContractProject
// {
//   "id": 1 /*主键id*/,
//   "contractJj": "F_JJ" /*是否军检：T_JJ|F_J*/,
//   "isProduce": "LW_TASK" /*排产类型：ZS_TASK|LW_TAS*/,
//   "contractType": "contractType" /*合同类型*/,
//   "projectId": 1 /*承制项目表id*/,
//   "projectCode": "projectCode" /*承制项目编号*/,
//   "produceType": "GAI_PRODUCT" /*产品类型：END_PRODUCT|NEW_PRODUCT|GAI_PRODUC*/,
//   "contractCode": "contractCode" /*合同编号*/,
//   "contractName": "contractName" /*合同名称*/,
//   "makeName": "makeName" /*承制单位*/,
//   "orderName": "orderName" /*订购单位*/,
//   "orderJd": "orderJd" /*订购单位监管军代室*/,
//   "contractTime": "2024-10-30 16:17:18" /*签署日期*/,
//   "contractUserName": "contractUserName" /*签署人*/,
//   "contractMoney": "contractMoney" /*合同金额（元）*/,
//   "contractDateil": "contractDateil" /*合同要求摘要*/,
//   "fileList": [
//     {
//       "fileUrl": "fileUrl" /*文件地址*/,
//       "fileSize": 1 /*文件大小[可选]*/
//     }
//   ]
// }

//成品 /manage-api/contractProject/addEndProduct
// {
//   "contractProjectId": 1 /*承制合同id*/,
//   "endProDuctItemList": [
//     {
//       "id": 1 /*主键id*/,
//       "contractProjectId": 1 /*业务id*/,
//       "productId": 1 /*产品id（产品库选择）*/,
//       "productCode": "productCode" /*工号*/,
//       "productDate": "2024-10-30 16:17:18" /*交货期*/,
//       "measurement": "measurement" /*计量单位*/,
//       "factoryCode": "factoryCode" /*出厂编号*/,
//       "productNumber": "productNumber" /*数量*/,
//       "productJj": "F_JJ" /*是否军检：T_JJ|F_J*/,
//       "remark": "remark" /*备注*/
//     }
//   ]
// }
//成品
//deleteEndProduct
//零部件 addComponentParts
// {
//   "contractProjectId": 1 /*承制合同id*/,
//   "componentPartsItemList": [
//     {
//       "id": 1 /*主键id*/,
//       "contractProjectId": 1 /*业务id*/,
//       "partInformationId": 1 /*零件id*/,
//       "jobCode": "jobCode" /*工号*/,
//       "partCode": "partCode" /*零件编号*/,
//       "partType": "partType" /*零件型号（物流编码库选择）*/,
//       "partName": "partName" /*零件名称*/,
//       "partDate": "2024-10-30 16:17:18" /*交货期*/,
//       "measurement": "measurement" /*计量单位*/,
//       "partNumber": "partNumber" /*数量*/,
//       "partJj": "F_JJ" /*是否军检：T_JJ|F_J*/,
//       "contractType": 1 /*合同类型*/,
//       "remark": "remark" /*备注*/
//     }
//   ]
// }

//付款 addPayment
// {
//   "id": 1 /*主键id*/,
//   "contractProjectId": 1 /*合同表id*/,
//   "payNode": "payNode" /*付款节点名称*/,
//   "payMoney": "payMoney" /*付款金额（元）*/,
//   "payProport": "payProport" /*付款比例（%）*/,
//   "payDate": "2024-10-30 16:17:18" /*截止日期*/,
//   "remark": "remark" /*备注*/
// }

//计划材料 addContractMaterial

// {
//   "id": 1 /*主键id*/,
//   "contractProjectId": 1 /*合同表id*/,
//   "archivesMaterialId": 1 /*档案资料库id*/,
//   "name": "name" /*文件名称*/,
//   "number": "number" /*数量要求*/,
//   "format": "format" /*格式要求*/,
//   "remark": "remark" /*备注说明*/
// }

//获得承制单位任务列表分页
export function queryTaskPage(params: unknown) {
  return request<any>({
    url: "/organizaTask/queryTaskPage",
    method: "post",
    data: params,
  });
}
export function addOrganizeTask(params: unknown) {
  return request<any>({
    url: "/organizaTask/addOrganizeTask",
    method: "post",
    data: params,
  });
}
// {
//   "id": 1 /*承制单位任务主键id*/,
//   "contractProjectId": 1 /*承制合同id*/,
//   "contractCode": "contractCode" /*合同编号*/,
//   "contractName": "contractName" /*合同名称*/,
//   "orderName": "orderName" /*订购单位*/,
//   "orderJd": "orderJd" /*订购单位监管军代室*/,
//   "taskCode": "taskCode" /*监管协议编号*/,
//   "taskName": "taskName" /*监管协议名称*/,
//   "taskType": "taskType" /*任务类型（字典）*/,
//   "taskTime": "2024-11-04 15:05:40" /*接收时间*/,
//   "addSuperviseList": [
//     {
//       "fileUrl": "fileUrl" /*文件地址*/,
//       "fileSize": 1 /*文件大小[可选]*/
//     }
//   ],
//   "addEffectList": [
//     {
//       "fileUrl": "fileUrl" /*文件地址*/,
//       "fileSize": 1 /*文件大小[可选]*/
//     }
//   ]
// }
export function updateOrganizeTask(params: unknown) {
  return request<any>({
    url: "/organizaTask/updateOrganizeTask",
    method: "post",
    data: params,
  });
}
export function deleteOrganizeTask(params: unknown) {
  return request<any>({
    url: "/organizaTask/deleteOrganizeTask",
    method: "post",
    data: params,
  });
}
export function getOrganizeTask(params: unknown) {
  return request<any>({
    url: "/organizaTask/getOrganizeTask",
    method: "post",
    data: params,
  });
}
//获取承制合同信息(承制单位任务)
export function getContract(params: unknown) {
  return request<any>({
    url: "/organizaTask/getContract",
    method: "get",
    data: params,
  });
}
//获取承制合同信息(供方单位任务)
export function getcompanContract(params: unknown) {
  return request<any>({
    url: "/companyTask/getContract ",
    method: "get",
    data: params,
  });
}
//获取采购合同信息(供方单位任务)

export function getPurchaseContract(params: unknown) {
  return request<any>({
    url: "/companyTask/getPurchaseContract",
    method: "get",
    data: params,
  });
}

export function addCompanyTask(params: unknown) {
  return request<any>({
    url: "/companyTask/addCompanyTask",
    method: "post",
    data: params,
  });
}
export function updateCompanyTask(params: unknown) {
  return request<any>({
    url: "/companyTask/updateCompanyTask",
    method: "post",
    data: params,
  });
}
export function deleteCompanyTask(params: unknown) {
  return request<any>({
    url: "/companyTask/deleteCompanyTask ",
    method: "post",
    data: params,
  });
}
export function querycompanyTaskPage(params: unknown) {
  return request<any>({
    url: "/companyTask/queryTaskPage",
    method: "post",
    data: params,
  });
}
export function getCompanyTask(params: unknown) {
  return request<any>({
    url: "/companyTask/getCompanyTask ",
    method: "post",
    data: params,
  });
}
// {
//   "id": 1 /*主键id*/,
//   "contractProjectId": 1 /*承制合同id*/,
//   "purchaseContractId": 1 /*采购合同id*/,
//   "purchaseCode": "purchaseCode" /*采购合同编号*/,
//   "purchaseName": "purchaseName" /*采购合同名称*/,
//   "companyName": "companyName" /*供方单位名称*/,
//   "companyJd": "companyJd" /*供方单位监管军代室*/,
//   "contractCode": "contractCode" /*承制合同编号*/,
//   "contractName": "contractName" /*承制合同名称*/,
//   "taskName": "taskName" /*监管协议名称*/,
//   "taskType": "taskType" /*任务类型*/,
//   "taskTime": "2024-11-04 15:05:40" /*发送时间*/,
//   "addSuperviseList": [
//     {
//       "fileUrl": "fileUrl" /*文件地址*/,
//       "fileSize": 1 /*文件大小[可选]*/
//     }
//   ],
//   "updateSuperviseList": [
//     {
//       "id": 1 /*附件Id*/,
//       "fileUrl": "fileUrl" /*文件地址*/,
//       "fileName": "fileName" /*文件名*/,
//       "fileType": "OTHER" /*文件类型：IMAGE|VIDEO|WORD|EXCEL|PPT|PDF|OTHE*/,
//       "fileSize": 1 /*文件大小*/,
//       "fileBusiness": "PROJECT_PLANNER_BUSINESS" /*业务类型：CONTRACT_MANUFACTURE_BUSINESS|PRODUCT_TASK_BUSINESS|CONTRACT_PROJECT_BUSINESS|PROJECT_FORECAST_BUSINESS|CHECK_RULES_BUSINESS|QUALITY_DEVIATION_BUSINESS|QUALITY_OVER_BUSINESS|QUALITY_ASSURANCE_BUSINESS|QUALITY_RISK_BUSINESS|QUALITY_CHANG_BUSINESS|QUALITY_UNQUALIFIED_BUSINESS|COMPANY_BUSINESS|QUALITY_SUPERVISE_BUSINESS|CHECK_APPLY_BUSINESS|QUALITY_COST_BUSINESS|QUALITY_MATING_BUSINESS|QUALITY_PURCHASE_BUSINESS|TASK_SUPERVISE_BUSINESS|TASK_EFFECT_BUSINESS|COMPANY_TASK_SUPERVISE_BUSINESS|PROJECT_PLANNER_BUSINES*/
//     }
//   ]
// }

//新增项目策划及审批

export function addProjectPlanner(params: unknown) {
  return request<any>({
    url: "/projectPlanner/addProjectPlanner",
    method: "post",
    data: params,
  });
}
export function getProjectPlannerPage(params: unknown) {
  return request<any>({
    url: "/projectPlanner/getProjectPlannerPage",
    method: "post",
    data: params,
  });
}
export function getProjectPlannerDetail(params: unknown) {
  return request<any>({
    url: "/projectPlanner/getProjectPlannerDetail",
    method: "post",
    data: params,
  });
}
export function updateProjectPlanner(params: unknown) {
  return request<any>({
    url: "/projectPlanner/updateProjectPlanner",
    method: "post",
    data: params,
  });
}
export function deleteProjectPlanner(params: unknown) {
  return request<any>({
    url: "/projectPlanner/deleteProjectPlanner ",
    method: "post",
    data: params,
  });
}
export function handlerProjectPlanner(params: unknown) {
  return request<any>({
    url: "/projectPlanner/handle",
    method: "post",
    data: params,
  });
}
// {
//   "id": 1 /*项目审批策划主键id*/,
//   "contractProjectId": 1 /*承制合同id*/,
//   "contractCode": "contractCode" /*合同编号*/,
//   "contractName": "contractName" /*合同名称*/,
//   "orderName": "orderName" /*订购单位*/,
//   "orderJd": "orderJd" /*订购单位监管军代室*/,
//   "plamTime": "2024-11-04 15:49:05" /*创建时间*/,
//   "produceModel": "produceModel" /*产品型号*/,
//   "produceName": "produceName" /*产品名称*/,
//   "projectBackground": "projectBackground" /*项目背景*/,
//   "consistOf": "consistOf" /*组成*/,
//   "functionalInterface": "functionalInterface" /*功能接口*/,
//   "electricalInterface": "electricalInterface" /*电气接口*/,
//   "physicalInterface": "physicalInterface" /*物理接口*/,
//   "image": "image" /*功能流程图*/,
//   "majorFunction": "majorFunction" /*主要功能*/,
//   "jobScope": "jobScope" /*工作范围*/,
//   "status": "END_STATUS" /*审核状态（枚举）：NEW_STATUS|END_STATU*/,
//   "fileList": [
//     {
//       "fileUrl": "fileUrl" /*文件地址*/,
//       "fileSize": 1 /*文件大小[可选]*/
//     }
//   ],
//   "technicalItemList": [
//     {
//       "id": 1 /*战术性能主键id*/,
//       "projectPlannerId": 1 /*项目策划审批表表id*/,
//       "workCondit": "workCondit" /*工况*/,
//       "internetTraffic": "internetTraffic" /*流量（lmin）*/,
//       "exportPressure": "exportPressure" /*出口压力（MPa）*/
//     }
//   ],
//   "workProgressItemList": [
//     {
//       "id": 1 /*任务进度要求主键id*/,
//       "projectPlannerId": 1 /*项目策划审批表表id*/,
//       "workName": "workName" /*任务名称*/,
//       "progress": "progress" /*进度要求*/
//     }
//   ],
//   "deliverablesItemList": [
//     {
//       "id": 1 /*交付备件清单主键id*/,
//       "projectPlannerId": 1 /*项目策划审批表表id*/,
//       "type": "type" /*型号*/,
//       "name": "name" /*名称*/,
//       "number": 1 /*数量*/,
//       "measurement": "measurement" /*计量单位*/
//     }
//   ],
//   "projectMembersItemList": [
//     {
//       "id": 1 /*项目组人员及职责主键id*/,
//       "projectPlannerId": 1 /*项目策划审批表表id*/,
//       "name": "name" /*姓名*/,
//       "task": "task" /*承担任务*/
//     }
//   ],
//   "stageItemList": [
//     {
//       "id": 1 /*设计阶段主键id*/,
//       "projectPlannerId": 1 /*项目策划审批表id*/,
//       "stageTask": "stageTask" /*任务内容*/,
//       "stageName": "stageName" /*责任人*/,
//       "stageTime": "2024-11-04 15:49:05" /*任务实施周期*/,
//       "stageType": "EQUIP_STAGE" /*阶段类型枚举：1设计阶段、2制造阶段、3装配试验及发货阶段：DESIGN_STAGE|MANUFACTURING_STAGE|EQUIP_STAG*/
//     }
//   ],
//   "outResourcesItemList": [
//     {
//       "id": 1 /*外部资源需求主键id*/,
//       "projectPlannerId": 1 /*项目策划审批表表id*/,
//       "partName": "partName" /*零部件名称*/,
//       "processesName": "processesName" /*工序名称*/,
//       "outsourcedName": "outsourcedName" /*外购厂家*/
//     }
//   ]
// }
/**
 * 获取承制合同列表
 *
 * @param params
 */
export function fetchQueryContractProjectList(params: unknown) {
  return request<any>({
    url: "/organizaTask/getContract",
    method: "get",
    data: params,
  });
}
//采购计划新增
export function addPurchasePlan(params: unknown) {
  return request<any>({
    url: "/purchasePlan/addPurchasePlan",
    method: "post",
    data: params,
  });
}
export function createPurchasePlanCode(params: unknown) {
  return request<any>({
    url: "/purchasePlan/createPurchasePlanCode",
    method: "post",
    data: params,
  });
}
export function getPurchasePlan(params: unknown) {
  return request<any>({
    url: "/purchasePlan/getPurchasePlan",
    method: "post",
    data: params,
  });
}
export function updatePurchasePlan(params: unknown) {
  return request<any>({
    url: "/purchasePlan/updatePurchasePlan",
    method: "post",
    data: params,
  });
}
export function deletePurchasePlan(params: unknown) {
  return request<any>({
    url: "/purchasePlan/deletePurchasePlan",
    method: "post",
    data: params,
  });
}
export function getPurchasePlanPage(params: unknown) {
  return request<any>({
    url: "/purchasePlan/getPurchasePlanPage",
    method: "post",
    data: params,
  });
}
//获取排产计划
export function getProductTaskCode(params: unknown) {
  return request<any>({
    url: "/purchasePlan/getProductTaskCode",
    method: "post",
    data: params,
  });
}
//获取采购员
export function getOrgUser(params: unknown) {
  return request<any>({
    url: "/purchasePlan/getOrgUser",
    method: "post",
    data: params,
  });
}

//采购合同管理

export function addPurchaseContract(params: unknown) {
  return request<any>({
    url: "/purchaseContract/addPurchaseContract",
    method: "post",
    data: params,
  });
}
export function updatePurchaseContract(params: unknown) {
  return request<any>({
    url: "/purchaseContract/updatePurchaseContract",
    method: "post",
    data: params,
  });
}
export function getPurchaseContractPage(params: unknown) {
  return request<any>({
    url: "/purchaseContract/getPurchaseContractPage",
    method: "post",
    data: params,
  });
}
export function getPurchaseContractList(params: unknown) {
  return request<any>({
    url: "/purchaseContract/getPurchaseContract",
    method: "post",
    data: params,
  });
}
export function queryUnFinishPurchaseContractPage(params: unknown) {
  return request<any>({
    url: "/purchaseContract/queryUnFinishPurchaseContractPage",
    method: "post",
    data: params,
  });
}
export function getPurchasePartsById(params: unknown) {
  return request<any>({
    url: "/purchaseContract/getPurchasePartsById",
    method: "post",
    data: params,
  });
}
//获取项目号
export function getPurchaseCode(params: unknown) {
  return request<any>({
    url: "/purchaseContract/getPurchaseCode",
    method: "get",
    data: params,
  });
}
export function getPurchaseContractById(params: unknown) {
  return request<any>({
    url: "/purchaseContract/getPurchaseContractById",
    method: "post",
    data: params,
  });
}
export function deletePurchaseContract(params: unknown) {
  return request<any>({
    url: "/purchaseContract/deletePurchaseContract",
    method: "post",
    data: params,
  });
}
//获取供方单位
export function getCompany(params: unknown) {
  return request<any>({
    url: "/purchaseContract/getCompany",
    method: "get",
    data: params,
  });
}
//获取承制单位
export function getOrganiza(params: unknown) {
  return request<any>({
    url: "/organiza/getOrganiza",
    method: "post",
    data: params,
  });
}
//物料库
export function queryMaterialPageList(params: unknown) {
  return request<any>({
    url: "/materialWarehouse/queryMaterialPageList",
    method: "post",
    data: params,
  });
}
//工作台-合同进展情况

export function workBenchContract(params: unknown) {
  return request<any>({
    url: "/workBench/queryContract",
    method: "post",
    data: params,
  });
}
//根据合同id获取工号
export function getStrCode(params: unknown) {
  return request<any>({
    url: "/purchasePlan/getStrCode",
    method: "post",
    data: params,
  });
}
//根据工号获取物料列表
export function getMaterialWarehouse(params: unknown) {
  return request<any>({
    url: "/purchasePlan/getMaterialWarehouse",
    method: "post",
    data: params,
  });
}
//待办 /flow/execute/toDoPage
export function toDoPage(params: unknown) {
  return request<any>({
    url: "/flow/execute/toDoPage",
    method: "post",
    data: params,
  });
}
export function donePage(params: unknown) {
  return request<any>({
    url: "/flow/execute/donePage",
    method: "post",
    data: params,
  });
}
//查询工作流节点 flow/execute/anyNodeList
export function anyNodeList(params: unknown) {
  return request<any>({
    url: "/flow/execute/anyNodeList/" + params,
    method: "get",
  });
}
// 查询流程图flowchart
export function flowchart(params: unknown) {
  return request<any>({
    url: "/flow/definition/flowChart/" + params,
    method: "get",
  });
}
export function getBackNodeList(params: unknown) {
  return request<any>({
    url: "/flow/execute/getBackNodeList/" + params,
    method: "get",
  });
}
//采购计划审批
export function PurchasePlanhandle(params: unknown) {
  return request<any>({
    url: "/purchasePlan/handle",
    method: "post",
    data: params,
  });
}
//已办按月统计 flow/execute/countDoneEveryMonth
export function countDoneEveryMonth(params: unknown) {
  return request<any>({
    url: "/flow/execute/countDoneEveryMonth",
    method: "post",
    data: params,
  });
}
//统计已办认为
export function countDoneThisMonthAndToday(params: unknown) {
  return request<any>({
    url: "/flow/execute/countDoneThisMonthAndToday",
    method: "post",
    data: params,
  });
}
export function getOrgCertificateStatus(params: unknown) {
  return request<any>({
    url: "/workBench/getOrgCertificateStatus",
    method: "post",
    data: params,
  });
}
//审批历史
export function getdoneList(params: unknown) {
  return request<any>({
    url: "/flow/execute/doneList/" + params,
    method: "get",
  });
}
export function flowChartNoColor(params: unknown) {
  return request<any>({
    url: "/flow/definition/flowChartNoColor/" + params,
    method: "get",
  });
}
export function companyBYName(params: unknown) {
  return request<any>({
    url: "/company/companyBYName",
    method: "post",
    data: params,
  });
}
export function mesContract(params: unknown) {
  return request<any>({
    url: "/contractProject/mesContract",
    method: "post",
    data: params,
  });
}
//项目进度
export function getProgressInformation(params: unknown) {
  return request<any>({
    url: "/mes/getProgressInformation",
    method: "post",
    data: params,
  });
}
//备注
export function updateContractArchivesDataRemark(params: unknown) {
  return request<any>({
    url: "/contractArchivesData/updateContractArchivesDataRemark",
    method: "post",
    data: params,
  });
}
export function createRecCode(params: unknown) {
  return request<any>({
    url: "/receipt/createRecCode",
    method: "post",
    data: params,
  });
}
export function mesProductTask(params: unknown) {
  return request<any>({
    url: "/productTask/mesProductTask",
    method: "post",
    data: params,
  });
}
export function mesPickLibrary(params: unknown) {
  return request<any>({
    url: "/pickLibrary/mesPickLibrary",
    method: "post",
    data: params,
  });
}
export function getProductForParts(params: unknown) {
  return request<any>({
    url: "/mes/getProductForParts",
    method: "post",
    data: params,
  });
}
export function exportContractManufacture(params: unknown) {
  return request<any>({
    url: "/contractManufacture/exportContractManufacture",
    method: "post",
    data: params,
  });
}

//售后批量操作 aftermarket/updateStatusByIds
export function updateStatusByIds(params: unknown) {
  return request<any>({
    url: "/aftermarket/updateStatusByIds",
    method: "post",
    data: params,
  });
}
//成台泵导入
export function importEndProduct(params: unknown) {
  return request<any>({
    url: "/contractProject/importEndProduct",
    method: "post",
    data: params,
  });
}
//采购员列表
export function getPurchaseUser(params: unknown) {
  return request<any>({
    url: "/purchasePlan/getPurchaseUser",
    method: "post",
    data: params,
  });
}
//采购合同 purchaseContract/createPurchaseContractCode
export function createPurchaseContractCode(params: unknown) {
  return request<any>({
    url: "/purchaseContract/createPurchaseContractCode",
    method: "post",
    data: params,
  });
}
//承制合同评审 contractManufacture/getProject
export function contractManufacturegetProject(params: unknown) {
  return request<any>({
    url: "/contractManufacture/getProject",
    method: "post",
    data: params,
  });
}
export function contractProjectgetProject(params: unknown) {
  return request<any>({
    url: "/contractProject/getProject",
    method: "post",
    data: params,
  });
}
//质保大纲导出
export function exportQualityAssurance(params: unknown) {
  return request<any>({
    url: "/qualityAssurance/exportQualityAssurance",
    method: "post",
    data: params,
  });
}
//采购计划
export function exportPurchasePlan(params: unknown) {
  return request<any>({
    url: "/purchasePlan/exportPurchasePlan",
    method: "post",
    data: params,
  });
}
//采购合同导出
export function exportPurchaseContract(params: unknown) {
  return request<any>({
    url: "/purchaseContract/exportPurchaseContract",
    method: "post",
    data: params,
  });
}
// 获取验收项目名称 checkApply/queryInspectItems
export function queryInspectItems(params: unknown) {
  return request<any>({
    url: "/checkApply/queryInspectItems",
    method: "post",
    data: params,
  });
}
//  成台泵批量是否出厂说明
export function updateEndProductByIdsFactory(params: unknown) {
  return request<any>({
    url: "/checkFactory/updateEndProductByIdsFactory",
    method: "post",
    data: params,
  });
}
//零部件批量是否出厂说明
export function updatePartsByIdsFactory(params: unknown) {
  return request<any>({
    url: "/checkFactory/updatePartsByIdsFactory",
    method: "post",
    data: params,
  });
}
//排产计划导出 productTask/exportProductTask
export function exportProductTask(params: unknown) {
  return request<any>({
    url: "/productTask/exportProductTask",
    method: "post",
    data: params,
  });
}
//验收纪实 verificationRecord/add
export function verificationRecordadd(params: unknown) {
  return request<any>({
    url: "/verificationRecord/add",
    method: "post",
    data: params,
  });
}
export function verificationRecordupdate(params: unknown) {
  return request<any>({
    url: "/verificationRecord/update",
    method: "post",
    data: params,
  });
}
export function verificationRecordqueryPage(params: unknown) {
  return request<any>({
    url: "/verificationRecord/queryPage",
    method: "post",
    data: params,
  });
}
export function verificationRecordqueryDetail(params: unknown) {
  return request<any>({
    url: "/verificationRecord/queryDetail",
    method: "post",
    data: params,
  });
}
export function verificationRecorddelete(params: unknown) {
  return request<any>({
    url: "/verificationRecord/delete",
    method: "post",
    data: params,
  });
}
export function reloadMaterialCode(params: unknown) {
  return request<any>({
    url: "/materialWarehouse/reloadMaterialCode",
    method: "post",
    data: params,
  });
}
export function exportCompany(params: unknown) {
  return request<any>({
    url: "/company/exportCompany",
    method: "post",
    data: params,
  });
}
export function deleteCompanyYear(params: unknown) {
  return request<any>({
    url: "/company/deleteCompanyYear",
    method: "post",
    data: params,
  });
}
export function getFlowTask(params: unknown) {
  return request<any>({
    url: "/flow/execute/getFlowTask",
    method: "post",
    data: params,
  });
}
//根据项目查采购编号
export function queryPurchaseContractByContractProjectId(params: unknown) {
  return request<any>({
    url: "/purchaseContract/queryPurchaseContractByContractProjectId",
    method: "post",
    data: params,
  });
}
//批量质量符合性检查
export function batchAddAcceptance(params: unknown) {
  return request<any>({
    url: "/receipt/batchAddAcceptance",
    method: "post",
    data: params,
  });
}
//合格供方-供方单位相关证书-历史记录-查询
export function queryCompanyCertificateYearList(params: unknown) {
  return request<any>({
    url: "/companyCertificate/queryCompanyCertificateYearList",
    method: "post",
    data: params,
  });
}
export function queryOrgCertificateYearList(params: unknown) {
  return request<any>({
    url: "/organiza/queryOrgCertificateYearList",
    method: "post",
    data: params,
  });
}
//合格供方-供方单位相关证书-历史记录-删除
export function deleteOrgCertificateYearList(params: unknown) {
  return request<any>({
    url: "/organiza/deleteOrgCertificateYearList",
    method: "post",
    data: params,
  });
}
export function deleteCompanyCertificateYear(params: unknown) {
  return request<any>({
    url: "/companyCertificate/deleteCompanyCertificateYear",
    method: "post",
    data: params,
  });
}
export function companyCertificatequeryList(params: unknown) {
  return request<any>({
    url: "/companyCertificate/queryList",
    method: "post",
    data: params,
  });
}
export function productTaskqueryList(params: unknown) {
  return request<any>({
    url: "/productTask/queryList",
    method: "post",
    data: params,
  });
}
