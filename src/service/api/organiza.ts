import { request } from '../request';

/** 获取承制单位 */
export function fetchGetOrganiza(params: unknown) {
  return request<any>({
    url: '/organiza/getOrganiza',
    method: 'post',
    data: params
  });
}
/** 承制单位新增附件 */
export function addOrgAttachment(params: unknown) {
  return request<any>({
    url: '/organiza/addOrgAttachment',
    method: 'post',
    data: params
  });
}
/**
 * 承制单位-更新
 *
 * @param params
 */
export function fetchUpdateOrganiza(params: unknown) {
  return request<any>({
    url: '/organiza/updateOrganiza',
    method: 'post',
    data: params
  });
}



/** 承制单位 - 联系人列表 */
export function fetchOrgQueryOrgUserPage(params: unknown) {
  return request<any>({
    url: '/organiza/queryOrgUserPage',
    method: 'post',
    data: params
  });
}

/** 承制单位 - 新增联系人 */
export function fetchAddOrgUser(params: unknown) {
  return request<any>({
    url: '/organiza/addOrgUser',
    method: 'post',
    data: params
  });
}

/** 承制单位 - 编辑联系人 */
export function fetchUpdateOrgUser(params: unknown) {
  return request<any>({
    url: '/organiza/updateOrgUser',
    method: 'post',
    data: params
  });
}


/** 承制单位 - 联系人详情 */
export function fetchGetOrgUserById(id: number) {
  return request<any>({
    url: '/organiza/getOrgUser',
    method: 'post',
    data: { id }
  });
}

/** 承制单位 - 删除联系人 */
export function fetchDeleteOrgUserById(id: number) {
  return request<any>({
    url: '/organiza/deleteOrgUser',
    method: 'post',
    data: { id }
  });
}



/** 承制单位 - 单位资格证书列表 */
export function fetchOrgQueryOrgCertificate(params: unknown) {
  return request<any>({
    url: '/organiza/queryOrgCertificate',
    method: 'post',
    data: params
  });
}



/** 承制单位 - 新增单位资格证书 */
export function fetchAddOrgCertificate(params: unknown) {
  return request<any>({
    url: '/organiza/addOrgCertificate',
    method: 'post',
    data: params
  });
}

/** 承制单位 - 编辑单位资格证书 */
export function fetchUpdateOrgCertificate(params: unknown) {
  return request<any>({
    url: '/organiza/updateOrgCertificate',
    method: 'post',
    data: params
  });
}


/** 承制单位 - 单位资格证书详情 */
export function fetchGetOrgCertificateById(id: number) {
  return request<any>({
    url: '/organiza/getOrgCertificate',
    method: 'post',
    data: { id }
  });
}

/** 承制单位 - 删除单位资格证书 */
export function fetchDeleteOrgCertificateById(id: number) {
  return request<any>({
    url: '/organiza/deleteOrgCertificate',
    method: 'post',
    data: { id }
  });
}



/** 承制单位 - 单位资格情况列表 */
export function fetchOrgQueryOrgQualificate(params: unknown) {
  return request<any>({
    url: '/organiza/queryOrgQualificate',
    method: 'post',
    data: params
  });
}



/** 承制单位 - 新增单位资格情况 */
export function fetchAddOrgQualificate(params: unknown) {
  return request<any>({
    url: '/organiza/addOrgQualificate',
    method: 'post',
    data: params
  });
}


/** 承制单位 - 编辑单位资格情况 */
export function fetchUpdateOrgQualificate(params: unknown) {
  return request<any>({
    url: '/organiza/updateOrgQualificate',
    method: 'post',
    data: params
  });
}


/** 承制单位 - 单位资格情况详情 */
export function fetchGetOrgQualificateById(id: number) {
  return request<any>({
    url: '/organiza/getOrgQualificate',
    method: 'post',
    data: { id }
  });
}

/** 承制单位 - 删除单位资格情况 */
export function fetchDeleteOrgQualificateById(id: number) {
  return request<any>({
    url: '/organiza/deleteOrgQualificate',
    method: 'post',
    data: { id }
  });
}
/** 承制单位 - 附件列表 */
export function getOrgAttachment(params: unknown) {
  return request<any>({
    url: '/organiza/getOrgAttachment',
    method: 'get',
    data: params
  });
}
