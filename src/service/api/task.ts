import { request } from '../request';

/**
 * 任务分工管理 - 列表
 *
 * @param params
 */
export function fetchQueryTask(params: unknown) {
  return request<any>({
    url: '/task/queryTask',
    method: 'post',
    data: params
  });
}

/**
 * 获取承制合同 - 详情
 *
 * @param params
 */
export function fetchQueryContract(params: unknown) {
  return request<any>({
    url: '/common/queryContract',
    method: 'post',
    data: params
  });
}

/**
 * 质保大纲 - 合同列表
 *
 * @param params
 */
export function fetchContractProject(params: unknown) {
  return request<any>({
    url: '/qualityAssurance/selectContractProjectNotInQuaAssurance',
    method: 'post',
    data: params
  });
}

/**
 * 质保大纲 - 列表
 *
 * @param params
 */
export function fetchQueryPageList(params: unknown) {
  return request<any>({
    url: '/qualityAssurance/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 质保大纲 - 新增
 *
 * @param params
 */
export function fetchSaveQuality(params: unknown) {
  return request<any>({
    url: '/qualityAssurance/add',
    method: 'post',
    data: params
  });
}

/**
 * 质保大纲 - 审核
 *
 * @param params
 */
export function fetchSaveQualityHandle(params: unknown) {
  return request<any>({
    url: '/qualityAssurance/handle',
    method: 'post',
    data: params
  });
}

/**
 * 质保大纲 - 删除
 *
 * @param params
 */
export function fetchSaveDelete(params: unknown) {
  return request<any>({
    url: '/qualityAssurance/delete',
    method: 'post',
    data: params
  });
}

/**
 * 质保大纲 - 详情
 *
 * @param params
 */
export function fetchSaveQuery(params: unknown) {
  return request<any>({
    url: '/qualityAssurance/query',
    method: 'post',
    data: params
  });
}

/**
 * 质保大纲 - 详情
 *
 * @param params
 */
export function fetchSaveUpdate(params: unknown) {
  return request<any>({
    url: '/qualityAssurance/update',
    method: 'post',
    data: params
  });
}

/**
 * 技术变更 - 列表
 *
 * @param params
 */
export function fetchQueryList(params: unknown) {
  return request<any>({
    url: '/stateChange/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 技术变更 - 新增
 *
 * @param params
 */
export function fetchStateChangeAdd(params: unknown) {
  return request<any>({
    url: '/stateChange/add',
    method: 'post',
    data: params
  });
}

/**
 * 技术变更 - 编辑
 *
 * @param params
 */
export function fetchStateChangeUpdate(params: unknown) {
  return request<any>({
    url: '/stateChange/update',
    method: 'post',
    data: params
  });
}

/**
 * 技术变更 - 删除
 *
 * @param params
 */
export function fetchStateChangeDelete(params: unknown) {
  return request<any>({
    url: '/stateChange/delete',
    method: 'post',
    data: params
  });
}

/**
 * 技术变更 - 详情
 *
 * @param params
 */
export function fetchStateChangeQuery(params: unknown) {
  return request<any>({
    url: '/stateChange/query',
    method: 'post',
    data: params
  });
}

/**
 * 不合格品 - 列表
 *
 * @param params
 */
export function fetchUnqualifiedList(params: unknown) {
  return request<any>({
    url: '/unqualified/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 不合格品 - 审核
 *
 * @param params
 */
export function fetchUnqualifiedHandle(params: unknown) {
  return request<any>({
    url: '/unqualified/handle',
    method: 'post',
    data: params
  });
}

/**
 * 不合格品 - 新增
 *
 * @param params
 */
export function fetchUnqualifiedAdd(params: unknown) {
  return request<any>({
    url: '/unqualified/add',
    method: 'post',
    data: params
  });
}

/**
 * 不合格品 - 详情
 *
 * @param params
 */
export function fetchUnqualifiedQuery(params: unknown) {
  return request<any>({
    url: '/unqualified/query',
    method: 'post',
    data: params
  });
}

/**
 * 不合格品 - 编辑
 *
 * @param params
 */
export function fetchUnqualifiedUpdate(params: unknown) {
  return request<any>({
    url: '/unqualified/update',
    method: 'post',
    data: params
  });
}

/**
 * 不合格品 - 删除
 *
 * @param params
 */
export function fetchUnqualifiedDelete(params: unknown) {
  return request<any>({
    url: '/unqualified/delete',
    method: 'post',
    data: params
  });
}

/**
 * 偏离许可 - 列表
 *
 * @param params
 */
export function fetchDeviationList(params: unknown) {
  return request<any>({
    url: '/deviationPermission/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 偏离许可 - 新增
 *
 * @param params
 */
export function fetchDeviationAdd(params: unknown) {
  return request<any>({
    url: '/deviationPermission/add',
    method: 'post',
    data: params
  });
}

/**
 * 偏离许可 - 详情
 *
 * @param params
 */
export function fetchDeviationQuery(params: unknown) {
  return request<any>({
    url: '/deviationPermission/query',
    method: 'post',
    data: params
  });
}

/**
 * 偏离许可 - 编辑
 *
 * @param params
 */
export function fetchDeviationUpdate(params: unknown) {
  return request<any>({
    url: '/deviationPermission/update',
    method: 'post',
    data: params
  });
}

/**
 * 偏离许可 - 删除
 *
 * @param params
 */
export function fetchDeviationDelete(params: unknown) {
  return request<any>({
    url: '/deviationPermission/delete',
    method: 'post',
    data: params
  });
}

/**
 * 问题处理 - 列表
 *
 * @param params
 */
export function fetchProblemList(params: unknown) {
  return request<any>({
    url: '/qualifiedOver/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 问题处理 - 审核
 *
 * @param params
 */
export function fetchProblemHandle(params: unknown) {
  return request<any>({
    url: '/qualifiedOver/handle',
    method: 'post',
    data: params
  });
}

/**
 * 问题处理 - 新增
 *
 * @param params
 */
export function fetchProblemAdd(params: unknown) {
  return request<any>({
    url: '/qualifiedOver/add',
    method: 'post',
    data: params
  });
}

/**
 * 问题处理 - 删除
 *
 * @param params
 */
export function fetchProblemDelete(params: unknown) {
  return request<any>({
    url: '/qualifiedOver/delete',
    method: 'post',
    data: params
  });
}

/**
 * 问题处理 - 编辑
 *
 * @param params
 */
export function fetchProblemUpdate(params: unknown) {
  return request<any>({
    url: '/qualifiedOver/update',
    method: 'post',
    data: params
  });
}

/**
 * 问题处理 - 详情
 *
 * @param params
 */
export function fetchProblemQuery(params: unknown) {
  return request<any>({
    url: '/qualifiedOver/query',
    method: 'post',
    data: params
  });
}

/**
 * 问题处理 - 删除情况说明
 *
 * @param params
 */
export function fetchProblemOver(params: unknown) {
  return request<any>({
    url: '/qualifiedOver/deleteOver',
    method: 'post',
    data: params
  });
}

/**
 * 风险监控 - 列表
 *
 * @param params
 */
export function fetchRiskList(params: unknown) {
  return request<any>({
    url: '/risk/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 风险监控 - 编辑
 *
 * @param params
 */
export function fetchRiskUpdate(params: unknown) {
  return request<any>({
    url: '/risk/update',
    method: 'post',
    data: params
  });
}

/**
 * 风险监控 - 删除
 *
 * @param params
 */
export function fetchRiskDelete(params: unknown) {
  return request<any>({
    url: '/risk/delete',
    method: 'post',
    data: params
  });
}

/**
 * 风险监控 - 新增
 *
 * @param params
 */
export function fetchRiskAdd(params: unknown) {
  return request<any>({
    url: '/risk/add',
    method: 'post',
    data: params
  });
}

/**
 * 风险监控 - 详情
 *
 * @param params
 */
export function fetchRiskQuery(params: unknown) {
  return request<any>({
    url: '/risk/query',
    method: 'post',
    data: params
  });
}

/**
 * 风险监控 - 删除情况说明
 *
 * @param params
 */
export function fetchRiskDel(params: unknown) {
  return request<any>({
    url: '/risk/deleteRisk',
    method: 'post',
    data: params
  });
}

/**
 * 风险监控 - 审核
 *
 * @param params
 */
export function fetchRiskHandle(params: unknown) {
  return request<any>({
    url: '/risk/handle',
    method: 'post',
    data: params
  });
}

/**
 * 进度管理 - 列表
 *
 * @param params
 */
export function fetchSuperviseList(params: unknown) {
  return request<any>({
    url: '/progressSupervise/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 进度管理 - 新增
 *
 * @param params
 */
export function fetchSuperviseAdd(params: unknown) {
  return request<any>({
    url: '/progressSupervise/add',
    method: 'post',
    data: params
  });
}

/**
 * 进度管理 - 删除
 *
 * @param params
 */
export function fetchSuperviseDelete(params: unknown) {
  return request<any>({
    url: '/progressSupervise/delete',
    method: 'post',
    data: params
  });
}

/**
 * 进度管理 - 详情
 *
 * @param params
 */
export function fetchSuperviseQuery(params: unknown) {
  return request<any>({
    url: '/progressSupervise/query',
    method: 'post',
    data: params
  });
}

/**
 * 进度管理 - 项目进展编辑
 *
 * @param params
 */
export function fetchSuperviseUpdateFlow(params: unknown) {
  return request<any>({
    url: '/progressSupervise/updateFlow',
    method: 'post',
    data: params
  });
}

/**
 * 成本管理 - 列表
 *
 * @param params
 */
export function fetchCostList(params: unknown) {
  return request<any>({
    url: '/costControl/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 成本管理 - 新增
 *
 * @param params
 */
export function fetchCostAdd(params: unknown) {
  return request<any>({
    url: '/costControl/add',
    method: 'post',
    data: params
  });
}

/**
 * 成本管理 - 删除
 *
 * @param params
 */
export function fetchCostDelete(params: unknown) {
  return request<any>({
    url: '/costControl/delete',
    method: 'post',
    data: params
  });
}

/**
 * 成本管理 - 编辑
 *
 * @param params
 */
export function fetchCostUpdate(params: unknown) {
  return request<any>({
    url: '/costControl/update',
    method: 'post',
    data: params
  });
}

/**
 * 成本管理 - 详情
 *
 * @param params
 */
export function fetchCostQuery(params: unknown) {
  return request<any>({
    url: '/costControl/query',
    method: 'post',
    data: params
  });
}

/**
 * 配套经费支付 - 列表
 *
 * @param params
 */
export function fetchMatingList(params: unknown) {
  return request<any>({
    url: '/matingPayment/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 配套经费支付 - 新增
 *
 * @param params
 */
export function fetchMatingAdd(params: unknown) {
  return request<any>({
    url: '/matingPayment/add',
    method: 'post',
    data: params
  });
}

/**
 * 配套经费支付 - 删除
 *
 * @param params
 */
export function fetchMatingDelete(params: unknown) {
  return request<any>({
    url: '/matingPayment/delete',
    method: 'post',
    data: params
  });
}

/**
 * 配套经费支付 - 编辑
 *
 * @param params
 */
export function fetchMatingUpdate(params: unknown) {
  return request<any>({
    url: '/matingPayment/update',
    method: 'post',
    data: params
  });
}

/**
 * 配套经费支付 - 详情
 *
 * @param params
 */
export function fetchMatingQuery(params: unknown) {
  return request<any>({
    url: '/matingPayment/query',
    method: 'post',
    data: params
  });
}

/**
 * 合同节点考核 - 列表
 *
 * @param params
 */
export function fetchNodeList(params: unknown) {
  return request<any>({
    url: '/purchaseNodeCheck/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 合同节点考核 - 新增
 *
 * @param params
 */
export function fetchNodeAdd(params: unknown) {
  return request<any>({
    url: '/purchaseNodeCheck/add',
    method: 'post',
    data: params
  });
}

/**
 * 合同节点考核 - 删除
 *
 * @param params
 */
export function fetchNodeDelete(params: unknown) {
  return request<any>({
    url: '/purchaseNodeCheck/delete',
    method: 'post',
    data: params
  });
}

/**
 * 合同节点考核 - 编辑
 *
 * @param params
 */
export function fetchNodeUpdate(params: unknown) {
  return request<any>({
    url: '/purchaseNodeCheck/update',
    method: 'post',
    data: params
  });
}

/**
 * 合同节点考核 - 详情
 *
 * @param params
 */
export function fetchNodeQuery(params: unknown) {
  return request<any>({
    url: '/purchaseNodeCheck/query',
    method: 'post',
    data: params
  });
}
