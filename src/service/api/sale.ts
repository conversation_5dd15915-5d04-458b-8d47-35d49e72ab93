import { request } from '../request';

/**
 * 售后服务 - 列表
 *
 * @param params
 */
export function fetchAftermarketList(params: unknown) {
  return request<any>({
    url: '/aftermarket/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 售后服务 - 新增
 *
 * @param params
 */
export function fetchAftermarketAdd(params: unknown) {
  return request<any>({
    url: '/aftermarket/add',
    method: 'post',
    data: params
  });
}

/**
 * 售后服务 - 删除
 *
 * @param params
 */
export function fetchAftermarketDelete(params: unknown) {
  return request<any>({
    url: '/aftermarket/delete',
    method: 'post',
    data: params
  });
}

/**
 * 售后服务 - 编辑
 *
 * @param params
 */
export function fetchAftermarketUpdate(params: unknown) {
  return request<any>({
    url: '/aftermarket/update',
    method: 'post',
    data: params
  });
}

/**
 * 售后服务 - 详情
 *
 * @param params
 */
export function fetchAftermarketQuery(params: unknown) {
  return request<any>({
    url: '/aftermarket/query',
    method: 'post',
    data: params
  });
}

/**
 * 合同绩效 - 列表
 *
 * @param params
 */
export function fetchPerformanceList(params: unknown) {
  return request<any>({
    url: '/contractPerformance/queryPageList',
    method: 'post',
    data: params
  });
}

/**
 * 合同绩效 - 新增
 *
 * @param params
 */
export function fetchPerformanceAdd(params: unknown) {
  return request<any>({
    url: '/contractPerformance/add',
    method: 'post',
    data: params
  });
}

/**
 * 合同绩效 - 删除
 *
 * @param params
 */
export function fetchPerformanceDelete(params: unknown) {
  return request<any>({
    url: '/contractPerformance/delete',
    method: 'post',
    data: params
  });
}

/**
 * 合同绩效 - 编辑
 *
 * @param params
 */
export function fetchPerformanceUpdate(params: unknown) {
  return request<any>({
    url: '/contractPerformance/update',
    method: 'post',
    data: params
  });
}

/**
 * 合同绩效 - 详情
 *
 * @param params
 */
export function fetchPerformanceQuery(params: unknown) {
  return request<any>({
    url: '/contractPerformance/query',
    method: 'post',
    data: params
  });
}

/**
 * 合同绩效 - 绩效评价 - 新增
 *
 * @param params
 */
export function fetchPerformanceAddAdvise(params: unknown) {
  return request<any>({
    url: '/contractPerformance/addAdvise',
    method: 'post',
    data: params
  });
}

/**
 * 合同绩效 - 绩效评价 - 删除
 *
 * @param params
 */
export function fetchPerformanceDeleteAdvise(params: unknown) {
  return request<any>({
    url: '/contractPerformance/deleteAdvise ',
    method: 'post',
    data: params
  });
}

/**
 * 合同绩效 - 绩效评价 - 编辑
 *
 * @param params
 */
export function fetchPerformanceUpdateAdvise(params: unknown) {
  return request<any>({
    url: '/contractPerformance/updateAdvise ',
    method: 'post',
    data: params
  });
}
