import { request } from "../request";

/** 获取档案资料库信息
 */
export function getArchives(params: unknown) {
  return request<any>({
    url: "/contractManufacture/getArchives",
    method: "get",
    data: params,
  });
}
/** 档案资料库-采购合同档案资料-查询
 */
export function queryPurchaseContracts(params: unknown) {
  return request<any>({
    url: "/purchaseContractsData/queryPurchaseContracts",
    method: "post",
    data: params,
  });
}
//承制单位资料库-查询

export function queryCompanyArchives(params: unknown) {
  return request<any>({
    url: "/companyArchivesData/queryCompanyArchives ",
    method: "post",
    data: params,
  });
}
//档案资料库-售后服务查询

export function afterSalesArchivesData(params: unknown) {
  return request<any>({
    url: "/afterSalesArchivesData/queryPageList ",
    method: "post",
    data: params,
  });
}
//承制合同档案资料-查询

export function queryContractArchivesData(params: unknown) {
  return request<any>({
    url: "/contractArchivesData/queryContractArchivesData",
    method: "post",
    data: params,
  });
}
//承制合同档案资料-材料清单-查询

export function queryMaterials(params: unknown) {
  return request<any>({
    url: "/contractArchivesData/queryMaterials",
    method: "post",
    data: params,
  });
}
//承制合同档案资料-产品出厂目录-查询

export function queryProductFactoryList(params: unknown) {
  return request<any>({
    url: "/contractArchivesData/queryProductFactoryList",
    method: "post",
    data: params,
  });
}
//档案资料库-合同履行绩效查询

export function contractPerformanceData(params: unknown) {
  return request<any>({
    url: "/contractPerformanceData/queryPageList ",
    method: "post",
    data: params,
  });
}
export function queryFileDetails (params: unknown) {
  return request<any>({
    url: "/contractArchivesData/queryFileDetails",
    method: "post",
    data: params,
  });
}
export function attachmentquery (params: unknown) {
  return request<any>({
    url: "/attachment/query",
    method: "post",
    data: params,
  });
}
//产品库 productWarehouse/productPage
export function productPage (params: unknown) {
  return request<any>({
    url: "/productWarehouse/productPage",
    method: "post",
    data: params,
  });
}
export function addProduct (params: unknown) {
  return request<any>({
    url: "/productWarehouse/addProduct",
    method: "post",
    data: params,
  });
}
export function updateProduct (params: unknown) {
  return request<any>({
    url: "/productWarehouse/updateProduct",
    method: "post",
    data: params,
  });
}
export function getWarehouseProduct (params: unknown) {
  return request<any>({
    url: "/productWarehouse/getProduct",
    method: "post",
    data: params,
  });
}
export function deleteProduct (params: unknown) {
  return request<any>({
    url: "/productWarehouse/deleteProduct",
    method: "post",
    data: params,
  });
}
export function queryProductMaterials (params: unknown) {
  return request<any>({
    url: "/contractArchivesData/queryProductMaterials",
    method: "post",
    data: params,
  });
}
export function upArchivesDataFile (params: unknown) {
  return request<any>({
    url: "/contractArchivesData/upArchivesDataFile",
    method: "post",
    data: params,
  });
}
export function queryFileList (params: unknown) {
  return request<any>({
    url: "/contractArchivesData/queryFileList",
    method: "post",
    data: params,
  });
}
export function zipFilesWithCommonsCompress (params: unknown) {
  return request<any>({
    url: "/contractArchivesData/zipFilesWithCommonsCompress",
    method: "post",
    data: params,
  });
}
