/**
 * 【重要】该文件由脚本生成，请勿手动修改，重新生成请运行 npm run api 命令
 */

import { request } from '../request'

/**
 * 工作流-分页查询流程定义列表
 */
export function fetchManageApiFlowDefinitionList(params: unknown) {
  return request<any>({
    url: 'flow/definition/list',
    method: 'post',
    data: params
  })
}

/**
 * 工作流-获取流程定义详细信息
 */
export function fetchManageApiFlowDefinitionGetInfoId(params: { id: string | number }) {
  return request<any>({
    url: 'flow/definition/getInfo',
    method: 'get',
    params: params
  })
}

/**
 * 工作流-新增流程定义
 */
export function fetchManageApiFlowDefinitionAdd(params: unknown) {
  return request<any>({
    url: 'flow/definition/add',
    method: 'post',
    data: params
  })
}

/**
 * 工作流-发布流程定义
 */
export function fetchManageApiFlowDefinitionPublishId(params: { id: string | number }) {
  return request<any>({
    url: 'flow/definition/publish',
    method: 'get',
    params: params
  })
}

/**
 * 工作流-取消发布流程定义
 */
export function fetchManageApiFlowDefinitionUnPublishId(params: { id: string | number }) {
  return request<any>({
    url: 'flow/definition/unPublish',
    method: 'get',
    params: params
  })
}

/**
 * 工作流-修改流程定义
 */
export function fetchManageApiFlowDefinitionEdit(params: unknown) {
  return request<any>({
    url: 'flow/definition/edit',
    method: 'post',
    data: params
  })
}

/**
 * 工作流-删除流程定义
 */
export function fetchManageApiFlowDefinitionRemoveId(params: { id: string | number }) {
  return request<any>({
    url: 'flow/definition/remove',
    method: 'get',
    params: params
  })
}

/**
 * 工作流-删除流程定义
 */
export function fetchManageApiFlowDefinitionBatchRemoveIds(params: { ids: string | number }) {
  return request<any>({
    url: 'flow/definition/batchRemove',
    method: 'get',
    params: params
  })
}

/**
 * 工作流-复制流程定义
 */
export function fetchManageApiFlowDefinitionCopyDefId(params: { id: string | number }) {
  return request<any>({
    url: 'flow/definition/copyDef',
    method: 'get',
    params: params
  })
}

/**
 * 工作流-流程导入
 */
export function fetchManageApiFlowDefinitionImportDefinition(params: unknown) {
  return request<any>({
    url: 'flow/definition/importDefinition',
    method: 'post',
    data: params
  })
}

/**
 * 工作流-查询流程图（流程定义）
 */
export function fetchManageApiFlowDefinitionFlowChartNoColorInstanceId(params: { instanceId: string | number }) {
  return request<any>({
    url: 'flow/definition/flowChartNoColor',
    method: 'get',
    params: params
  })
}

/**
 * 工作流-查询流程图（流程实例）
 */
export function fetchManageApiFlowDefinitionFlowChartInstanceId(params: { instanceId: string | number }) {
  return request<any>({
    url: 'flow/definition/flowChart',
    method: 'get',
    params: params
  })
}

/**
 * 工作流-激活流程
 */
export function fetchManageApiFlowDefinitionActiveDefinitionId(params: { definitionId: string | number }) {
  return request<any>({
    url: 'flow/definition/active',
    method: 'get',
    params: params
  })
}

/**
 * 工作流-挂起流程
 */
export function fetchManageApiFlowDefinitionUnActiveDefinitionId(params: { definitionId: string | number }) {
  return request<any>({
    url: 'flow/definition/unActive',
    method: 'get',
    params: params
  })
}

/**
 * 统计各月份已办任务
 */
export function fetchManageApiFlowExecuteCountDoneEveryMonth(params: unknown) {
  return request<any>({
    url: 'flow/execute/countDoneEveryMonth',
    method: 'post',
    data: params
  })
}

/**
 * 统计当月、当天已办任务
 */
export function fetchManageApiFlowExecuteCountDoneThisMonthAndToday(params: unknown) {
  return request<any>({
    url: 'flow/execute/countDoneThisMonthAndToday',
    method: 'post',
    data: params
  })
}

/**
 * 分页待办任务列表
 */
export function fetchManageApiFlowExecuteToDoPage(params: unknown) {
  return request<any>({
    url: 'flow/execute/toDoPage',
    method: 'post',
    data: params
  })
}

/**
 * 分页抄送任务列表
 */
export function fetchManageApiFlowExecuteCopyPage(params: unknown) {
  return request<any>({
    url: 'flow/execute/copyPage',
    method: 'post',
    data: params
  })
}

/**
 * 分页已办任务列表
 */
export function fetchManageApiFlowExecuteDonePage(params: unknown) {
  return request<any>({
    url: 'flow/execute/donePage',
    method: 'get',
    params: params
  })
}

/**
 * 查询已办任务历史记录
 */
export function fetchManageApiFlowExecuteDoneListInstanceId(params: { instanceId: string | number }) {
  return request<any>({
    url: 'flow/execute/doneList',
    method: 'get',
    params: params
  })
}

/**
 * 根据taskId查询代表任务
 */
export function fetchManageApiFlowExecuteGetTaskByIdTaskId(params: { taskId: string | number }) {
  return request<any>({
    url: 'flow/execute/getTaskById',
    method: 'get',
    params: params
  })
}

/**
 * 查询跳转任意节点列表
 */
export function fetchManageApiFlowExecuteAnyNodeListInstanceId(params: { instanceId: string | number }) {
  return request<any>({
    url: 'flow/execute/anyNodeList',
    method: 'get',
    params: params
  })
}

/**
 * 查询可跳转的节点列表
 */
export function fetchManageApiFlowExecuteGetBackNodeListTaskId(params: { taskId: string | number }) {
  return request<any>({
    url: 'flow/execute/getBackNodeList',
    method: 'get',
    params: params
  })
}

/**
 * 处理非办理的流程交互类型
 */
export function fetchManageApiFlowExecuteInteractiveType(params: unknown) {
  return request<any>({
    url: 'flow/execute/interactiveType',
    method: 'post',
    data: params
  })
}

/**
 * 激活流程
 */
export function fetchManageApiFlowExecuteActiveInstanceId(params: { instanceId: string | number }) {
  return request<any>({
    url: 'flow/execute/active',
    method: 'get',
    params: params
  })
}

/**
 * 挂起流程
 */
export function fetchManageApiFlowExecuteUnActiveInstanceId(params: { instanceId: string | number }) {
  return request<any>({
    url: 'flow/execute/unActive',
    method: 'get',
    params: params
  })
}

/**
 * 根据ID反显姓名
 */
export function fetchManageApiFlowExecuteIdReverseDisplayNameIds(params: { ids: string | number }) {
  return request<any>({
    url: 'flow/execute/idReverseDisplayName',
    method: 'get',
    params: params
  })
}

/**
 * 担保贷-提交审批
 */
export function fetchManageApiAuditSubmitGuarantee(params: unknown) {
  return request<any>({
    url: 'audit/submitGuarantee',
    method: 'post',
    data: params
  })
}

/**
 * 担保贷-审批
 */
export function fetchManageApiAuditAuditGuarantee(params: unknown) {
  return request<any>({
    url: 'audit/auditGuarantee',
    method: 'post',
    data: params
  })
}

/**
 * 担保贷-批量审批
 */
export function fetchManageApiAuditBatchAuditGuarantee(params: unknown) {
  return request<any>({
    url: 'audit/batchAuditGuarantee',
    method: 'post',
    data: params
  })
}

/**
 * 信用贷-提交审批
 */
export function fetchManageApiAuditSubmitCredit(params: unknown) {
  return request<any>({
    url: 'audit/submitCredit',
    method: 'post',
    data: params
  })
}

/**
 * 信用贷-审批
 */
export function fetchManageApiAuditAuditCredit(params: unknown) {
  return request<any>({
    url: 'audit/auditCredit',
    method: 'post',
    data: params
  })
}

/**
 * 风补审批-撤回
 */
export function fetchManageApiAuditRecall(params: unknown) {
  return request<any>({
    url: 'audit/recall',
    method: 'post',
    data: params
  })
}

/**
 * 风补审批-中止流程
 */
export function fetchManageApiAuditKill(params: unknown) {
  return request<any>({
    url: 'audit/kill',
    method: 'post',
    data: params
  })
}

/**
 * 新增风险补偿审批记录
 */
export function fetchManageApiRiskAddRisk(params: unknown) {
  return request<any>({
    url: 'risk/addRisk',
    method: 'post',
    data: params
  })
}

/**
 * 提交风险补偿审批记录
 */
export function fetchManageApiRiskSubmit(params: unknown) {
  return request<any>({
    url: 'risk/submit',
    method: 'post',
    data: params
  })
}

/**
 * 修改风补审批记录
 */
export function fetchManageApiRiskEdit(params: unknown) {
  return request<any>({
    url: 'risk/edit',
    method: 'post',
    data: params
  })
}

/**
 * 拨付
 */
export function fetchManageApiRiskPaid(params: unknown) {
  return request<any>({
    url: 'risk/paid',
    method: 'post',
    data: params
  })
}

/**
 * 风补审批-查询待办
 */
export function fetchManageApiRiskQueryTodoList(params: unknown) {
  return request<any>({
    url: 'risk/queryTodoList',
    method: 'post',
    data: params
  })
}

/**
 * 风补审批-查询已办
 */
export function fetchManageApiRiskQueryDoneList(params: unknown) {
  return request<any>({
    url: 'risk/queryDoneList',
    method: 'post',
    data: params
  })
}

/**
 * 风补审批-审批记录
 */
export function fetchManageApiRiskQueryHisTask(params: unknown) {
  return request<any>({
    url: 'risk/queryHisTask',
    method: 'post',
    data: params
  })
}

/**
 * 删除风补审批
 */
export function fetchManageApiRiskRemoveRiskAudit(params: unknown) {
  return request<any>({
    url: 'risk/removeRiskAudit',
    method: 'post',
    data: params
  })
}

/**
 * 风补审批-分页查询
 */
export function fetchManageApiRiskQueryPage(params: unknown) {
  return request<any>({
    url: 'risk/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 查询详情
 */
export function fetchManageApiRiskQueryDetail(params: unknown) {
  return request<any>({
    url: 'risk/queryDetail',
    method: 'post',
    data: params
  })
}

/**
 * 贷款利率配置-新增配置
 */
export function fetchManageApiLoanRateAdd(params: unknown) {
  return request<any>({
    url: 'loanRate/add',
    method: 'post',
    data: params
  })
}

/**
 * 贷款利率配置-修改配置
 */
export function fetchManageApiLoanRateEdit(params: unknown) {
  return request<any>({
    url: 'loanRate/edit',
    method: 'post',
    data: params
  })
}

/**
 * 贷款利率配置-删除配置
 */
export function fetchManageApiLoanRateDelete(params: unknown) {
  return request<any>({
    url: 'loanRate/delete',
    method: 'post',
    data: params
  })
}

/**
 * 贷款利率配置-列表查询
 */
export function fetchManageApiLoanRateQueryPage(params: unknown) {
  return request<any>({
    url: 'loanRate/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 导出配置-新增修改配置
 */
export function fetchManageApiExportConfigAddOrUpdate(params: unknown) {
  return request<any>({
    url: 'exportConfig/addOrUpdate',
    method: 'post',
    data: params
  })
}

/**
 * 导出配置-列表查询
 */
export function fetchManageApiExportConfigGetInfo(params: unknown) {
  return request<any>({
    url: 'exportConfig/getInfo',
    method: 'post',
    data: params
  })
}

/**
 * 新增配置
 */
export function fetchManageApiConfigAdd(params: unknown) {
  return request<any>({
    url: 'config/add',
    method: 'post',
    data: params
  })
}

/**
 * 编辑配置
 */
export function fetchManageApiConfigEdit(params: unknown) {
  return request<any>({
    url: 'config/edit',
    method: 'post',
    data: params
  })
}

/**
 * 分页查询配置
 */
export function fetchManageApiConfigQueryPage(params: unknown) {
  return request<any>({
    url: 'config/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 熔断
 */
export function fetchManageApiConfigFusing(params: unknown) {
  return request<any>({
    url: 'config/fusing',
    method: 'post',
    data: params
  })
}

/**
 * 分页查询年度风补金额
 */
export function fetchManageApiConfigQueryYearCount(params: unknown) {
  return request<any>({
    url: 'config/queryYearCount',
    method: 'post',
    data: params
  })
}

/**
 * 大屏
 */
export function fetchManageApiDashboardQueryDashboard(params: unknown) {
  return request<any>({
    url: 'dashboard/queryDashboard',
    method: 'post',
    data: params
  })
}

/**
 * 大屏-全口径科技贷款投放额
 */
export function fetchManageApiDashboardQueryReportList(params: unknown) {
  return request<any>({
    url: 'dashboard/queryReportList',
    method: 'post',
    data: params
  })
}

/**
 * 贷款任务管理-新增
 */
export function fetchManageApiLoanTaskManageAdd(params: unknown) {
  return request<any>({
    url: 'loanTaskManage/add',
    method: 'post',
    data: params
  })
}

/**
 * 贷款任务管理-修改
 */
export function fetchManageApiLoanTaskManageUpdate(params: unknown) {
  return request<any>({
    url: 'loanTaskManage/update',
    method: 'post',
    data: params
  })
}

/**
 * 贷款任务管理-分页查询
 */
export function fetchManageApiLoanTaskManageQueryPage(params: unknown) {
  return request<any>({
    url: 'loanTaskManage/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 贷款任务管理-查询
 */
export function fetchManageApiLoanTaskManageGet(params: unknown) {
  return request<any>({
    url: 'loanTaskManage/get',
    method: 'post',
    data: params
  })
}

/**
 * 贷款任务管理-删除
 */
export function fetchManageApiLoanTaskManageDelete(params: unknown) {
  return request<any>({
    url: 'loanTaskManage/delete',
    method: 'post',
    data: params
  })
}

/**
 * 新增合作机构
 */
export function fetchManageApiOrgSave(params: unknown) {
  return request<any>({
    url: 'org/save',
    method: 'post',
    data: params
  })
}

/**
 * 修改合作机构
 */
export function fetchManageApiOrgUpdate(params: unknown) {
  return request<any>({
    url: 'org/update',
    method: 'post',
    data: params
  })
}

/**
 * 查询合作机构
 */
export function fetchManageApiOrgQueryPage(params: unknown) {
  return request<any>({
    url: 'org/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 导入合作机构
 */
export function fetchManageApiOrgImport(params: unknown) {
  return request<any>({
    url: 'org/import',
    method: 'post',
    data: params
  })
}

/**
 * 移除合作机构
 */
export function fetchManageApiOrgRemove(params: unknown) {
  return request<any>({
    url: 'org/remove',
    method: 'post',
    data: params
  })
}

/**
 * 科技金融-导入数据
 */
export function fetchManageApiSciEntImportData(params: unknown) {
  return request<any>({
    url: 'sci-ent/importData',
    method: 'post',
    data: params
  })
}

/**
 * 科技金融-同步数据
 */
export function fetchManageApiSciEntSyncRecordData(params: unknown) {
  return request<any>({
    url: 'sci-ent/syncRecordData',
    method: 'post',
    data: params
  })
}

/**
 * 科技金融-同步数据
 */
export function fetchManageApiSciEntSyncEntData(params: unknown) {
  return request<any>({
    url: 'sci-ent/syncEntData',
    method: 'post',
    data: params
  })
}

/**
 * 科技金融-查询企业列表
 */
export function fetchManageApiSciEntQueryPage(params: unknown) {
  return request<any>({
    url: 'sci-ent/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 科技金融-上传附件
 */
export function fetchManageApiSciEntUpload(params: unknown) {
  return request<any>({
    url: 'sci-ent/upload',
    method: 'post',
    data: params
  })
}

/**
 * 科技金融-新增企业
 */
export function fetchManageApiSciEntAdd(params: unknown) {
  return request<any>({
    url: 'sci-ent/add',
    method: 'post',
    data: params
  })
}

/**
 * 科技金融-更新
 */
export function fetchManageApiSciEntUpdate(params: unknown) {
  return request<any>({
    url: 'sci-ent/update',
    method: 'post',
    data: params
  })
}

/**
 * 科技金融-批量编辑
 */
export function fetchManageApiSciEntBatchUpdate(params: unknown) {
  return request<any>({
    url: 'sci-ent/batchUpdate',
    method: 'post',
    data: params
  })
}

/**
 * 科技金融-删除科技企业
 */
export function fetchManageApiSciEntRemove(params: unknown) {
  return request<any>({
    url: 'sci-ent/remove',
    method: 'post',
    data: params
  })
}

/**
 * 科技金融-计算评分
 */
export function fetchManageApiSciEntCalcByYear(params: unknown) {
  return request<any>({
    url: 'sci-ent/calcByYear',
    method: 'post',
    data: params
  })
}

/**
 * 下载模板
 */
export function fetchManageApiSciEntDownloadTemplate(params: unknown) {
  return request<any>({
    url: 'sci-ent/downloadTemplate',
    method: 'post',
    data: params,
    responseType: "arraybuffer"
  })
}

/**
 * 科技金融-查询详情
 */
export function fetchManageApiSciEntQueryDetail(params: unknown) {
  return request<any>({
    url: 'sci-ent/queryDetail',
    method: 'post',
    data: params
  })
}

/**
 * 新增贷款备案
 */
export function fetchManageApiRecordAdd(params: unknown) {
  return request<any>({
    url: 'record/add',
    method: 'post',
    data: params
  })
}

/**
 * 备案-删除
 */
export function fetchManageApiRecordDelete(params: unknown) {
  return request<any>({
    url: 'record/delete',
    method: 'post',
    data: params
  })
}

/**
 * 批量修改贷款备案状态
 */
export function fetchManageApiRecordChangeBatchStatus(params: unknown) {
  return request<any>({
    url: 'record/changeBatchStatus',
    method: 'post',
    data: params
  })
}

/**
 * 风补备案-编辑
 */
export function fetchManageApiRecordEdit(params: unknown) {
  return request<any>({
    url: 'record/edit',
    method: 'post',
    data: params
  })
}

/**
 * 贷款备案-提交备案
 */
export function fetchManageApiRecordRecordApply(params: unknown) {
  return request<any>({
    url: 'record/recordApply',
    method: 'post',
    data: params
  })
}

/**
 * 查询剩余可备案金额
 */
export function fetchManageApiRecordQueryRestAmount(params: unknown) {
  return request<any>({
    url: 'record/queryRestAmount',
    method: 'post',
    data: params
  })
}

/**
 * 分页查询贷款备案
 */
export function fetchManageApiRecordQueryPage(params: unknown) {
  return request<any>({
    url: 'record/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 查询备案详情
 */
export function fetchManageApiRecordQueryDetail(params: unknown) {
  return request<any>({
    url: 'record/queryDetail',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计分页查询(区域)
 */
export function fetchManageApiLoanStatisticQueryPageByRegion(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByRegion',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计分页查询(区域)-导出
 */
export function fetchManageApiLoanStatisticQueryPageByRegionExport(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByRegionExport',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计分页查询(区域-机构)
 */
export function fetchManageApiLoanStatisticQueryPageByRegionOrg(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByRegionOrg',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计分页查询(区域-机构)-导出
 */
export function fetchManageApiLoanStatisticQueryPageByRegionOrgExport(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByRegionOrgExport',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计分页查询(区域-机构-企业)
 */
export function fetchManageApiLoanStatisticQueryPageByRegionOrgEnt(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByRegionOrgEnt',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计分页查询(区域-机构-企业)-导出
 */
export function fetchManageApiLoanStatisticQueryPageByRegionOrgEntExport(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByRegionOrgEntExport',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计分页查询(机构)
 */
export function fetchManageApiLoanStatisticQueryPageByBank(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByBank',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计查询(机构)-导出
 */
export function fetchManageApiLoanStatisticQueryPageByBankExport(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByBankExport',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计分页查询(机构-区域)
 */
export function fetchManageApiLoanStatisticQueryPageByBankRegion(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByBankRegion',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计查询(机构-区域)-导出
 */
export function fetchManageApiLoanStatisticQueryPageByBankRegionExport(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByBankRegionExport',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计分页查询(企业)
 */
export function fetchManageApiLoanStatisticQueryPageByEnt(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByEnt',
    method: 'post',
    data: params
  })
}

/**
 * 贷款统计分页查询(企业)-导出
 */
export function fetchManageApiLoanStatisticQueryPageByEntExport(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByEntExport',
    method: 'post',
    data: params
  })
}

/**
 * 风补统计分页查询(区域)
 */
export function fetchManageApiRiskStatisticQueryPageByRegion(params: unknown) {
  return request<any>({
    url: 'riskStatistic/queryPageByRegion',
    method: 'post',
    data: params
  })
}

/**
 * 风补统计分页查询(区域)-导出
 */
export function fetchManageApiRiskStatisticQueryPageByRegionExport(params: unknown) {
  return request<any>({
    url: 'riskStatistic/queryPageByRegionExport',
    method: 'post',
    data: params
  })
}

/**
 * 风补统计分页查询(银行)
 */
export function fetchManageApiRiskStatisticQueryPageByBank(params: unknown) {
  return request<any>({
    url: 'riskStatistic/queryPageByBank',
    method: 'post',
    data: params
  })
}

/**
 * 风补统计分页查询(银行)-导出
 */
export function fetchManageApiRiskStatisticQueryPageByBankExport(params: unknown) {
  return request<any>({
    url: 'riskStatistic/queryPageByBankExport',
    method: 'post',
    data: params
  })
}

/**
 * 文件上传
 */
export function fetchManageApiAttachmentUpload(params: unknown) {
  return request<any>({
    url: 'attachment/upload',
    method: 'post',
    data: params
  })
}

/**
 * 文件数组上传
 */
export function fetchManageApiAttachmentUploadByteArrays(params: unknown) {
  return request<any>({
    url: 'attachment/uploadByteArrays',
    method: 'post',
    data: params
  })
}

/**
 * 附件信息列表新增
 */
export function fetchManageApiAttachmentAdd(params: unknown) {
  return request<any>({
    url: 'attachment/add',
    method: 'post',
    data: params
  })
}

/**
 * 附件信息列表更新 - 全删全增
 */
export function fetchManageApiAttachmentUpdate(params: unknown) {
  return request<any>({
    url: 'attachment/update',
    method: 'post',
    data: params
  })
}

/**
 * 附件信息删除-通過id
 */
export function fetchManageApiAttachmentDeleteById(params: unknown) {
  return request<any>({
    url: 'attachment/deleteById',
    method: 'post',
    data: params
  })
}

/**
 * 附件信息删除
 */
export function fetchManageApiAttachmentDelete(params: unknown) {
  return request<any>({
    url: 'attachment/delete',
    method: 'post',
    data: params
  })
}

/**
 * 附件列表查询
 */
export function fetchManageApiAttachmentQuery(params: unknown) {
  return request<any>({
    url: 'attachment/query',
    method: 'post',
    data: params
  })
}

/**
 * 文件下载
 */
export function fetchManageApiAttachmentDownloadFileByUrl(params: unknown) {
  return request<any>({
    url: 'attachment/downloadFileByUrl',
    method: 'post',
    data: params,
    responseType: "arraybuffer"
  })
}

/**
 * 登录-管理后台
 */
export function fetchManageApiAuthLoginManage(params: unknown) {
  return request<any>({
    url: 'auth/loginManage',
    method: 'post',
    data: params
  })
}

/**
 * 发送短信验证码
 */
export function fetchManageApiAuthSendCode(params: unknown) {
  return request<any>({
    url: 'auth/sendCode',
    method: 'post',
    data: params
  })
}

/**
 * 退出登录
 */
export function fetchManageApiAuthLogout(params: unknown) {
  return request<any>({
    url: 'auth/logout',
    method: 'post',
    data: params
  })
}

/**
 * 权限-新增菜单权限
 */
export function fetchManageApiElementAddElement(params: unknown) {
  return request<any>({
    url: 'element/addElement',
    method: 'post',
    data: params
  })
}

/**
 * 权限-更新菜单权限
 */
export function fetchManageApiElementUpdateElement(params: unknown) {
  return request<any>({
    url: 'element/updateElement',
    method: 'post',
    data: params
  })
}

/**
 * 权限-排序菜单权限
 */
export function fetchManageApiElementSortElement(params: unknown) {
  return request<any>({
    url: 'element/sortElement',
    method: 'post',
    data: params
  })
}

/**
 * 权限-删除菜单权限
 */
export function fetchManageApiElementDeleteElement(params: unknown) {
  return request<any>({
    url: 'element/deleteElement',
    method: 'post',
    data: params
  })
}

/**
 * 权限-查询菜单权限详情
 */
export function fetchManageApiElementGetElement(params: unknown) {
  return request<any>({
    url: 'element/getElement',
    method: 'post',
    data: params
  })
}

/**
 * 权限-查询菜单权限列表
 */
export function fetchManageApiElementQueryElementList(params: unknown) {
  return request<any>({
    url: 'element/queryElementList',
    method: 'post',
    data: params
  })
}

/**
 * 权限-查询接口权限列表
 */
export function fetchManageApiElementQueryApiList(params: unknown) {
  return request<any>({
    url: 'element/queryApiList',
    method: 'post',
    data: params
  })
}

/**
 * 角色-新增角色
 */
export function fetchManageApiRoleAddRole(params: unknown) {
  return request<any>({
    url: 'role/addRole',
    method: 'post',
    data: params
  })
}

/**
 * 角色-更新角色
 */
export function fetchManageApiRoleUpdateRole(params: unknown) {
  return request<any>({
    url: 'role/updateRole',
    method: 'post',
    data: params
  })
}

/**
 * 角色-删除角色
 */
export function fetchManageApiRoleDeleteRole(params: unknown) {
  return request<any>({
    url: 'role/deleteRole',
    method: 'post',
    data: params
  })
}

/**
 * 角色-分页查询角色列表
 */
export function fetchManageApiRoleQueryRolePage(params: unknown) {
  return request<any>({
    url: 'role/queryRolePage',
    method: 'post',
    data: params
  })
}

/**
 * 角色-查询角色详情
 */
export function fetchManageApiRoleGetRole(params: unknown) {
  return request<any>({
    url: 'role/getRole',
    method: 'post',
    data: params
  })
}

/**
 * 角色-角色列表
 */
export function fetchManageApiRoleQueryRoleList(params: unknown) {
  return request<any>({
    url: 'role/queryRoleList',
    method: 'post',
    data: params
  })
}

/**
 * 字典-获得字典列表分页
 */
export function fetchManageApiDictQueryPage(params: unknown) {
  return request<any>({
    url: 'dict/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 字典-详情
 */
export function fetchManageApiDictDetail(params: unknown) {
  return request<any>({
    url: 'dict/detail',
    method: 'post',
    data: params
  })
}

/**
 * 字典-更新
 */
export function fetchManageApiDictUpdate(params: unknown) {
  return request<any>({
    url: 'dict/update',
    method: 'post',
    data: params
  })
}

/**
 * 字典-删除
 */
export function fetchManageApiDictDelete(params: unknown) {
  return request<any>({
    url: 'dict/delete',
    method: 'post',
    data: params
  })
}

/**
 * 导出记录-列表查询
 */
export function fetchManageApiExportLogQueryPage(params: unknown) {
  return request<any>({
    url: 'exportLog/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 导出记录-删除记录
 */
export function fetchManageApiExportLogDeleteLog(params: unknown) {
  return request<any>({
    url: 'exportLog/deleteLog',
    method: 'post',
    data: params
  })
}

/**
 * 日志-分页查询
 */
export function fetchManageApiOperateLogQueryPage(params: unknown) {
  return request<any>({
    url: 'operateLog/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 日志-从备份恢复
 */
export function fetchManageApiOperateLogOperateLogRecover(params: unknown) {
  return request<any>({
    url: 'operateLog/operateLogRecover',
    method: 'post',
    data: params
  })
}

/**
 * 日志-备份
 */
export function fetchManageApiOperateLogOperateLogBackup(params: unknown) {
  return request<any>({
    url: 'operateLog/operateLogBackup',
    method: 'post',
    data: params
  })
}

/**
 * 查询风补配置修改记录
 */
export function fetchManageApiTrackTrackRecord(params: unknown) {
  return request<any>({
    url: 'track/trackRecord',
    method: 'post',
    data: params
  })
}

/**
 * 通知管理-查询列表
 */
export function fetchManageApiNoticeQueryPage(params: unknown) {
  return request<any>({
    url: 'notice/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 通知管理-查询详情
 */
export function fetchManageApiNoticeQueryDetail(params: unknown) {
  return request<any>({
    url: 'notice/queryDetail',
    method: 'post',
    data: params
  })
}

/**
 * 通知管理-消息已读
 */
export function fetchManageApiNoticeRead(params: unknown) {
  return request<any>({
    url: 'notice/read',
    method: 'post',
    data: params
  })
}

/**
 * 通知管理-批量消息已读
 */
export function fetchManageApiNoticeBatchRead(params: unknown) {
  return request<any>({
    url: 'notice/batchRead',
    method: 'post',
    data: params
  })
}

/**
 * 通知管理-查询未读消息数量
 */
export function fetchManageApiNoticeQueryUnReadCount(params: unknown) {
  return request<any>({
    url: 'notice/queryUnReadCount',
    method: 'post',
    data: params
  })
}

/**
 * 获取公钥
 */
export function fetchManageApiSecurityGetRsaPublicKey(params: unknown) {
  return request<any>({
    url: 'security/getRsaPublicKey',
    method: 'post',
    data: params
  })
}

/**
 * 重要数据加密
 */
export function fetchManageApiSecurityImportantDataEncrypt(params: unknown) {
  return request<any>({
    url: 'security/importantDataEncrypt',
    method: 'post',
    data: params
  })
}

/**
 * 重要数据解密
 */
export function fetchManageApiSecurityImportantDataDecrypt(params: unknown) {
  return request<any>({
    url: 'security/importantDataDecrypt',
    method: 'post',
    data: params
  })
}

/**
 * 机构-获得机构列表分页
 */
export function fetchManageApiOrganizationQueryPage(params: unknown) {
  return request<any>({
    url: 'organization/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 机构-获得机构树
 */
export function fetchManageApiOrganizationGetOrganizationTree(params: unknown) {
  return request<any>({
    url: 'organization/getOrganizationTree',
    method: 'post',
    data: params
  })
}

/**
 * 机构-详情
 */
export function fetchManageApiOrganizationDetail(params: unknown) {
  return request<any>({
    url: 'organization/detail',
    method: 'post',
    data: params
  })
}

/**
 * 机构-新增
 */
export function fetchManageApiOrganizationAdd(params: unknown) {
  return request<any>({
    url: 'organization/add',
    method: 'post',
    data: params
  })
}

/**
 * 机构-更新
 */
export function fetchManageApiOrganizationUpdate(params: unknown) {
  return request<any>({
    url: 'organization/update',
    method: 'post',
    data: params
  })
}

/**
 * 机构-删除
 */
export function fetchManageApiOrganizationDelete(params: unknown) {
  return request<any>({
    url: 'organization/delete',
    method: 'post',
    data: params
  })
}

/**
 * 用户-不分页查询
 */
export function fetchManageApiUserQueryList(params: unknown) {
  return request<any>({
    url: 'user/queryList',
    method: 'post',
    data: params
  })
}

/**
 * 用户-分页查询
 */
export function fetchManageApiUserQueryPage(params: unknown) {
  return request<any>({
    url: 'user/queryPage',
    method: 'post',
    data: params
  })
}

/**
 * 用户-新增
 */
export function fetchManageApiUserAdd(params: unknown) {
  return request<any>({
    url: 'user/add',
    method: 'post',
    data: params
  })
}

/**
 * 用户-更新
 */
export function fetchManageApiUserUpdate(params: unknown) {
  return request<any>({
    url: 'user/update',
    method: 'post',
    data: params
  })
}

/**
 * 用户-用户明细查询
 */
export function fetchManageApiUserDetail(params: unknown) {
  return request<any>({
    url: 'user/detail',
    method: 'post',
    data: params
  })
}

/**
 * 用户-删除
 */
export function fetchManageApiUserDelete(params: unknown) {
  return request<any>({
    url: 'user/delete',
    method: 'post',
    data: params
  })
}

/**
 * 修改密码
 */
export function fetchManageApiUserUpdatePwd(params: unknown) {
  return request<any>({
    url: 'user/updatePwd',
    method: 'post',
    data: params
  })
}

/**
 * 批量手机号加密
 */
export function fetchManageApiUserEncryptPhone(params: unknown) {
  return request<any>({
    url: 'user/encryptPhone',
    method: 'post',
    data: params
  })
}
  