import { JSEncrypt } from 'jsencrypt'
import { request } from '../request'

interface IEncryptedDataByRSA {
  keyNo: string
  randNum: number
  rsaStrArr: string[]
}

/** 安全 - RSA公钥获取 */
export function fetchGetRsaPublicKey() {
  return request<Api.Auth.KeyInfo>({
    url: '/security/getRsaPublicKey',
    method: 'post',
    data: {}
  })
}

/** 将密码加密成RSA */
const encryptedDataByRSA = async (strArr: string[] = []): Promise<IEncryptedDataByRSA | null> => {
  const { data = {}, error } = await fetchGetRsaPublicKey()
  if (error) return null
  const jse = new JSEncrypt()
  const randNum = Math.round(Math.random() * 1000000)
  const { publicKey, keyNo } = data as Api.Auth.KeyInfo
  // rsa加密
  jse.setPublicKey(publicKey)
  const rsaStrArr = strArr.map((str) => {
    return jse.encrypt(str + randNum) as string
  })
  return {
    keyNo,
    randNum,
    rsaStrArr
  }
}

/** 短信 - 发送验证码 */
export async function fetchSendCode(phone: string) {
  return request<Api.Auth.UserInfo>({
    url: '/auth/sendCode',
    method: 'post',
    data: {
      phone
    }
  })
}

/** 用户-密码重置 */
export async function fetchResetPwd(pwd: string, phone: string, code: string) {
  const encryptedData = await encryptedDataByRSA([pwd])
  if (!encryptedData) return null
  const { keyNo, randNum, rsaStrArr } = encryptedData
  return request<Api.Auth.UserInfo>({
    url: '/user/resetPwd',
    method: 'post',
    data: {
      phone,
      code,
      pwd: rsaStrArr[0],
      keyNo,
      random: randNum
    }
  })
}

/** 用户-密码修改 */
export async function fetchUpdatePwd(oldPwd: string, pwd: string, userId: string) {
  const encryptedData = await encryptedDataByRSA([oldPwd, pwd])
  if (!encryptedData) return null
  const { keyNo, randNum, rsaStrArr } = encryptedData
  return request<Api.Auth.UserInfo>({
    url: '/user/updatePwd',
    method: 'post',
    data: {
      userId,
      password: rsaStrArr[0],
      newPassword: rsaStrArr[1],
      keyNo,
      random: randNum
    }
  })
}

/** 用户-管理员重置密码 */
export async function fetchAdminResetPwd(pwd: string, phone: string) {
  const encryptedData = await encryptedDataByRSA([pwd])
  if (!encryptedData) return null
  const { keyNo, randNum, rsaStrArr } = encryptedData
  return request<Api.Auth.UserInfo>({
    url: '/user/adminResetPwd',
    method: 'post',
    data: {
      phone,
      pwd: rsaStrArr[0],
      keyNo,
      random: randNum
    }
  })
}

/** 密码登录 */
export async function fetchLogin(userName: string, password: string, verifyCode: string) {
  const encryptedData = await encryptedDataByRSA([password])
  if (!encryptedData) return null
  const { keyNo, randNum, rsaStrArr } = encryptedData
  return request<Api.Auth.UserInfo>({
    url: '/auth/loginManage',
    method: 'post',
    data: {
      loginNumber: userName,
      pwd: rsaStrArr[0],
      keyNo,
      random: randNum,
      verifyCode
    }
  })
}

/** 退出登录 */
export function fetchLogout() {
  return request<Api.Auth.LoginToken>({
    url: '/auth/logout',
    method: 'post',
    data: {}
  })
}
