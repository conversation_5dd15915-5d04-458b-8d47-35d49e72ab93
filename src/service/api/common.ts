import { request } from '../request'

/** 字典 - 列表 */
export function fetchDictQueryList(dictType: 'nation' | 'partyPosition' | 'relations') {
  return request<any>({
    url: '/dict/queryList',
    method: 'post',
    data: { dictType }
  })
}

/** 字典 - 列表 */
export function fetchAttachmentUpload(formData: FormData) {
  return request<any>({
    url: '/attachment/upload',
    method: 'post',
    data: formData
  })
}

/** 附件列表查询 */

export function fetchAttachmentQuery(params: unknown) {
  return request<any>({
    url: '/attachment/query',
    method: 'post',
    data: params
  })
}

/** 工作流-查询审核详情（审核进度） */

export function fetchFlowQueryAuditInfo(flowInstanceId: string) {
  return request<any>({
    url: '/flow/queryAuditInfo',
    method: 'post',
    data: {
      flowInstanceId
    }
  })
}

/** 工作流-撤回 */

export function fetchFlowWithdraw(instanceId: string) {
  return request<any>({
    url: '/flow/withdraw',
    method: 'post',
    data: {
      instanceId
    }
  })
}

/** 统一下载模板接口 */
export function fetchFileCentreDownload(type: 'COMPANY_INTEGRITY' | 'VITALITY_ASSESS' | 'CITY_ASSES' | 'EXAM') {
  return request<any>({
    // url: '/files/fileCentreDownload',
    url: '/files/templateDownload',
    method: 'post',
    data: {
      excelTemplateType: type
    },
    responseType: 'arraybuffer'
  })
}

/** 用户信息 - 导入 */
export function fetchUserImportExcel(formData: FormData) {
  return request<any>({
    url: '/user/importExcel',
    method: 'post',
    data: formData
  })
}

/** 附件删除 */
export function fileDeleteById(params: unknown) {
  return request<any>({
    url: '/attachment/deleteById',
    method: 'post',
    data: params
  })
}
