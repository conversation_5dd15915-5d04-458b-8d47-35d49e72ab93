import { request } from '../request'

/** 导出 */
export function queryPageByRegionExport(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByRegionExport',
    method: 'post',
    data: params
  })
}
//贷款统计查询(机构)-导出

export function queryPageByBankExport(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByBankExport',
    method: 'post',
    data: params
  })
}
//贷款统计查询(区域)-导出

export function queryPageByEntExport(params: unknown) {
  return request<any>({
    url: 'loanStatistic/queryPageByEntExport',
    method: 'post',
    data: params
  })
}
//风补统计分页查询(区域)-导

export function riskStatisticqueryPageByRegionExport(params: unknown) {
  return request<any>({
    url: 'riskStatistic/queryPageByRegionExport',
    method: 'post',
    data: params
  })
}
//风补统计分页查询(银行)-导出

export function riskStatisticqueryPageByBankExport(params: unknown) {
  return request<any>({
    url: 'riskStatistic/queryPageByBankExport',
    method: 'post',
    data: params
  })
}
//导出历史
export function exportLog(params: unknown) {
  return request<any>({
    url: 'exportLog/queryPage',
    method: 'post',
    data: params
  })
}
//记录导出配置
export function addOrUpdate(params: unknown) {
  return request<any>({
    url: 'exportConfig/addOrUpdate',
    method: 'post',
    data: params
  })
}
//查询导出
export function exportConfiggetInfo(params: unknown) {
  return request<any>({
    url: 'exportConfig/getInfo',
    method: 'post',
    data: params
  })
}
//导出删除
export function deleteLog(params: unknown) {
  return request<any>({
    url: 'exportLog/deleteLog',
    method: 'post',
    data: params
  })
}
