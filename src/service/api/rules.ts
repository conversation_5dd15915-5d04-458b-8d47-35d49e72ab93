import { request } from '../request';

//查询工作流节点
export function backNodeList  (params: unknown) {
  return request<any>({
    url: '/flow/execute/getBackNodeList/'+params,
    method: 'get',
  });
}

/**
 * 检验验收细则 - 列表
 *
 * @param params
 */
export function fetchQueryCheck(params: unknown) {
  return request<any>({
    url: '/check/queryCheck',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 历史版本列表
 *
 * @param params
 */
export function fetchQueryCheckVersion(params: unknown) {
  return request<any>({
    url: '/check/queryVersion',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 最新版本
 *
 * @param params
 */
export function fetchCheckLatestVersion(params: unknown) {
  return request<any>({
    url: '/check/queryLatestVersion',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 删除
 *
 * @param params
 */
export function fetchDeleteCheck(params: unknown) {
  return request<any>({
    url: '/check/deleteProduct',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 产品列表
 *
 * @param params
 */
export function fetchInsertProduct(params: unknown) {
  return request<any>({
    url: '/productTask/getProduct',
    method: 'get',
    data: params
  });
}

/**
 * 检验验收细则 - 产品新增
 *
 * @param params
 */
export function fetchInsertCheck(params: unknown) {
  return request<any>({
    url: '/check/insertProduct',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 产品详情
 *
 * @param params
 */
export function fetchQueryCheckDetail(params: unknown) {
  return request<any>({
    url: '/check/queryCheckDetail',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 产品编辑
 *
 * @param params
 */
export function fetchUpdateProduct(params: unknown) {
  return request<any>({
    url: '/check/updateProduct',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 检验内容详情
 *
 * @param params
 */
export function fetchQueryCheckDetails(params: unknown) {
  return request<any>({
    url: '/check/queryCheckDetails',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 检验内容编辑
 *
 * @param params
 */
export function fetchUpdateInpect(params: unknown) {
  return request<any>({
    url: '/check/updateInpect',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 验收内容删除
 *
 * @param params
 */
export function fetchDeleteInspect(params: unknown) {
  return request<any>({
    url: '/check/deleteInspect',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 条件审查删除
 *
 * @param params
 */
export function fetchDeleteCondition(params: unknown) {
  return request<any>({
    url: '/check/deleteInspectCondition',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收细则 - 质量符合性删除
 *
 * @param params
 */
export function fetchDeleteQuality(params: unknown) {
  return request<any>({
    url: '/check/deleteInspectQuality',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收计划 - 列表
 *
 * @param params
 */
export function fetchPlanList(params: unknown) {
  return request<any>({
    url: '/checkPlan/queryPlan',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收计划 - 审核
 *
 * @param params
 */
export function fetchPlanHandle(params: unknown) {
  return request<any>({
    url: '/checkPlan/handle',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收计划 - 删除
 *
 * @param params
 */
export function fetchPlanDelete(params: unknown) {
  return request<any>({
    url: '/checkPlan/deletePlan',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收计划 - 新增
 *
 * @param params
 */
export function fetchPlanAdd(params: unknown) {
  return request<any>({
    url: '/checkPlan/insertPlan',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收计划 - 编辑
 *
 * @param params
 */
export function fetchPlanUpdate(params: unknown) {
  return request<any>({
    url: '/checkPlan/updatePlan',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收计划 - 详情
 *
 * @param params
 */
export function fetchPlanQuery(params: unknown) {
  return request<any>({
    url: '/checkPlan/queryPlanDetail',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收计划 - 验收内容下拉框
 *
 * @param params
 */
export function fetchPlanInspectName(params: unknown) {
  return request<any>({
    url: '/check/queryInspectName',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请 - 合同下拉框
 *
 * @param params
 */
export function fetchApplyContract(params: unknown) {
  return request<any>({
    url: '/checkApply/queryContract',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请-分页查询
 *
 * @param params
 */
export function fetchQueryCheckApplyPage(params: unknown) {
  return request<any>({
    url: '/checkApply/queryApply',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请-新增
 *
 * @param params
 */
export function fetchAddCheckApply(params: unknown) {
  return request<any>({
    url: '/checkApply/insertApply',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请-更新
 *
 * @param params
 */
export function fetchUpdateCheckApply(params: unknown) {
  return request<any>({
    url: '/checkApply/updateApply',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请-查询详情
 *
 * @param params
 */
export function fetchGetCheckApplyById(id: number) {
  return request<any>({
    url: '/checkApply/queryApplyDetail',
    method: 'post',
    data: {
      id
    }
  });
}

/**
 * 检验申请-获取工艺卡号
 *
 * @param params
 */
export function fetchWorkOrderNoProApply(params: unknown) {
  return request<any>({
    url: '/mes/workOrderNoPro',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请-生成编号
 *
 * @param params
 */
export function fetchCreateCheckNumber(params: unknown) {
  return request<any>({
    url: '/checkApply/createCheckNumber',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请-获取不合格产品处理列表
 *
 * @param params
 */
export function getUnqualifiedList(params: unknown) {
  return request<any>({
    url: '/checkApply/getUnqualifiedList',
    method: 'get',
    data: params
  });
}

/**
 * 检验申请-通过产品名称型号查询验收对象
 *
 * @param params
 */
export function fetchObjByNameAndModelApply(params: unknown) {
  return request<any>({
    url: '/check/getObjByNameAndModel',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请-受理条件审查-审核
 *
 * @param params
 */
export function fetchAuditApplyCondition(params: unknown) {
  return request<any>({
    url: '/checkApply/auditApplyCondition',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请-质量符合性检查-审核
 *
 * @param params
 */
export function fetchAuditApplyQuality(params: unknown) {
  return request<any>({
    url: '/checkApply/auditApplyQuality',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请 - 流程审核
 *
 * @param params
 */
export function fetchAuditApplyHandle(params: unknown) {
  return request<any>({
    url: '/checkApply/handle',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请-删除
 *
 * @param id
 */
export function fetchDeleteCheckApplyById(id: number) {
  return request<any>({
    url: '/checkApply/deleteApply',
    method: 'post',
    data: {
      id
    }
  });
}

/**
 * 检验申请-工序质检
 *
 * @param params
 */
export function fetchProcessesBalance(params: unknown) {
  return request<any>({
    url: '/mes/getProcessesBalance',
    method: 'post',
    data: params
  });
}
/**
 * 检验申请-工序质检
 *
 * @param params
 */
export function getProcessesPart(params: unknown) {
  return request<any>({
    url: '/mes/getProcessesPart',
    method: 'post',
    data: params
  });
}
export function getHydrostaticTest(params: unknown) {
  return request<any>({
    url: '/mes/getHydrostaticTest',
    method: 'post',
    data: params
  });
}

/**
 * 检验申请-工单终检
 *
 * @param params
 */
export function fetchPumpCheckPart(params: unknown) {
  return request<any>({
    url: '/mes/getPumpCheckPart',
    method: 'post',
    data: params
  });
}
/**
 * 检验细则-审批
 *
 * @param params
 */
export function checkhandle(params: unknown) {
  return request<any>({
    url: '/check/handle',
    method: 'post',
    data: params
  });
}

/**
 * 军检验收卡片-查询
 *
 * @param params
 */
export function fetchQueryCheckCardPage(params: unknown) {
  return request<any>({
    url: '/checkCard/queryCard',
    method: 'post',
    data: params
  });
}

/**
 * 军检验收卡片-详情
 *
 * @param params
 */
export function fetchGetCheckCardById(id: number) {
  return request<any>({
    url: '/checkCard/queryCardDetail',
    method: 'post',
    data: {
      id
    }
  });
}

/**
 * 军检验收卡片-制卡
 *
 * @param params
 */
export function fetchExportApplyCard(params: unknown) {
  return request<any>({
    url: '/checkExportCard/exportApplyCard',
    method: 'post',
    data: params
  });
}

/**
 * 军检验收卡片-产品校验单
 *
 * @param params
 */
export function fetchExportProductCard(params: unknown) {
  return request<any>({
    url: '/checkExportCard/exportProductCard',
    method: 'post',
    data: params
  });
}

/**
 * 合格证申请-查询
 *
 * @param params
 */
export function fetchQueryCheckCardApplyPage(params: unknown) {
  return request<any>({
    url: '/checkCardApply/queryCardApply',
    method: 'post',
    data: params
  });
}

/**
 * 合格证申请-新增
 *
 * @param params
 */
export function fetchCardApplyAdd(params: unknown) {
  return request<any>({
    url: '/checkCardApply/insertCardApply',
    method: 'post',
    data: params
  });
}

/**
 * 合格证申请-删除
 *
 * @param params
 */
export function fetchCardApplyDelete(params: unknown) {
  return request<any>({
    url: '/checkCardApply/deleteCardApply',
    method: 'post',
    data: params
  });
}

/**
 * 合格证申请-编辑
 *
 * @param params
 */
export function fetchCardApplyUpdate(params: unknown) {
  return request<any>({
    url: '/checkCardApply/updateCardApply',
    method: 'post',
    data: params
  });
}

/**
 * 合格证申请-详情
 *
 * @param params
 */
export function fetchCardApplyQuery(params: unknown) {
  return request<any>({
    url: '/checkCardApply/queryCardApplyDetail',
    method: 'post',
    data: params
  });
}

/**
 * 合格证申请-相关资料
 *
 * @param params
 */
export function fetchApplyRelevantFile(params: unknown) {
  return request<any>({
    url: '/checkCardApply/queryRelevantFile',
    method: 'post',
    data: params
  });
}

/**
 * 合格证申请-审核
 *
 * @param params
 */
export function fetchCardApplyAudit(params: unknown) {
  return request<any>({
    url: '/checkCardApply/auditCardApply',
    method: 'post',
    data: params
  });
}
export function fetchCheckCardApplyHandle(params: unknown) {
  return request<any>({
    url: '/checkCardApply/handle',
    method: 'post',
    data: params
  });
}

/**
 * 合格证管理-内部军检合格证-查询
 *
 * @param params
 */
export function fetchQueryCertificatePage(params: unknown) {
  return request<any>({
    url: '/checkCardManage/queryCertificate',
    method: 'post',
    data: params
  });
}

/**
 * 合格证管理-内部军检合格证-详情
 *
 * @param params
 */
export function fetchQueryCertificateDetail(params: unknown) {
  return request<any>({
    url: '/checkCardManage/queryCertificateDetail',
    method: 'post',
    data: params
  });
}

/**
 * 合格证管理-内部证书制证
 *
 * @param params
 */
export function fetchExportInsideCard(params: unknown) {
  return request<any>({
    url: '/checkExportCard/exportInsideCard',
    method: 'post',
    data: params
  });
}

/**
 * 合格证管理-军检制证
 *
 * @param params
 */
export function fetchExportJqCard(params: unknown) {
  return request<any>({
    url: '/checkExportCard/exportJqCard',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-出厂检查-分页查询
 *
 * @param params
 */
export function fetchQueryCheckFactoryPage(params: unknown) {
  return request<any>({
    url: '/checkFactory/queryPageFactory',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-出厂检查-新增
 *
 * @param params
 */
export function fetchFactoryAdd(params: unknown) {
  return request<any>({
    url: '/checkFactory/insertFactory',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-出厂检查-删除
 *
 * @param params
 */
export function fetchFactoryDelete(params: unknown) {
  return request<any>({
    url: '/checkFactory/deleteFactory',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-出厂检查-编辑
 *
 * @param params
 */
export function fetchFactoryUpdate(params: unknown) {
  return request<any>({
    url: '/checkFactory/updateFactory',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-出厂检查-详情
 *
 * @param params
 */
export function fetchFactoryQuery(params: unknown) {
  return request<any>({
    url: '/checkFactory/queryFactoryDetail',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-出厂检查-审核
 *
 * @param params
 */
export function fetchFactoryAudit(params: unknown) {
  return request<any>({
    url: '/checkFactory/auditFactory',
    method: 'post',
    data: params
  });
}
export function fetchFactoryHandle(params: unknown) {
  return request<any>({
    url: '/checkFactory/handle',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-交接发运-分页查询
 *
 * @param params
 */
export function fetchShippingList(params: unknown) {
  return request<any>({
    url: '/shipping/queryPageShipping',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-交接发运-新增
 *
 * @param params
 */
export function fetchShippingAdd(params: unknown) {
  return request<any>({
    url: '/shipping/insertShipping',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-交接发运-删除
 *
 * @param params
 */
export function fetchShippingDelete(params: unknown) {
  return request<any>({
    url: '/shipping/deleteShipping',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-交接发运-编辑
 *
 * @param params
 */
export function fetchShippingUpdate(params: unknown) {
  return request<any>({
    url: '/shipping/updateShipping',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-交接发运-详情
 *
 * @param params
 */
export function fetchShippingQuery(params: unknown) {
  return request<any>({
    url: '/shipping/queryShippingDetail',
    method: 'post',
    data: params
  });
}

/**
 * 检验验收-交接发运-审核
 *
 * @param params
 */
export function fetchShippingAudit(params: unknown) {
  return request<any>({
    url: '/shipping/auditShipping',
    method: 'post',
    data: params
  });
}
export function fetchShippingHandle(params: unknown) {
  return request<any>({
    url: '/shipping/handle',
    method: 'post',
    data: params
  });
}
