import { request } from '../request';

/**
 * 合格供方-分页查询
 *
 * @param params
 */
export function fetchQueryCompanyPage(params: unknown) {
  return request<any>({
    url: '/company/queryPage',
    method: 'post',
    data: params
  });
}


/**
 * 合格供方-新增
 *
 * @param params
 */
export function fetchAddCompany(params: unknown) {
  return request<any>({
    url: '/company/add',
    method: 'post',
    data: params
  });
}

/**
 * 合格供方-更新
 *
 * @param params
 */
export function fetchUpdateCompany(params: unknown) {
  return request<any>({
    url: '/company/update',
    method: 'post',
    data: params
  });
}

/**
 * 合格供方-查询详情
 *
 * @param params
 */
export function fetchGetCompanyById(id: number) {
  return request<any>({
    url: '/company/detail',
    method: 'post',
    data: {
      id
    }
  });
}

/**
 * 合格供方-删除
 *
 * @param parar
 */
export function fetchDeleteCompanyById(params: unknown) {
  return request<any>({
    url: '/company/delete',
    method: 'post',
    data: params
  });
}


/**
 * 合格供方-供方单位相关证书查询
 *
 * @param params
 */
export function fetchQueryCompanyCertificateList(params: unknown) {
  return request<any>({
    url: '/companyCertificate/queryList',
    method: 'post',
    data: params
  });
}

/**
 * 合格供方-供方单位相关证书分页查询
 *
 * @param params
 */
export function fetchQueryCompanyCertificatePage(params: unknown) {
  return request<any>({
    url: '/companyCertificate/queryPage',
    method: 'post',
    data: params
  });
}

/**
 * 合格供方-供方单位相关证书查询-新增
 *
 * @param params
 */
export function fetchAddCompanyCertificate(params: unknown) {
  return request<any>({
    url: '/companyCertificate/add',
    method: 'post',
    data: params
  });
}

/**
 * 合格供方-供方单位相关证书查询-修改
 *
 * @param params
 */
export function fetchUpdateCompanyCertificate(params: unknown) {
  return request<any>({
    url: '/companyCertificate/update',
    method: 'post',
    data: params
  });
}

/**
 * 合格供方-供方单位相关证书查询-通过id查询
 *
 * @param params
 */
export function fetchGetCompanyCertificateById(id: number) {
  return request<any>({
    url: '/companyCertificate/detail',
    method: 'post',
    data: {
      id
    }
  });
}

/**
 * 合格供方-供方单位相关证书查询-删除
 *
 * @param id
 */
export function fetchDeleteCompanyCertificateById(id: number) {
  return request<any>({
    url: '/companyCertificate/delete',
    method: 'post',
    data: {
      id
    }
  });
}


/**
 * 合格供方-供方单位单位资格保持情况分页查询
 *
 * @param params
 */
export function fetchQueryCompanyQualificatePage(params: unknown) {
  return request<any>({
    url: '/companyQualificate/queryPage',
    method: 'post',
    data: params
  });
}

/**
 * 合格供方-供方单位单位资格保持情况-新增
 *
 * @param params
 */
export function fetchAddCompanyQualificate(params: unknown) {
  return request<any>({
    url: '/companyQualificate/add',
    method: 'post',
    data: params
  });
}

/**
 * 合格供方-供方单位单位资格保持情况-修改
 *
 * @param params
 */
export function fetchUpdateCompanyQualificate(params: unknown) {
  return request<any>({
    url: '/companyQualificate/update',
    method: 'post',
    data: params
  });
}

/**
 * 合格供方-供方单位资格保持情况-通过id查询
 *
 * @param params
 */
export function fetchGetCompanyQualificateById(id: number) {
  return request<any>({
    url: '/companyQualificate/detail',
    method: 'post',
    data: {
      id
    }
  });
}

/**
 * 合格供方-供方单位资格保持情况-删除
 *
 * @param id
 */
export function fetchDeleteCompanyQualificateById(id: number) {
  return request<any>({
    url: '/companyQualificate/delete',
    method: 'post',
    data: {
      id
    }
  });
}

/**
 * 失信名单-分页查询
 *
 * @param params
 */
export function fetchQueryIntegrityCompanyPage(params: unknown) {
  return request<any>({
    url: '/company/queryIntegrityPage',
    method: 'post',
    data: params
  });
}

/**
 * 失信名单-新增
 *
 * @param params
 */
export function fetchAddIntegrityCompany(params: unknown) {
  return request<any>({
    url: '/company/addIntegrity',
    method: 'post',
    data: params
  });
}

/**
 * 失信名单-修改
 *
 * @param params
 */
export function fetchUpdateIntegrityCompany(params: unknown) {
  return request<any>({
    url: '/company/updateIntegrity',
    method: 'post',
    data: params
  });
}

/**
 * 合格供方、失信名单-通过id查询所有信息
 *
 * @param params
 */
export function fetchGetCompanyApproveDetailById(id: number) {
  return request<any>({
    url: '/company/approveDetail',
    method: 'post',
    data: {
      id
    }
  });
}

/**
 * 失信名单 - 导入
 *
 * @param params
 */
export function fetchCompanyImportExcel(params: unknown) {
  return request<any>({
    url: '/company/importExcel',
    method: 'post',
    data: params
  });
}
/**
 * 待审核 - 导入
 *
 * @param params
 */
export function importCompany(params: unknown) {
  return request<any>({
    url: '/company/importCompany',
    method: 'post',
    data: params
  });
}


/**
 * 合格供方审核
 *
 * @param params
 */
export function fetchCompanyApprove(params: unknown) {
  return request<any>({
    url: '/company/approve',
    method: 'post',
    data: params
  });
}
export function companyhandle(params: unknown) {
  return request<any>({
    url: '/company/handle',
    method: 'post',
    data: params
  });
}
