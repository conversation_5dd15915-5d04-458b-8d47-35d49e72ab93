import { request } from '../request';

/**
 * 权限-新增菜单权限
 *
 * @param params
 */
export function fetchAddElement(params: unknown) {
  return request<any>({
    url: '/element/addElement',
    method: 'post',
    data: params
  });
}

/**
 * 权限-更新菜单权限
 *
 * @param params
 */
export function fetchUpdateElement(params: unknown) {
  return request<any>({
    url: '/element/updateElement',
    method: 'post',
    data: params
  });
}

/**
 * 权限-排序菜单权限
 *
 * @param params
 */
export function fetchSortElement(params: unknown) {
  return request<any>({
    url: '/element/sortElement',
    method: 'post',
    data: params
  });
}

/**
 * 权限-删除菜单权限
 *
 * @param params
 */
export function fetchDeleteElementById(id: number) {
  return request<any>({
    url: '/element/deleteElement',
    method: 'post',
    data: {
      elementId: id
    }
  });
}

/**
 * 权限-查询菜单权限详情
 *
 * @param params
 */
export function fetchGetElementById(id: number) {
  return request<any>({
    url: '/element/getElement',
    method: 'post',
    data: {
      elementId: id
    }
  });
}

/**
 * 权限-查询菜单权限列表
 *
 * @param params
 */
export function fetchQueryElementList(params: unknown) {
  return request<any>({
    url: '/element/queryElementList',
    method: 'post',
    data: params
  });
}

/**
 * 权限-查询接口权限列表
 *
 * @param params
 */
export function fetchQueryApiList(params: unknown) {
  return request<any>({
    url: '/element/queryApiList',
    method: 'post',
    data: params
  });
}

/**
 * 角色-新增角色
 *
 * @param params
 */
export function fetchAddRole(params: unknown) {
  return request<any>({
    url: '/role/addRole',
    method: 'post',
    data: params
  });
}

/**
 * 角色-更新角色
 *
 * @param params
 */
export function fetchUpdateRole(params: unknown) {
  return request<any>({
    url: '/role/updateRole',
    method: 'post',
    data: params
  });
}

/**
 * 角色-删除角色
 *
 * @param id
 */
export function fetchDeleteRoleById(id: number) {
  return request<any>({
    url: '/role/deleteRole',
    method: 'post',
    data: {
      roleId: id
    }
  });
}

/** 角色-分页查询角色列表 */
export function fetchQueryRolePage(params: unknown) {
  return request<any>({
    url: '/role/queryRolePage',
    method: 'post',
    data: params
  });
}

/** 角色-角色列表 */
export function fetchQueryRoleList() {
  return request<any>({
    url: '/role/queryRoleList',
    method: 'post',
    data: {}
  });
}

/**
 * 角色-查询角色详情
 *
 * @param params
 */
export function fetchGetRoleById(id: number) {
  return request<any>({
    url: '/role/getRole',
    method: 'post',
    data: {
      roleId: id
    }
  });
}

/**
 * 用户 - 新增
 *
 * @param params
 */
export function fetchUserAdd(params: unknown) {
  return request<any>({
    url: '/user/add',
    method: 'post',
    data: params
  });
}

/**
 * 用户 - 编辑
 *
 * @param params
 */
export function fetchUserUpdate(params: unknown) {
  return request<any>({
    url: '/user/update',
    method: 'post',
    data: params
  });
}

/**
 * 用户 - 详情
 *
 * @param id
 */
export function fetchUserDetailById(id: number) {
  return request<any>({
    url: '/user/detail',
    method: 'post',
    data: { id }
  });
}

/**
 * 用户 - 删除
 *
 * @param id
 */
export function fetchUserDelete(id: number) {
  return request<any>({
    url: '/user/delete',
    method: 'post',
    data: { id }
  });
}

/**
 * 用户 - 状态更新
 *
 * @param id
 */
export function fetchUserUpdateStatus(id: number) {
  return request<any>({
    url: '/user/updateStatus',
    method: 'post',
    data: { id }
  });
}

/** 用户 - 列表(分页) */
export function fetchUserQueryPage(params: unknown) {
  return request<any>({
    url: '/user/queryPage',
    method: 'post',
    data: params
  });
}

/** 用户 - 不分页列表 */
export function fetchUserList(params: unknown) {
  return request<any>({
    url: '/user/queryList',
    method: 'post',
    data: params
  });
}

/** 操作日志-日志列表查询 */
export function fetchOperateLogPage(params: unknown) {
  return request<any>({
    url: '/operateLog/page',
    method: 'post',
    data: params
  });
}


/** 字典 - 列表 */
export function fetchQueryDictPage(params: unknown) {
  return request<any>({
    url: '/dict/queryPage',
    method: 'post',
    data: params
  });
}

/**
 * 字典 - 详情
 *
 * @param id
 */
export function fetchDictDetailByType(dictType: string) {
  return request<any>({
    url: '/dict/detail',
    method: 'post',
    data: { dictType }
  });
}

/**
 * 字典 - 编辑
 *
 * @param params
 */
export function fetchDictUpdate(params: unknown) {
  return request<any>({
    url: '/dict/update',
    method: 'post',
    data: params
  });
}

/**
 * 字典 - 删除
 *
 * @param id
 */
export function fetchDictDelete(id: number) {
  return request<any>({
    url: '/dict/delete',
    method: 'post',
    data: { id }
  });
}


/** 机构信息 - 机构列表 */
export function fetchOrgQueryList(params: unknown) {
  return request<any>({
    url: '/organization/getOrganizationTree',
    method: 'post',
    data: params
  });
}

/** 机构信息 - 新增 */
export function fetchOrgAdd(params: unknown) {
  return request<any>({
    url: '/organization/add',
    method: 'post',
    data: params
  });
}

/** 机构信息 - 编辑 */
export function fetchOrgUpdate(params: unknown) {
  return request<any>({
    url: '/organization/update',
    method: 'post',
    data: params
  });
}

/** 机构信息 - 详情 */
export function fetchOrgDetailById(id: number) {
  return request<any>({
    url: '/organization/detail',
    method: 'post',
    data: { id }
  });
}

/** 机构信息 - 删除 */
export function fetchOrgDeleteById(id: number) {
  return request<any>({
    url: '/organization/delete',
    method: 'post',
    data: { id }
  });
}

/** 日志-分页查询 */
export function fetchQueryOperateLogPage(params: unknown) {
  return request<any>({
    url: '/operateLog/queryPage',
    method: 'post',
    data: params
  });
}
