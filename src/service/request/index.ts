import { createFlatRequest } from '@yc/axios'
import { sleep } from '@/utils/common'
import { localStg } from '@/utils/storage'
import { getServiceBaseURL } from '@/utils/service'
import { BACKEND_SUCCESS_CODE } from '~/packages/axios/src/constant'
import { useAuthStore } from '@/store/modules/auth'

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y'
const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy)

export const request = createFlatRequest<App.Service.Response>(
  {
    baseURL,
    headers: {}
  },
  {
    async onRequest(config) {
      const { headers } = config

      // set token
      const token = localStg.get('token')
      const Authorization = token ? `Bearer ${token}` : null
      Object.assign(headers, { Authorization })
      // if (config.method === 'post' || config.method === 'put') {
      //   const requestObj = {
      //     url: config.url,
      //     data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
      //     time: new Date().getTime()
      //   }
      //   const requestSize = Object.keys(JSON.stringify(requestObj)).length // 请求数据大小
      //   const limitSize = 5 * 1024 * 1024 // 限制存放数据5M
      //   if (requestSize >= limitSize) {
      //     console.warn(`[${config.url}]: ` + '请求数据大小超出允许的5M限制，无法进行防重复提交验证。')
      //     return config
      //   }
      //   const sessionObj = cache.session.getJSON('sessionObj')
      //   if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
      //     cache.session.setJSON('sessionObj', requestObj)
      //   } else {
      //     const s_url = sessionObj.url // 请求地址
      //     const s_data = sessionObj.data // 请求数据
      //     const s_time = sessionObj.time // 请求时间
      //     const interval = 1000 // 间隔时间(ms)，小于此时间视为重复提交
      //     if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
      //       const message = '数据正在处理，请勿重复提交'
      //       return Promise.reject(new Error(message))
      //     } else {
      //       cache.session.setJSON('sessionObj', requestObj)
      //     }
      //   }
      // }
      return config
    },
    isBackendSuccess(response: any) {
      // when the backend response code is "0000", it means the request is success
      // you can change this logic by yourself
      if (response.headers['content-type'].includes('application/json')) {
        // return response.data.rspCd === BACKEND_SUCCESS_CODE;
        return response.data.errcode === 0 || response.data.errcode === 200
      }
      return true
    },
    async onBackendFail(_response: any) {
      // 当后端响应码不是“00000”时，表示请求失败
      // 例如：令牌已过期、刷新令牌和重试请求
      if (_response.data.errcode === 3104 || _response.data.errcode === 3005) {
        const authStore = useAuthStore()
        await sleep(500)
        await authStore.logout()
      }
    },
    transformBackendResponse(response: any) {
      if (response.headers['content-type'] === 'application/json') {
        return response.data.data
      }
      return response
    },
    onError(error: any) {
      // when the request is fail, you can show error message
      console.log('onError', error)
      const status = error?.response?.status
      if (status === 401) {
        const authStore = useAuthStore()
        authStore.logout(false)
        return
      }
      let message = error.message
      // show backend error message
      if (error.code !== BACKEND_SUCCESS_CODE) {
        const response = JSON.parse(error?.response?.response)
        message = response?.errmsg || message
        if (response.errcode == 3104) {
          message = '登录超时！请重新登录'
        }
      }
      window.$message?.error(message)
    }
  }
)
