{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"@/*": ["./src/*"], "~/*": ["./*"]}, "resolveJsonModule": true, "types": ["vite/client", "node", "unplugin-icons/types/vue"], "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.vue", "src/typings/*.d.ts", "build/**/*.ts", "packages/**/*.ts", "vite.config.ts"], "exclude": ["node_modules", "dist"]}