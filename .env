VITE_BASE_URL=/manage

VITE_APP_TITLE=知识价值贷款风险补偿管理平台

VITE_APP_DESC=YCAdmin is a fresh and elegant admin template

# the prefix of the icon name
VITE_ICON_PREFIX=icon

# the prefix of the local svg icon component, must include VITE_ICON_PREFIX
# format {VITE_ICON_PREFIX}-{local icon name}
VITE_ICON_LOCAL_PREFIX=icon-local

# auth route mode: static ｜ dynamic
VITE_AUTH_ROUTE_MODE=dynamic

# static auth route home
VITE_ROUTE_HOME=science_list

# default menu icon
VITE_MENU_ICON=mdi:menu

# whether to enable http proxy when is dev mode
VITE_HTTP_PROXY=Y

# vue-router mode: hash | history | memory
VITE_ROUTER_HISTORY_MODE=hash

