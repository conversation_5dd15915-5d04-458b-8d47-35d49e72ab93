{"printWidth": 140, "tabWidth": 2, "useTabs": false, "singleQuote": true, "quoteProps": "as-needed", "trailingComma": "none", "bracketSpacing": true, "jsxBracketSameLine": false, "arrowParens": "always", "rangeStart": 0, "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "endOfLine": "auto", "semi": false, "usePrettierrc": true, "singleAttributePerLine": true, "bracketSameLine": false, "space": {"beforeFunctionParen": true}, "overrides": [{"files": "*.json", "options": {"tabWidth": 4}}, {"files": "*.md", "options": {"printWidth": 100}}]}