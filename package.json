{"name": "yc-vue3-admin", "private": true, "version": "1.0.0", "type": "module", "packageManager": "pnpm@8.14.3", "scripts": {"dev": "vite --mode test", "dev:prod": "vite --mode prod", "build": "vite build --mode prod", "build:test": "vite build --mode test", "preview": "vite preview", "preinstall": "npx only-allow pnpm", "cleanup": "yc cleanup", "api": "yc build-ig-api"}, "dependencies": {"@better-scroll/core": "2.5.1", "@ckeditor/ckeditor5-build-decoupled-document": "^41.2.1", "@ckeditor/ckeditor5-theme-lark": "^41.2.1", "@ckeditor/ckeditor5-vue": "^5.1.0", "@ckeditor/vite-plugin-ckeditor5": "^0.1.3", "@iconify/vue": "4.1.1", "@vicons/ionicons5": "^0.13.0", "@vueuse/core": "10.9.0", "@yc/axios": "workspace:*", "@yc/color": "workspace:*", "@yc/hooks": "workspace:*", "@yc/materials": "workspace:*", "@yc/utils": "workspace:*", "autofit.js": "^3.2.8", "clipboard": "2.0.11", "dayjs": "1.11.10", "echarts": "5.5.0", "gm-crypto": "^0.1.12", "html2pdf.js": "^0.10.1", "js-base64": "^3.7.7", "jsencrypt": "^3.3.2", "jspdf": "^2.5.1", "lodash-es": "4.17.21", "naive-ui": "2.41.0", "nprogress": "0.2.0", "pinia": "2.1.7", "qs": "6.12.0", "vue": "3.4.21", "vue-draggable-plus": "0.6.0", "vue-i18n": "9.10.2", "vue-router": "4.3.0", "vue-types": "^6.0.0", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.193", "@soybeanjs/eslint-config": "1.2.5", "@types/lodash-es": "4.17.12", "@types/node": "20.11.30", "@types/nprogress": "0.2.3", "@types/qs": "6.9.13", "@unocss/eslint-config": "0.58.6", "@unocss/preset-icons": "0.58.6", "@unocss/preset-uno": "0.58.6", "@unocss/transformer-directives": "0.58.6", "@unocss/transformer-variant-group": "0.58.6", "@unocss/vite": "0.58.6", "@vitejs/plugin-vue": "5.0.4", "@vitejs/plugin-vue-jsx": "3.1.0", "@yc/scripts": "workspace:*", "@yc/uno-preset": "workspace:*", "cross-env": "7.0.3", "enquirer": "2.4.1", "eslint": "8.57.0", "eslint-plugin-vue": "9.23.0", "lint-staged": "15.2.2", "npm-run-all": "4.1.5", "sass": "1.72.0", "simple-git-hooks": "2.11.0", "tsx": "4.7.1", "typescript": "5.4.2", "unplugin-auto-import": "^19.1.1", "unplugin-icons": "0.18.5", "unplugin-vue-components": "0.26.0", "vite": "5.1.6", "vite-plugin-mcp": "^0.0.3", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.3.5", "vue-eslint-parser": "9.4.2", "vue-tsc": "2.0.6"}}